// backend/scheduler/savingsScheduler.js
// This script should be required in your main backend entry (e.g., index.js) to run automatically.

const cron = require('node-cron');
const SavingsPlan = require('../models/savingsPlan');
const User = require('../models/user');
const Notification = require('../models/notification');
const Transaction = require('../models/transaction');

// Helper to check if a deduction is due based on frequency and lastDepositDate
function isDue(plan, today) {
  if (!plan.lastDepositDate) return true; // First deduction
  const last = new Date(plan.lastDepositDate);
  if (isNaN(last.getTime())) {
    console.error(`[SavingsScheduler] Invalid lastDepositDate in isDue() for plan ${plan._id}:`, plan.lastDepositDate);
    return false; // Treat as not due, skip this plan
  }
  switch (plan.depositFrequency) {
    case 'Daily':
      return today > new Date(last.setDate(last.getDate() + 1));
    case 'Weekly':
      return today > new Date(last.setDate(last.getDate() + 7));
    case 'Monthly':
      return today > new Date(last.setMonth(last.getMonth() + 1));
    case 'Yearly':
      return today > new Date(last.setFullYear(last.getFullYear() + 1));
    default:
      return false;
  }
}

console.log('[SavingsScheduler] Module loaded. Waiting for cron schedule...');

// Defensive: Only schedule cron if system date is valid
const now = new Date();
console.log('[SavingsScheduler] System date at startup:', now, 'Valid:', !isNaN(now.getTime()));
if (!isNaN(now.getTime())) {
  // Run every day at 1:00am
  cron.schedule('0 1 * * *', async () => {
    const today = new Date();
    console.log('[SavingsScheduler] Cron job triggered at:', today);
    console.log('[SavingsScheduler] Debug Date (cron runtime):', today, 'Is valid?', !isNaN(today.getTime()));
    if (isNaN(today.getTime())) {
      console.error('[SavingsScheduler] System date is invalid at runtime. Skipping cron execution.');
      return;
    }
    console.log('[SavingsScheduler] Cron running at:', today, 'Valid:', !isNaN(today.getTime()));
    try {
      const plans = await SavingsPlan.find({});
      for (const plan of plans) {
        // Validate plan.targetDate
        if (!plan.targetDate || isNaN(new Date(plan.targetDate).getTime())) {
          console.error(`[SavingsScheduler] Invalid targetDate for plan ${plan._id}:`, plan.targetDate);
          continue;
        }
        // Validate plan.lastDepositDate if present
        if (plan.lastDepositDate && isNaN(new Date(plan.lastDepositDate).getTime())) {
          console.error(`[SavingsScheduler] Invalid lastDepositDate for plan ${plan._id}:`, plan.lastDepositDate);
          continue;
        }
        if (plan.savedAmount >= plan.targetAmount) continue; // Already reached target
        if (today > new Date(plan.targetDate)) continue; // Plan expired
        if (!isDue(plan, today)) continue; // Not due yet

        const user = await User.findById(plan.userId);
        if (!user) continue;
        if (user.balance >= plan.depositAmount) {
          user.balance -= plan.depositAmount;
          plan.savedAmount += plan.depositAmount;
          plan.lastDepositDate = today;
          await user.save();
          await plan.save();
          // Create a transaction record for this deduction
          await Transaction.create({
            date: today,
            description: `Auto-savings deduction for plan '${plan.title || plan._id}'`,
            type: 'deposit',
            amount: plan.depositAmount,
            userId: user._id,
            balanceAfter: user.balance,
            reference: `SAV-${plan._id}-${today.getTime()}`,
            createdAt: today,
          });
          // Notify user: savings due and successful
          await Notification.create({
            userId: user._id,
            type: 'savings_due',
            title: 'Savings Deducted',
            message: `₦${plan.depositAmount} was deducted for your savings plan '${plan.title || plan._id}'.`,
          });
        } else {
          // Notify user: savings failed due to insufficient funds
          await Notification.create({
            userId: user._id,
            type: 'savings_failed',
            title: 'Savings Deduction Failed',
            message: `We could not deduct ₦${plan.depositAmount} for your savings plan '${plan.title || plan._id}' due to insufficient balance.`,
          });
        }
      }
      console.log(`[SavingsScheduler] Processed savings plans at ${today}`);
    } catch (err) {
      console.error('[SavingsScheduler] Error processing savings plans:', err);
    }
  });
} else {
  console.error('[SavingsScheduler] System date is invalid. Cron job not scheduled.');
}

// Export for testing or manual run
module.exports = {};
