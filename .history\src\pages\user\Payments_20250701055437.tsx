import React, { useState, useEffect } from "react";
// Paystack inline script loader that returns a Promise when loaded
const loadPaystackScript = () => {
  return new Promise((resolve, reject) => {
    if (document.getElementById('paystack-inline-js')) {
      // If already loaded, resolve immediately
      if ((window as any).PaystackPop) {
        resolve(true);
      } else {
        // Wait for script to finish loading
        const script = document.getElementById('paystack-inline-js');
        script?.addEventListener('load', () => resolve(true));
        script?.addEventListener('error', () => reject('Paystack script failed to load'));
      }
      return;
    }
    const script = document.createElement('script');
    script.id = 'paystack-inline-js';
    script.src = 'https://js.paystack.co/v1/inline.js';
    script.async = true;
    script.onload = () => resolve(true);
    script.onerror = () => reject('Paystack script failed to load');
    document.body.appendChild(script);
  });
};
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
// import { PaymentProofUpload } from "@/components/payments/PaymentProofUpload";
import { WithdrawalRequestForm } from "@/components/payments/WithdrawalRequestForm";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { useBalance } from "@/hooks/use-balance";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { PayoutModeDialog } from "@/components/payments/PayoutModeDialog";
import { Wallet, Upload } from "lucide-react";


export default function Payments() {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null);
  const [depositAmount, setDepositAmount] = useState("");
  const [depositStep, setDepositStep] = useState<"amount" | "success">("amount");
  const [isPaying, setIsPaying] = useState(false);
  const [paystackUrl, setPaystackUrl] = useState("");
  const [activeTab, setActiveTab] = useState("deposit");
  const [transactions, setTransactions] = useState([]);
  const [showPayoutSettings, setShowPayoutSettings] = useState(false);
  const [showPaymentProofModal, setShowPaymentProofModal] = useState(false);
  const { balance, updateBalance } = useBalance();


  // Load transactions from backend
  useEffect(() => {
    const fetchTransactions = async () => {
      const stored = localStorage.getItem('auth_user');
      const user = stored ? JSON.parse(stored) : null;
      if (!user?._id && !user?.id) return;
      try {
        const res = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/transactions/user/${user._id || user.id}?limit=20`);
        const data = await res.json();
        if (data && Array.isArray(data.transactions)) {
          setTransactions(data.transactions);
        } else {
          setTransactions([]);
        }
      } catch (err) {
        setTransactions([]);
      }
    };
    fetchTransactions();
    loadPaystackScript();
    // Expose for manual refresh
    window.refreshUserTransactions = fetchTransactions;
  }, []);

  const handleDepositAmountSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const amount = parseFloat(depositAmount);
    if (isNaN(amount) || amount <= 0) {
      toast.error("Please enter a valid deposit amount");
      return;
    }
    setIsPaying(true);
    // Get user email from localStorage
    const stored = localStorage.getItem('auth_user');
    const user = stored ? JSON.parse(stored) : null;
    const email = user?.email;
    if (!email) {
      toast.error("User email not found. Please log in again.");
      setIsPaying(false);
      return;
    }
    try {
      await loadPaystackScript();
      const paystackPop = (window as any).PaystackPop;
      if (!paystackPop || typeof paystackPop.setup !== 'function') {
        toast.error("Payment system not available. Please try again.");
        setIsPaying(false);
        return;
      }
      // Send userId in metadata for Paystack
      const res = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/paystack/deposit/initialize`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount: Math.round(amount * 100),
          email,
          metadata: {
            userId: user?._id || user?.id || undefined
          }
        })
      });
      const data = await res.json();
      if (data.status && data.data?.reference) {
        const verifyAndUpdate = async (reference: string) => {
          try {
            const verifyRes = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/paystack/deposit/verify/${reference}`);
            const verifyData = await verifyRes.json();
            if (verifyData.status && verifyData.data?.status === 'success') {
              // Fetch latest balance from backend after payment
              try {
                const balanceRes = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/userinfo/balance/${user?._id || user?.id}`);
                const balanceData = await balanceRes.json();
                if (balanceData && typeof balanceData.balance !== 'undefined') {
                  if (typeof updateBalance === 'function' && updateBalance.length === 2) {
                    // @ts-ignore
                    await updateBalance(balanceData.balance, 'add');
                  } else {
                    console.log('[Payments] Would set user balance to:', balanceData.balance);
                  }
                  console.log('[Payments] Refreshed user balance:', balanceData.balance);
                } else {
                  console.warn('[Payments] No balance in response:', balanceData);
                }
              } catch (err) {
                console.error('[Payments] Error refreshing balance:', err);
              }
              // POST to /api/transactions/trans after successful deposit
              try {
                const transRes = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/transactions/trans`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    userId: user?._id || user?.id,
                    type: 'deposit',
                    amount: parseFloat(depositAmount),
                    description: 'Deposit via Paystack',
                    status: 'completed'
                  })
                });
                const transData = await transRes.json();
                console.log('[Payments] Transaction POST response:', transData);
                if (!transRes.ok) {
                  toast.error('Failed to log transaction: ' + (transData?.error || 'Unknown error'));
                }
              } catch (err) {
                console.error('[Payments] Error posting transaction:', err);
                toast.error('Error logging transaction');
              }
              setDepositStep('success');
              toast.success('Deposit successful!');
              if (typeof window.refreshUserTransactions === 'function') {
                window.refreshUserTransactions();
              }
            } else {
              toast.error(verifyData.message || 'Payment verification failed.');
            }
          } catch (err) {
            toast.error('Network error during payment verification.');
          }
          setIsPaying(false);
        };
        const handler = paystackPop.setup({
          key: import.meta.env.VITE_PAYSTACK_PUBLIC_KEY,
          email,
          amount: Math.round(amount * 100),
          currency: 'NGN',
          ref: data.data.reference,
          metadata: {
            userId: user?._id || user?.id || undefined
          },
          callback: function(response: any) {
            verifyAndUpdate(response.reference);
          },
          onClose: function() {
            setIsPaying(false);
            toast.info('Payment cancelled.');
          }
        });
        if (handler && typeof handler.openIframe === 'function') {
          handler.openIframe();
        } else {
          toast.error("Failed to open payment window. Please try again.");
          setIsPaying(false);
        }
      } else {
        toast.error(data.message || "Failed to initialize payment");
        setIsPaying(false);
      }
    } catch (err) {
      toast.error("Network error. Please try again.");
      setIsPaying(false);
    }
  };


  const handleWithdrawalSubmit = async (formData) => {
    // Get user info for withdrawal reference (optional)
    const stored = localStorage.getItem('auth_user');
    const user = stored ? JSON.parse(stored) : null;
    try {
      const res = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/paystack/transfer`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount: formData.amount,
          bank_code: formData.bankCode,
          account_number: formData.accountNumber,
          account_name: formData.accountName,
          user_id: user?._id || user?.id || undefined
        })
      });
      const data = await res.json();
      if (data.status) {
        toast.success('Withdrawal initiated!');
        setActiveTab('transactions');
        // POST to /api/transactions/trans after successful withdrawal
        try {
          const transRes = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/transactions/trans`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              userId: user?._id || user?.id,
              type: 'withdrawal',
              amount: formData.amount,
              description: 'Withdrawal via Paystack',
              status: 'completed'
            })
          });
          const transData = await transRes.json();
          console.log('[Payments] Withdrawal Transaction POST response:', transData);
          if (!transRes.ok) {
            toast.error('Failed to log withdrawal transaction: ' + (transData?.error || 'Unknown error'));
          }
        } catch (err) {
          console.error('[Payments] Error posting withdrawal transaction:', err);
          toast.error('Error logging withdrawal transaction');
        }
        // Refresh transactions after withdrawal
        try {
          const stored = localStorage.getItem('auth_user');
          const user = stored ? JSON.parse(stored) : null;
          if (user?._id || user?.id) {
            const res = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/transactions/user/${user._id || user.id}?limit=20`);
            const data = await res.json();
            if (data && Array.isArray(data.transactions)) {
              setTransactions(data.transactions);
            }
          }
        } catch (err) {}
      } else {
        toast.error(data.message || 'Withdrawal failed');
      }
    } catch (err) {
      toast.error('Network error. Please try again.');
    }
  };

  // Helper function to determine badge variant based on status
  const getStatusBadgeVariant = (status: string) => {
    if (status === "completed") return "success";
    if (status === "failed") return "destructive";
    return "outline"; // For "pending" status
  };

  const renderDepositStep = () => {
    switch (depositStep) {
      case "amount":
        return (
          <form onSubmit={handleDepositAmountSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="amount">Deposit Amount (₦)</Label>
              <Input
                id="amount"
                type="number"
                placeholder="Enter amount to deposit"
                value={depositAmount}
                onChange={(e) => setDepositAmount(e.target.value)}
                required
                disabled={isPaying}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isPaying}>
              {isPaying ? (
                <span className="flex items-center justify-center"><span className="loader mr-2"></span>Processing...</span>
              ) : (
                "Deposit"
              )}
            </Button>
            {isPaying && (
              <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg p-6 flex flex-col items-center shadow-lg">
                  <div className="loader mb-4"></div>
                  <div className="font-medium text-lg mb-2">Securely opening payment window…</div>
                  <div className="text-sm text-muted-foreground">Do not close or refresh this page.</div>
                </div>
              </div>
            )}
          </form>
        );
      case "success":
        return (
          <div className="space-y-4 text-center">
            <h3 className="text-lg font-medium text-green-600">Deposit Successful!</h3>
            <p>Your deposit has been received and your balance updated.</p>
            <Button className="w-full" onClick={() => { setDepositStep("amount"); setDepositAmount(""); }}>Make Another Deposit</Button>
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold">Payments</h1>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            className="flex items-center gap-2"
            onClick={() => setShowPaymentProofModal(true)}
          >
            <Upload className="h-4 w-4" />
            I've Made a Payment
          </Button>
          <Button 
            variant="outline" 
            className="flex items-center gap-2"
            onClick={() => setShowPayoutSettings(true)}
          >
            <Wallet className="h-4 w-4" />
            Payout Settings
          </Button>
        </div>
      </div>
      <Dialog open={showPayoutSettings} onOpenChange={setShowPayoutSettings}>
        <PayoutModeDialog onClose={() => setShowPayoutSettings(false)} />
      </Dialog>
      {/*
      <Dialog open={showPaymentProofModal} onOpenChange={setShowPaymentProofModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Submit Payment Proof</DialogTitle>
          </DialogHeader>
          <PaymentProofUpload 
            onSubmit={handleDirectPaymentProofSubmit}
            isLoading={false}
          />
        </DialogContent>
      </Dialog>
      */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="deposit">Deposit</TabsTrigger>
          <TabsTrigger value="withdraw">Withdraw</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
        </TabsList>
        <TabsContent value="deposit" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Make a Deposit</CardTitle>
              <CardDescription>
                Deposit funds into your savings account
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderDepositStep()}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="withdraw" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Request Withdrawal</CardTitle>
              <CardDescription>
                Withdraw funds from your savings account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <WithdrawalRequestForm onSubmit={handleWithdrawalSubmit} />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Transaction History</CardTitle>
              <CardDescription>
                View your recent deposit and withdrawal transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type</TableHead>
                    <TableHead>Amount (₦)</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.length > 0 ? (
                    transactions.map((transaction) => {
                      // Use status from transaction, fallback to Paystack status if available
                      let status = transaction.status;
                      if (!status && transaction.paystackStatus) status = transaction.paystackStatus;
                      if (!status && transaction.description && transaction.description.toLowerCase().includes('paystack')) status = 'completed';
                      return (
                        <TableRow key={transaction.id || transaction._id}>
                          <TableCell className="font-medium capitalize">{transaction.type}</TableCell>
                          <TableCell>{transaction.amount?.toLocaleString?.() ?? transaction.amount}</TableCell>
                          <TableCell>{transaction.date ? new Date(transaction.date).toLocaleString() : ''}</TableCell>
                          <TableCell>{transaction.reference}</TableCell>
                          <TableCell>
                            <Badge variant={getStatusBadgeVariant(status)}>
                              {status || 'completed'}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                        No transactions yet
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
