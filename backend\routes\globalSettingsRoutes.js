const express = require('express');
const router = express.Router();
const GlobalSettings = require('../models/globalSettings');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// GET /api/settings - Get all public settings (no auth required)
router.get('/', async (req, res) => {
  try {
    const settings = await GlobalSettings.getSettings();
    
    // Filter out sensitive data for public access
    const publicSettings = {
      appName: settings.appName,
      appDescription: settings.appDescription,
      appVersion: settings.appVersion,
      logo: settings.logo,
      favicon: settings.favicon,
      colors: settings.colors,
      content: settings.content,
      contact: {
        email: settings.contact.email,
        phone: settings.contact.phone,
        address: settings.contact.address,
        socialMedia: settings.contact.socialMedia
      }
    };
    
    res.json({
      success: true,
      data: publicSettings
    });
  } catch (error) {
    console.error('Error fetching public settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch settings'
    });
  }
});

// GET /api/settings/admin - Get all settings including sensitive data (admin only)
router.get('/admin', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const settings = await GlobalSettings.getSettings()
      .select('+apiKeys.paystack.publicKey +apiKeys.paystack.secretKey +apiKeys.aws.accessKeyId +apiKeys.aws.secretAccessKey');
    
    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Error fetching admin settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch admin settings'
    });
  }
});

// PUT /api/settings/app-config - Update app configuration
router.put('/app-config', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { appName, appDescription, appVersion } = req.body;
    
    const updates = {};
    if (appName !== undefined) updates.appName = appName;
    if (appDescription !== undefined) updates.appDescription = appDescription;
    if (appVersion !== undefined) updates.appVersion = appVersion;
    
    const settings = await GlobalSettings.updateSettings(updates, req.user.id);
    
    res.json({
      success: true,
      message: 'App configuration updated successfully',
      data: {
        appName: settings.appName,
        appDescription: settings.appDescription,
        appVersion: settings.appVersion
      }
    });
  } catch (error) {
    console.error('Error updating app config:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update app configuration'
    });
  }
});

// PUT /api/settings/colors - Update color theme
router.put('/colors', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { primary, secondary, accent, background } = req.body;
    
    const colorUpdates = {};
    if (primary) colorUpdates['colors.primary'] = primary;
    if (secondary) colorUpdates['colors.secondary'] = secondary;
    if (accent) colorUpdates['colors.accent'] = accent;
    if (background) colorUpdates['colors.background'] = background;
    
    const settings = await GlobalSettings.updateSettings(colorUpdates, req.user.id);
    
    res.json({
      success: true,
      message: 'Color theme updated successfully',
      data: settings.colors
    });
  } catch (error) {
    console.error('Error updating colors:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update color theme'
    });
  }
});

// PUT /api/settings/branding - Update branding (logo, favicon)
router.put('/branding', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { logoUrl, logoFilename, faviconUrl, faviconFilename } = req.body;
    
    const updates = {};
    if (logoUrl !== undefined) {
      updates['logo.url'] = logoUrl;
      updates['logo.filename'] = logoFilename;
      updates['logo.uploadedAt'] = new Date();
    }
    if (faviconUrl !== undefined) {
      updates['favicon.url'] = faviconUrl;
      updates['favicon.filename'] = faviconFilename;
    }
    
    const settings = await GlobalSettings.updateSettings(updates, req.user.id);
    
    res.json({
      success: true,
      message: 'Branding updated successfully',
      data: {
        logo: settings.logo,
        favicon: settings.favicon
      }
    });
  } catch (error) {
    console.error('Error updating branding:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update branding'
    });
  }
});

// PUT /api/settings/api-keys - Update API keys
router.put('/api-keys', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { paystack, aws } = req.body;
    
    const updates = {};
    if (paystack) {
      if (paystack.publicKey !== undefined) updates['apiKeys.paystack.publicKey'] = paystack.publicKey;
      if (paystack.secretKey !== undefined) updates['apiKeys.paystack.secretKey'] = paystack.secretKey;
      if (paystack.isLive !== undefined) updates['apiKeys.paystack.isLive'] = paystack.isLive;
    }
    if (aws) {
      if (aws.accessKeyId !== undefined) updates['apiKeys.aws.accessKeyId'] = aws.accessKeyId;
      if (aws.secretAccessKey !== undefined) updates['apiKeys.aws.secretAccessKey'] = aws.secretAccessKey;
      if (aws.region !== undefined) updates['apiKeys.aws.region'] = aws.region;
      if (aws.s3Bucket !== undefined) updates['apiKeys.aws.s3Bucket'] = aws.s3Bucket;
    }
    
    await GlobalSettings.updateSettings(updates, req.user.id);
    
    res.json({
      success: true,
      message: 'API keys updated successfully'
    });
  } catch (error) {
    console.error('Error updating API keys:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update API keys'
    });
  }
});

// PUT /api/settings/content - Update frontend content
router.put('/content', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { landingPage, aboutPage } = req.body;
    
    const updates = {};
    if (landingPage) {
      Object.keys(landingPage).forEach(key => {
        updates[`content.landingPage.${key}`] = landingPage[key];
      });
    }
    if (aboutPage) {
      Object.keys(aboutPage).forEach(key => {
        updates[`content.aboutPage.${key}`] = aboutPage[key];
      });
    }
    
    const settings = await GlobalSettings.updateSettings(updates, req.user.id);
    
    res.json({
      success: true,
      message: 'Content updated successfully',
      data: settings.content
    });
  } catch (error) {
    console.error('Error updating content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update content'
    });
  }
});

// PUT /api/settings/system - Update system configuration
router.put('/system', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const systemUpdates = {};
    const allowedFields = [
      'maintenanceMode', 'maintenanceMessage', 'allowRegistration',
      'emailVerificationRequired', 'kycRequired', 'minimumDeposit', 'maximumDeposit'
    ];
    
    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        systemUpdates[`system.${field}`] = req.body[field];
      }
    });
    
    const settings = await GlobalSettings.updateSettings(systemUpdates, req.user.id);
    
    res.json({
      success: true,
      message: 'System configuration updated successfully',
      data: settings.system
    });
  } catch (error) {
    console.error('Error updating system config:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update system configuration'
    });
  }
});

// PUT /api/settings/contact - Update contact information
router.put('/contact', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { email, phone, address, socialMedia } = req.body;
    
    const updates = {};
    if (email !== undefined) updates['contact.email'] = email;
    if (phone !== undefined) updates['contact.phone'] = phone;
    if (address !== undefined) updates['contact.address'] = address;
    if (socialMedia !== undefined) updates['contact.socialMedia'] = socialMedia;
    
    const settings = await GlobalSettings.updateSettings(updates, req.user.id);
    
    res.json({
      success: true,
      message: 'Contact information updated successfully',
      data: settings.contact
    });
  } catch (error) {
    console.error('Error updating contact info:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update contact information'
    });
  }
});

// GET /api/settings/history - Get settings change history (admin only)
router.get('/history', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const settings = await GlobalSettings.findOne()
      .populate('lastUpdatedBy', 'firstName lastName email')
      .select('version lastUpdatedBy updatedAt');
    
    res.json({
      success: true,
      data: {
        currentVersion: settings?.version || 1,
        lastUpdatedBy: settings?.lastUpdatedBy,
        lastUpdatedAt: settings?.updatedAt
      }
    });
  } catch (error) {
    console.error('Error fetching settings history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch settings history'
    });
  }
});

module.exports = router;
