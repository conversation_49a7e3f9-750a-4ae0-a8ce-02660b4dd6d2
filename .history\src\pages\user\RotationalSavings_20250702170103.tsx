import React, { useState, useEffect } from 'react';
import { GroupSavingsService } from '@/services/group-savings';
import { useAuth } from '@/hooks/use-auth';
import { useJoinRotationalGroup } from '@/hooks/use-group-savings';
import { useNavigate } from 'react-router-dom';
import RotationalGroupSavings from '@/components/savings/RotationalGroupSavings';
import GroupSavingsList from '@/components/savings/GroupSavingsList';
import CreateGroupSavingsForm from '@/components/savings/CreateGroupSavingsForm';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, Target, TrendingUp, PiggyBank } from 'lucide-react';
import { GroupSavingsPlan } from '@/types/group-savings';

export default function RotationalSavings() {
  // All hooks must be called inside the function body
  const { user, isLoading } = useAuth();
  const joinMutation = useJoinRotationalGroup();
  const navigate = useNavigate();

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<GroupSavingsPlan | null>(null);
  const [activeTab, setActiveTab] = useState('rotational');

  const handleCreateGroup = (group: any) => {
    console.log('Creating rotational group:', group);
  };

  const handleJoinGroup = (groupId: string) => {
    console.log('Joining group:', groupId);
  };

  const handleCreateGroupSavings = () => {
    setShowCreateForm(true);
  };

  const handleViewDetails = (plan: GroupSavingsPlan) => {
    setSelectedPlan(plan);
    console.log('View details for plan:', plan);
  };

  const handleFormSuccess = () => {
    setShowCreateForm(false);
    setActiveTab('group-savings'); // Switch to group savings tab after creation
  };

  const handleFormCancel = () => {
    setShowCreateForm(false);
  };

  if (showCreateForm) {
    return (
      <div className="container mx-auto px-4 py-6">
        <CreateGroupSavingsForm
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      </div>
    );
  }



  // User-specific dashboard stats
  const [userPlans, setUserPlans] = useState<GroupSavingsPlan[]>([]);
  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);
  useEffect(() => {
    setStatsLoading(true);
    GroupSavingsService.getGroupSavingsPlans().then(({ data, error }) => {
      if (error) {
        setStatsError('Failed to load your group savings');
        setUserPlans([]);
      } else {
        setUserPlans(data || []);
        setStatsError(null);
      }
      setStatsLoading(false);
    });
  }, []);

  // Calculate user-specific stats
  const totalGroupSavings = userPlans.reduce((sum, plan) => sum + (plan.savedAmount || 0), 0);
  const activeGroups = userPlans.length;
  // For demo: goals achieved = plans where savedAmount >= targetAmount
  const goalsAchieved = userPlans.filter(plan => (plan.savedAmount || 0) >= (plan.targetAmount || 0)).length;
  // For demo: average returns = average of all plan interest rates (if available)
  const avgReturns = userPlans.length > 0 ?
    Math.round(
      userPlans.reduce((sum, plan) => sum + (plan.interestRate || 0), 0) / userPlans.length
    ) : 0;

  // Show login prompt if not authenticated and pending join
  const pendingGroupId = typeof window !== 'undefined' ? localStorage.getItem('pendingJoinGroupId') : null;
  if (!user && pendingGroupId) {
    return (
      <div className="container mx-auto px-4 py-6 text-center">
        <h2 className="text-2xl font-bold mb-4">Join a Rotational Group</h2>
        <p className="mb-4">You need to log in to join the group. Please <a href="/login" className="text-blue-600 underline">log in</a> and you will be added automatically.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Group & Rotational Savings</h1>
        <p className="text-muted-foreground">
          Save together with friends and family, or join existing saving groups
        </p>
      </div>

      {/* Stats Overview (user-specific) */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Group Savings</CardTitle>
            <div className="p-2 bg-blue-100 rounded-lg">
              <PiggyBank className="h-5 w-5 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statsLoading ? <span className="animate-pulse">—</span> : `₦${totalGroupSavings.toLocaleString()}`}
            </div>
            <p className="text-xs text-green-600 font-medium">
              {/* Optionally, show change from last month if you have the data */}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Groups</CardTitle>
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="h-5 w-5 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statsLoading ? <span className="animate-pulse">—</span> : activeGroups}
            </div>
            <p className="text-xs text-green-600 font-medium">
              {/* Optionally, show change from last month if you have the data */}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Goals Achieved</CardTitle>
            <div className="p-2 bg-blue-100 rounded-lg">
              <Target className="h-5 w-5 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statsLoading ? <span className="animate-pulse">—</span> : goalsAchieved}
            </div>
            <p className="text-xs text-green-600 font-medium">
              {/* Optionally, show change from last month if you have the data */}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Returns</CardTitle>
            <div className="p-2 bg-blue-100 rounded-lg">
              <TrendingUp className="h-5 w-5 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statsLoading ? <span className="animate-pulse">—</span> : `${avgReturns}%`}
            </div>
            <p className="text-xs text-green-600 font-medium">
              {/* Optionally, show change from last month if you have the data */}
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="rotational" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Rotational Groups
          </TabsTrigger>
          <TabsTrigger value="group-savings" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Group Savings
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="rotational" className="mt-6">
          <RotationalGroupSavings
            onCreateGroup={handleCreateGroup}
            onJoinGroup={handleJoinGroup}
          />
        </TabsContent>
        
        <TabsContent value="group-savings" className="mt-6">
          <GroupSavingsList
            onCreateGroup={handleCreateGroupSavings}
            onViewDetails={handleViewDetails}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
