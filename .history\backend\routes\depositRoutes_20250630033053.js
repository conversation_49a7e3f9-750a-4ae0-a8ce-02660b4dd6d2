const express = require('express');
const axios = require('axios');
const Deposit = require('../models/deposit');
const User = require('../models/user');
const mongoose = require('mongoose');
const Notification = require('../models/notification');
const authMiddleware = require('../middleware/authMiddleware');

// Initialize Paystack payment
router.post('/initialize', authMiddleware, async (req, res) => {
  try {
    const { amount } = req.body;
    const userId = req.user && req.user.id;
    if (!userId) {
      return res.status(401).json({ error: 'User authentication failed. Please log in again.' });
    }
    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return res.status(400).json({ error: 'Amount must be a positive number.' });
    }
    // Get user email for Paystack
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    const paystackRes = await axios.post(
      `${process.env.PAYSTACK_BASE_URL}/transaction/initialize`,
      {
        email: user.email,
        amount: Math.round(amount * 100), // Paystack expects amount in kobo
        metadata: { userId }
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );
    const { reference, authorization_url } = paystackRes.data.data;
    // Create a pending deposit record
    const deposit = new Deposit({
      amount,
      paystackReference: reference,
      paystackStatus: 'pending',
      status: 'pending',
      userId,
    });
    await deposit.save();
    res.status(200).json({ authorization_url, reference });
  } catch (error) {
    console.error('Paystack initialize error:', error.response?.data || error.message);
    res.status(500).json({ error: 'Failed to initialize payment', details: error.message });
  }
});

// Paystack webhook for payment confirmation
router.post('/webhook', async (req, res) => {
  try {
    // Paystack sends events as JSON
    const event = req.body;
    if (event.event === 'charge.success') {
      const reference = event.data.reference;
      const deposit = await Deposit.findOne({ paystackReference: reference });
      if (deposit && deposit.status === 'pending') {
        deposit.status = 'success';
        deposit.paystackStatus = 'success';
        await deposit.save();
        // Update user balance
        const user = await User.findById(deposit.userId);
        if (user) {
          user.balance += deposit.amount;
          await user.save();
        }
        // Create transaction record
        const Transaction = require('../models/transaction');
        await Transaction.create({
          date: new Date(),
          description: 'Deposit via Paystack',
          type: 'deposit',
          amount: deposit.amount,
          userId: deposit.userId,
          balanceAfter: user ? user.balance : 0,
          reference,
        });
        // Notify user
        await Notification.create({
          userId: deposit.userId,
          type: 'deposit_approved',
          title: 'Deposit Successful',
          message: `Your deposit of ₦${deposit.amount} was successful.`,
        });
      }
    }
    res.sendStatus(200);
  } catch (error) {
    console.error('Paystack webhook error:', error.message);
    res.sendStatus(500);
  }
});

router.get('/all', async (req, res) => {
  try {
    const deposits = await Deposit.find().sort({ createdAt: -1 });
    res.json(deposits);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// [REMOVED] Manual deposit approval/rejection endpoint

// Get count of pending deposits for a specific user (for sidebar badge)
router.get('/user/:userId/pending-count', async (req, res) => {
  try {
    const { userId } = req.params;
    if (!userId) {
      return res.status(400).json({ error: 'userId parameter is required.' });
    }
    // status 'pending' means not yet confirmed by Paystack
    const count = await Deposit.countDocuments({ userId, status: 'pending' });
    res.status(200).json({ count });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch pending deposit count', details: error.message });
  }
});

// Admin: Get count of all pending deposits
router.get('/pending-count', async (req, res) => {
  try {
    const count = await Deposit.countDocuments({ status: 'pending' });
    res.json({ count });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch pending deposits count' });
  }
});

module.exports = router;
