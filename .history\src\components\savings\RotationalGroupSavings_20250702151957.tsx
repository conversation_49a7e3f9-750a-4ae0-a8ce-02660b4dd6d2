
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  Users, 
  Calendar, 
  DollarSign, 
  Clock, 
  Share2, 
  CheckCircle,
  UserPlus,
  ArrowRight,
  Plus,
  Eye,
  Copy
} from 'lucide-react';
import { toast } from 'sonner';
import { useRotationalGroups, useCreateRotationalGroup, useJoinRotationalGroup } from '@/hooks/use-group-savings';
import { CreateRotationalGroupInput, RotationalGroup } from '@/types/group-savings';

interface RotationalGroupSavingsProps {
  onCreateGroup?: (group: any) => void;
  onJoinGroup?: (groupId: string) => void;
}

export default function RotationalGroupSavings({ 
  onCreateGroup,
  onJoinGroup 
}: RotationalGroupSavingsProps) {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<RotationalGroup | null>(null);
  const [newGroup, setNewGroup] = useState<CreateRotationalGroupInput>({
    name: '',
    description: '',
    contribution_amount: 0,
    frequency: 'monthly',
    max_members: 5
  });

  const { data: groupsRaw = [], isLoading } = useRotationalGroups();
  // Normalize backend data to match expected frontend structure
  const groups = Array.isArray(groupsRaw)
    ? (groupsRaw as any[]).map((g) => ({
        ...g,
        current_members: g.members || g.current_members || [],
        contribution_amount: g.amountPerInterval || g.contribution_amount,
        frequency: g.intervalType || g.frequency,
        max_members: g.max_members || (g.members ? g.members.length : 0),
        status: g.status || (g.isActive === false ? 'completed' : 'recruiting'),
        total_cycles: g.total_cycles || g.max_members || (g.members ? g.members.length : 0),
        current_cycle: g.current_cycle || g.currentCycle || 1,
        invite_link: g.invite_link || '',
        next_withdrawal: g.next_withdrawal || null,
      }))
    : [];
  const createGroupMutation = useCreateRotationalGroup();
  const joinGroupMutation = useJoinRotationalGroup();
  const handleCreateGroup = async () => {
    if (!newGroup.name || !newGroup.contribution_amount) {
      toast.error('Please fill in all required fields');
      return;
    }
    try {
      const createdBy = 'currentUser';
      const nextPayoutDate = new Date().toISOString();
      console.log('[RotationalGroup] Creating group, payload:', { ...newGroup, createdBy, nextPayoutDate });
      const result = await createGroupMutation.mutateAsync({ ...newGroup, createdBy, nextPayoutDate });
      console.log('[RotationalGroup] Backend create result:', result);
      onCreateGroup?.(newGroup);
      setNewGroup({
        name: '',
        description: '',
        contribution_amount: 0,
        frequency: 'monthly',
        max_members: 5
      });
      setShowCreateForm(false);
    } catch (error) {
      console.error('[RotationalGroup] Error creating group:', error);
    }
  };

  const copyInviteLink = (link: string) => {
    navigator.clipboard.writeText(link);
    toast.success('Invite link copied to clipboard!');
  };

  const handleJoinGroup = async (groupId: string) => {
    try {
      const userId = 'currentUser';
      console.log('[RotationalGroup] Joining group', groupId, 'as user', userId);
      const result = await joinGroupMutation.mutateAsync({ groupId, userId });
      console.log('[RotationalGroup] Backend join result:', result);
      onJoinGroup?.(groupId);
    } catch (error) {
      console.error('[RotationalGroup] Error joining group:', error);
    }
  };

  const viewGroupDetails = (group: RotationalGroup) => {
    setSelectedGroup(group);
    setShowDetailsModal(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'recruiting': return 'bg-yellow-500';
      case 'active': return 'bg-green-500';
      case 'completed': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const calculateProgress = (group: RotationalGroup) => {
    return (group.current_cycle / group.total_cycles) * 100;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Group Details Modal */}
      <Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              {selectedGroup?.name}
            </DialogTitle>
          </DialogHeader>
          {selectedGroup && (
            <div className="space-y-4">
              <p className="text-muted-foreground">{selectedGroup.description}</p>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span>₦{selectedGroup.contribution_amount.toLocaleString()}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="capitalize">{selectedGroup.frequency}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span>{selectedGroup.current_members.length}/{selectedGroup.max_members}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>Cycle {selectedGroup.current_cycle}/{selectedGroup.total_cycles}</span>
                </div>
              </div>

              {selectedGroup.status !== 'recruiting' && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{Math.round(calculateProgress(selectedGroup))}%</span>
                  </div>
                  <Progress value={calculateProgress(selectedGroup)} className="h-2" />
                </div>
              )}

              <div className="space-y-2">
                <p className="text-sm font-medium">Members:</p>
                <div className="space-y-2">
                  {selectedGroup.current_members.map((member, index) => (
                    <div key={member.id} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={member.avatar_url} />
                          <AvatarFallback>
                            {member.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm">{member.name}</span>
                      </div>
                      <Badge variant={member.has_withdrawn ? "default" : "secondary"}>
                        {member.has_withdrawn ? 'Received' : `Turn ${member.withdrawal_order}`}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              {selectedGroup.status === 'recruiting' && (
                <Button
                  onClick={() => copyInviteLink(selectedGroup.invite_link)}
                  className="w-full flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Copy Invite Link
                </Button>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Rotational Savings Groups</h2>
          <p className="text-muted-foreground">Save together, take turns receiving the pot</p>
        </div>
        <Button onClick={() => setShowCreateForm(true)} className="flex items-center gap-2">
          <UserPlus className="h-4 w-4" />
          Create Group
        </Button>
      </div>

      {showCreateForm && (
        <Card className="w-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Create Rotational Savings Group
            </CardTitle>
            <CardDescription>
              Set up a group where members take turns receiving the total contribution amount
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="groupName">Group Name *</Label>
              <Input
                id="groupName"
                value={newGroup.name}
                onChange={(e) => setNewGroup({ ...newGroup, name: e.target.value })}
                placeholder="e.g., Office Colleagues Savings"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={newGroup.description}
                onChange={(e) => setNewGroup({ ...newGroup, description: e.target.value })}
                placeholder="What is this group saving for?"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contributionAmount">Contribution Amount (₦) *</Label>
                <Input
                  id="contributionAmount"
                  type="number"
                  value={newGroup.contribution_amount || ''}
                  onChange={(e) => setNewGroup({ ...newGroup, contribution_amount: parseInt(e.target.value) || 0 })}
                  placeholder="50000"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="frequency">Contribution Frequency *</Label>
                <select
                  id="frequency"
                  value={newGroup.frequency}
                  onChange={(e) => setNewGroup({ ...newGroup, frequency: e.target.value as 'weekly' | 'monthly' })}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                >
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxMembers">Maximum Members *</Label>
              <Input
                id="maxMembers"
                type="number"
                value={newGroup.max_members}
                onChange={(e) => setNewGroup({ ...newGroup, max_members: parseInt(e.target.value) || 5 })}
                placeholder="5"
                min="3"
                max="20"
              />
              <p className="text-sm text-muted-foreground">
                Each member will receive ₦{(newGroup.contribution_amount * newGroup.max_members).toLocaleString()}{' '}
                when it's their turn
              </p>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg space-y-2">
              <h4 className="font-medium text-blue-900">How it works:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• All members contribute the same amount {newGroup.frequency}</li>
                <li>• Each {newGroup.frequency}, one member receives the total pot</li>
                <li>• The withdrawal order is determined when the group starts</li>
                <li>• Everyone gets their turn to receive the full amount</li>
              </ul>
            </div>

            <div className="flex gap-3">
              <Button 
                onClick={handleCreateGroup} 
                className="flex-1"
                disabled={createGroupMutation.isPending}
              >
                {createGroupMutation.isPending ? 'Creating...' : 'Create Group'}
              </Button>
              <Button variant="outline" onClick={() => setShowCreateForm(false)} className="flex-1">
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {groups.map((group) => (
          <Card key={group.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg">{group.name}</CardTitle>
                <Badge className={getStatusColor(group.status)}>
                  {group.status.charAt(0).toUpperCase() + group.status.slice(1)}
                </Badge>
              </div>
              <CardDescription className="line-clamp-2">{group.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span>₦{group.contribution_amount.toLocaleString()}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="capitalize">{group.frequency}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span>{group.current_members.length}/{group.max_members}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>Cycle {group.current_cycle}/{group.total_cycles}</span>
                </div>
              </div>

              {group.status !== 'recruiting' && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{Math.round(calculateProgress(group))}%</span>
                  </div>
                  <Progress value={calculateProgress(group)} className="h-2" />
                </div>
              )}

              {group.next_withdrawal && (
                <div className="bg-green-50 p-3 rounded-lg">
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-green-800">
                      Next withdrawal: {group.next_withdrawal.date}
                    </span>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <p className="text-sm font-medium">Members:</p>
                <div className="flex -space-x-2">
                  {group.current_members.slice(0, 4).map((member, index) => (
                    <Avatar key={member.id} className="border-2 border-background h-8 w-8">
                      <AvatarImage src={member.avatar_url} />
                      <AvatarFallback className="text-xs">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                  ))}
                  {group.current_members.length > 4 && (
                    <div className="h-8 w-8 rounded-full bg-muted border-2 border-background flex items-center justify-center text-xs">
                      +{group.current_members.length - 4}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex gap-2">
                {group.status === 'recruiting' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleJoinGroup(group.id)}
                    className="flex-1"
                  >
                    <UserPlus className="h-4 w-4 mr-1" />
                    Join Group
                  </Button>
                )}
                
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => viewGroupDetails(group)}
                  className="flex-1"
                >
                  <Eye className="h-4 w-4 mr-1" />
                  View Details
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {groups.length === 0 && (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No rotational groups yet</h3>
          <p className="text-muted-foreground mb-4">
            Create your first rotational savings group and invite friends to join
          </p>
          <Button onClick={() => setShowCreateForm(true)}>
            <UserPlus className="h-4 w-4 mr-2" />
            Create Your First Group
          </Button>
        </div>
      )}
    </div>
  );
}
