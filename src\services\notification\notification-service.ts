
import { toast } from 'sonner';
import { NotificationType, NotificationChannel } from '@/hooks/use-notifications';

// Default notification data interface
export interface NotificationData {
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  channel: NotificationChannel;
  priority?: 'low' | 'medium' | 'high';
  actionUrl?: string;
  metadata?: Record<string, any>;
}

// Simple in-app notification service
export const sendNotification = async (data: NotificationData) => {
  try {
    // Show toast notification
    toast[data.type](data.title, {
      description: data.message,
    });

    return { success: true };
  } catch (error) {
    console.error('Error sending notification:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

// Service factory function for compatibility
export const initNotificationService = () => {
  return {
    sendNotification: async (data: NotificationData) => {
      return await sendNotification(data);
    }
  };
};
