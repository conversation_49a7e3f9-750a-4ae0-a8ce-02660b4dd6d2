
export interface GroupSavingsPlan {
  id: string;
  name: string;
  description: string;
  category: 'electronics' | 'car' | 'phone' | 'grocery' | 'other';
  target_amount: number;
  contribution_amount: number;
  start_date: string;
  end_date: string;
  status: 'active' | 'completed' | 'pending' | 'recruiting';
  max_members: number;
  current_members: number;
  created_at: string;
  created_by: string;
  interest_rate?: number;
  auto_deduct?: boolean;
  metadata?: Record<string, any>;
}

export interface GroupMember {
  id: string;
  user_id: string;
  group_id: string;
  name: string;
  email: string;
  avatar_url?: string;
  contribution_amount: number;
  total_contributed: number;
  joined_at: string;
  role: 'admin' | 'member';
  status: 'active' | 'inactive' | 'pending';
  withdrawal_order?: number;
  has_withdrawn?: boolean;
}

export interface RotationalGroup {
  id: string;
  name: string;
  description: string;
  creator_id: string;
  contribution_amount: number;
  frequency: 'weekly' | 'monthly';
  max_members: number;
  current_members: GroupMember[];
  status: 'recruiting' | 'active' | 'completed';
  start_date?: string;
  next_withdrawal?: {
    member_id: string;
    date: string;
    amount: number;
  };
  invite_link: string;
  total_cycles: number;
  current_cycle: number;
  created_at: string;
}

export interface GroupTransaction {
  id: string;
  group_id: string;
  member_id: string;
  type: 'contribution' | 'withdrawal' | 'penalty' | 'bonus';
  amount: number;
  description: string;
  date: string;
  status: 'pending' | 'completed' | 'failed';
}

export interface CreateGroupSavingsInput {
  name: string;
  description: string;
  category: string;
  target_amount: number;
  contribution_amount: number;
  start_date: string;
  end_date: string;
  max_members: number;
  auto_deduct?: boolean;
  interest_rate?: number;
}

export interface CreateRotationalGroupInput {
  name: string;
  description: string;
  contribution_amount: number;
  frequency: 'weekly' | 'monthly';
  max_members: number;
}
