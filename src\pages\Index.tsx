import { But<PERSON> } from "@/components/ui/button";
import {
  ArrowRight,
  Check,
  Shield,
  Star,
  Zap,
  PiggyBank,
  CreditCard,
  BarChart,
  Users,
  Moon,
  Sun,
  Smartphone,
  Globe,
  TrendingUp,
  Award,
  Play,
  ChevronRight,
  Sparkles,
  Target,
  DollarSign
} from "lucide-react";
import { Link } from "react-router-dom";
import { useTheme } from "@/hooks/use-theme";

const Index = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-background via-background to-brand-lightGreen/5">
      {/* Navigation */}
      <header className="border-b border-border/50 bg-background/95 backdrop-blur-xl sticky top-0 z-50" data-aos="fade-down">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-2xl overflow-hidden bg-gradient-to-br from-brand-green to-brand-emerald p-0.5">
              <div className="h-full w-full rounded-2xl bg-background flex items-center justify-center">
                <img
                  src="/lovable-uploads/96b8da77-302c-4b65-87fb-ec3cf6bc86ca.png"
                  alt="Koja Logo"
                  className="h-6 w-6 object-cover"
                />
              </div>
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-brand-green to-brand-emerald bg-clip-text text-transparent">ASUSU BY KOJA</span>
          </div>
          <nav className="hidden md:flex gap-8">
            <a href="#features" className="text-sm font-medium text-muted-foreground hover:text-brand-green transition-all duration-300 relative group">
              Features
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-green transition-all duration-300 group-hover:w-full"></span>
            </a>
            <a href="#how-it-works" className="text-sm font-medium text-muted-foreground hover:text-brand-green transition-all duration-300 relative group">
              How it Works
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-green transition-all duration-300 group-hover:w-full"></span>
            </a>
            <a href="#testimonials" className="text-sm font-medium text-muted-foreground hover:text-brand-green transition-all duration-300 relative group">
              Testimonials
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-green transition-all duration-300 group-hover:w-full"></span>
            </a>
            <a href="#contact" className="text-sm font-medium text-muted-foreground hover:text-brand-green transition-all duration-300 relative group">
              Contact
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-green transition-all duration-300 group-hover:w-full"></span>
            </a>
          </nav>
          <div className="flex items-center gap-4">
            <button
              onClick={toggleTheme}
              className="p-2.5 rounded-xl hover:bg-muted/50 transition-all duration-300 border border-border/50"
              aria-label="Toggle theme"
            >
              {theme === 'dark' ? (
                <Sun className="h-4 w-4 text-brand-green" />
              ) : (
                <Moon className="h-4 w-4 text-brand-green" />
              )}
            </button>
            <div className="hidden md:flex gap-3">
              <Link to="/login">
                <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-brand-green hover:bg-brand-green/5">
                  Sign In
                </Button>
              </Link>
              <Link to="/signup">
                <Button size="sm" className="bg-gradient-to-r from-brand-green to-brand-emerald hover:from-brand-green/90 hover:to-brand-emerald/90 text-white shadow-lg shadow-brand-green/25 border-0">
                  Get Started
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative overflow-hidden py-24 md:py-32 lg:py-40">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-brand-green/5 via-transparent to-brand-emerald/5"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-brand-green/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-brand-emerald/10 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="flex flex-col lg:flex-row items-center gap-16">
            {/* Left Content */}
            <div className="lg:w-1/2 space-y-8" data-aos="fade-right" data-aos-delay="100">
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-brand-green/10 to-brand-emerald/10 border border-brand-green/20 rounded-full">
                <Sparkles className="h-4 w-4 text-brand-green" />
                <span className="text-sm font-medium text-brand-green">Modern Nigerian Digital Savings</span>
              </div>

              <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold leading-[1.1] tracking-tight">
                <span className="bg-gradient-to-r from-brand-green to-brand-emerald bg-clip-text text-transparent">ASUSU</span>
                <br />
                <span className="text-foreground">by Koja</span>
                <br />
                <span className="text-2xl md:text-3xl lg:text-4xl font-medium text-muted-foreground">
                  Digital savings for Nigeria's future
                </span>
              </h1>

              <p className="text-xl text-muted-foreground max-w-lg leading-relaxed">
                Transform traditional Nigerian kolo savings with cutting-edge technology. Save smarter, grow faster, and achieve your financial dreams with confidence.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 pt-6">
                <Link to="/signup">
                  <Button size="lg" className="bg-gradient-to-r from-brand-green to-brand-emerald hover:from-brand-green/90 hover:to-brand-emerald/90 text-white shadow-xl shadow-brand-green/25 border-0 h-14 px-8 text-base font-semibold">
                    Start Saving Today
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Button size="lg" variant="outline" className="border-brand-green/20 text-brand-green hover:bg-brand-green/5 h-14 px-8 text-base font-semibold group">
                  <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                  Watch Demo
                </Button>
              </div>

              {/* Stats */}
              <div className="flex items-center gap-8 pt-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-brand-green">10K+</div>
                  <div className="text-sm text-muted-foreground">Active Savers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-brand-green">₦2.5B+</div>
                  <div className="text-sm text-muted-foreground">Total Saved</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-brand-green">99.9%</div>
                  <div className="text-sm text-muted-foreground">Uptime</div>
                </div>
              </div>
            </div>

            {/* Right Content - App Preview */}
            <div className="lg:w-1/2 relative" data-aos="fade-left" data-aos-delay="200">
              <div className="relative">
                {/* Floating Elements */}
                <div className="absolute -top-8 -left-8 w-24 h-24 bg-gradient-to-br from-brand-green to-brand-emerald rounded-2xl shadow-xl flex items-center justify-center animate-pulse">
                  <PiggyBank className="h-12 w-12 text-white" />
                </div>
                <div className="absolute -bottom-8 -right-8 w-20 h-20 bg-gradient-to-br from-brand-emerald to-brand-green rounded-xl shadow-lg flex items-center justify-center animate-bounce">
                  <TrendingUp className="h-10 w-10 text-white" />
                </div>

                {/* Main Phone Mockup */}
                <div className="relative mx-auto w-80 h-[600px] bg-gradient-to-br from-gray-900 to-gray-800 rounded-[3rem] p-2 shadow-2xl">
                  <div className="w-full h-full bg-gradient-to-br from-background to-brand-lightGreen/5 rounded-[2.5rem] overflow-hidden">
                    {/* Phone Screen Content */}
                    <div className="p-6 space-y-6">
                      {/* Status Bar */}
                      <div className="flex justify-between items-center text-xs text-muted-foreground">
                        <span>9:41</span>
                        <span>●●●●●</span>
                        <span>100%</span>
                      </div>

                      {/* App Header */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold text-lg">Good morning, Adebayo</h3>
                          <p className="text-sm text-muted-foreground">Your savings are growing!</p>
                        </div>
                        <div className="w-10 h-10 bg-gradient-to-br from-brand-green to-brand-emerald rounded-full"></div>
                      </div>

                      {/* Balance Card */}
                      <div className="bg-gradient-to-br from-brand-green to-brand-emerald rounded-2xl p-6 text-white">
                        <p className="text-sm opacity-90">Total Savings</p>
                        <p className="text-3xl font-bold">₦125,000</p>
                        <p className="text-sm opacity-90">+12% this month</p>
                      </div>

                      {/* Quick Actions */}
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-card border border-border rounded-xl p-4 text-center">
                          <Target className="h-6 w-6 text-brand-green mx-auto mb-2" />
                          <p className="text-sm font-medium">Save Now</p>
                        </div>
                        <div className="bg-card border border-border rounded-xl p-4 text-center">
                          <DollarSign className="h-6 w-6 text-brand-green mx-auto mb-2" />
                          <p className="text-sm font-medium">Withdraw</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24 bg-gradient-to-b from-background to-brand-lightGreen/5">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-20" data-aos="fade-up">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-brand-green/10 to-brand-emerald/10 border border-brand-green/20 rounded-full mb-6">
              <Award className="h-4 w-4 text-brand-green" />
              <span className="text-sm font-medium text-brand-green">Why Choose ASUSU</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
              Built for modern Nigeria
            </h2>
            <p className="text-xl text-muted-foreground leading-relaxed">
              Experience the perfect blend of traditional Nigerian savings culture and cutting-edge financial technology
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
            {[
              {
                icon: <Zap className="h-8 w-8" />,
                title: "Lightning Fast Savings",
                description: "Make daily contributions effortlessly from your mobile phone, anytime and anywhere with instant processing.",
                gradient: "from-brand-green to-brand-emerald"
              },
              {
                icon: <Shield className="h-8 w-8" />,
                title: "Bank-Level Security",
                description: "Your money is protected with military-grade encryption and all transactions are fully transparent and auditable.",
                gradient: "from-brand-emerald to-brand-green"
              },
              {
                icon: <Target className="h-8 w-8" />,
                title: "Smart Goals",
                description: "Set personalized savings goals with AI-powered insights and track your progress with beautiful visualizations.",
                gradient: "from-brand-green to-brand-emerald"
              },
              {
                icon: <Smartphone className="h-8 w-8" />,
                title: "Mobile First",
                description: "Designed for mobile-first experience with intuitive interface that makes saving as easy as sending a text.",
                gradient: "from-brand-emerald to-brand-green"
              },
              {
                icon: <Globe className="h-8 w-8" />,
                title: "Community Driven",
                description: "Join thousands of Nigerians building wealth together through our trusted community savings platform.",
                gradient: "from-brand-green to-brand-emerald"
              },
              {
                icon: <TrendingUp className="h-8 w-8" />,
                title: "Growth Analytics",
                description: "Track your financial growth with detailed analytics and insights to optimize your savings strategy.",
                gradient: "from-brand-emerald to-brand-green"
              },
            ].map((feature, index) => (
              <div
                key={index}
                className="group relative bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 hover:shadow-xl hover:shadow-brand-green/10 transition-all duration-500 hover:-translate-y-2"
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-500`}></div>

                {/* Icon */}
                <div className={`relative mb-6 w-16 h-16 bg-gradient-to-br ${feature.gradient} rounded-2xl flex items-center justify-center text-white shadow-lg shadow-brand-green/25`}>
                  {feature.icon}
                </div>

                {/* Content */}
                <div className="relative">
                  <h3 className="text-xl font-bold mb-4 group-hover:text-brand-green transition-colors duration-300">{feature.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">{feature.description}</p>
                </div>

                {/* Hover Arrow */}
                <div className="absolute top-8 right-8 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
                  <ChevronRight className="h-5 w-5 text-brand-green" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How it works */}
      <section id="how-it-works" className="py-24 bg-gradient-to-b from-brand-lightGreen/5 to-background">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-20" data-aos="fade-up">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-brand-green/10 to-brand-emerald/10 border border-brand-green/20 rounded-full mb-6">
              <Play className="h-4 w-4 text-brand-green" />
              <span className="text-sm font-medium text-brand-green">Simple Process</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
              Start saving in 4 easy steps
            </h2>
            <p className="text-xl text-muted-foreground leading-relaxed">
              Join thousands of Nigerians who have transformed their financial future with our simple, secure platform
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "Create Account",
                description: "Sign up for free and verify your identity in under 2 minutes",
                icon: <Users className="h-6 w-6" />
              },
              {
                step: "02",
                title: "Set Your Goals",
                description: "Define what you're saving for and set your target amount with smart recommendations",
                icon: <Target className="h-6 w-6" />
              },
              {
                step: "03",
                title: "Start Saving",
                description: "Contribute as little as ₦500 daily with automated savings and flexible schedules",
                icon: <CreditCard className="h-6 w-6" />
              },
              {
                step: "04",
                title: "Achieve Goals",
                description: "Watch your money grow and withdraw when you reach your financial milestones",
                icon: <Award className="h-6 w-6" />
              },
            ].map((step, index) => (
              <div
                key={index}
                className="relative group"
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                {/* Connection Line */}
                {index < 3 && (
                  <div className="hidden lg:block absolute top-12 -right-4 w-8 h-0.5 bg-gradient-to-r from-brand-green to-brand-emerald opacity-30"></div>
                )}

                {/* Step Card */}
                <div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 hover:shadow-xl hover:shadow-brand-green/10 transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                  {/* Background Gradient */}
                  <div className="absolute inset-0 bg-gradient-to-br from-brand-green/5 to-brand-emerald/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  {/* Step Number */}
                  <div className="relative mb-6">
                    <div className="text-6xl font-bold bg-gradient-to-r from-brand-green/20 to-brand-emerald/20 bg-clip-text text-transparent">
                      {step.step}
                    </div>
                    <div className="absolute top-2 right-0 w-12 h-12 bg-gradient-to-br from-brand-green to-brand-emerald rounded-xl flex items-center justify-center text-white shadow-lg shadow-brand-green/25">
                      {step.icon}
                    </div>
                  </div>

                  {/* Content */}
                  <div className="relative">
                    <h3 className="text-xl font-bold mb-4 group-hover:text-brand-green transition-colors duration-300">{step.title}</h3>
                    <p className="text-muted-foreground leading-relaxed">{step.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-16 text-center" data-aos="fade-up" data-aos-delay="400">
            <Link to="/signup">
              <Button size="lg" className="bg-gradient-to-r from-brand-green to-brand-emerald hover:from-brand-green/90 hover:to-brand-emerald/90 text-white shadow-xl shadow-brand-green/25 border-0 h-14 px-8 text-base font-semibold">
                Start Your Journey Today
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonials" className="py-24 bg-gradient-to-b from-background to-brand-lightGreen/5">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-20" data-aos="fade-up">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-brand-green/10 to-brand-emerald/10 border border-brand-green/20 rounded-full mb-6">
              <Star className="h-4 w-4 text-brand-green" />
              <span className="text-sm font-medium text-brand-green">Success Stories</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
              Trusted by thousands
            </h2>
            <p className="text-xl text-muted-foreground leading-relaxed">
              Join the growing community of Nigerians who have transformed their financial future with ASUSU
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
            {[
              {
                name: "Chioma Okafor",
                role: "Small Business Owner",
                location: "Lagos",
                quote: "ASUSU helped me save ₦2.5M to expand my business. The daily contribution system works perfectly with my income flow. I've never been more confident about my financial future.",
                rating: 5,
                savings: "₦2.5M saved"
              },
              {
                name: "Emmanuel Adeyemi",
                role: "Teacher",
                location: "Abuja",
                quote: "I've been able to save for my children's education without stress. The app reminds me to make my daily contributions and the progress tracking keeps me motivated.",
                rating: 5,
                savings: "₦850K saved"
              },
              {
                name: "Ngozi Eze",
                role: "IT Professional",
                location: "Port Harcourt",
                quote: "The transparency is what I love most. I can see exactly where my money is going and how it's growing over time. Best financial decision I've made!",
                rating: 5,
                savings: "₦1.2M saved"
              },
            ].map((testimonial, index) => (
              <div
                key={index}
                className="group relative bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 hover:shadow-xl hover:shadow-brand-green/10 transition-all duration-500 hover:-translate-y-2"
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                {/* Background Gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-brand-green/5 to-brand-emerald/5 opacity-0 group-hover:opacity-100 rounded-2xl transition-opacity duration-500"></div>

                {/* Content */}
                <div className="relative">
                  {/* Rating */}
                  <div className="flex items-center gap-1 mb-6">
                    {Array(testimonial.rating)
                      .fill(0)
                      .map((_, i) => (
                        <Star
                          key={i}
                          className="h-5 w-5 fill-brand-green text-brand-green"
                        />
                      ))}
                  </div>

                  {/* Quote */}
                  <blockquote className="text-lg text-muted-foreground mb-6 leading-relaxed italic">
                    "{testimonial.quote}"
                  </blockquote>

                  {/* Author Info */}
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-bold text-foreground">{testimonial.name}</p>
                      <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                      <p className="text-xs text-muted-foreground">{testimonial.location}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-semibold text-brand-green">{testimonial.savings}</div>
                      <div className="text-xs text-muted-foreground">Total Saved</div>
                    </div>
                  </div>
                </div>

                {/* Quote Icon */}
                <div className="absolute top-6 right-6 w-8 h-8 bg-gradient-to-br from-brand-green/20 to-brand-emerald/20 rounded-full flex items-center justify-center">
                  <span className="text-brand-green text-2xl font-serif">"</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-24 overflow-hidden" data-aos="fade-up">
        {/* Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-brand-green via-brand-emerald to-brand-green"></div>
        <div className="absolute inset-0 bg-white/5 backdrop-blur-sm"></div>

        {/* Floating Elements */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 border border-white/20 rounded-full mb-8">
              <Sparkles className="h-4 w-4 text-white" />
              <span className="text-sm font-medium text-white">Start Today</span>
            </div>

            <h2 className="text-4xl md:text-6xl font-bold mb-6 text-white leading-tight">
              Ready to transform your
              <br />
              <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">financial future?</span>
            </h2>

            <p className="text-xl text-white/90 mb-10 max-w-2xl mx-auto leading-relaxed">
              Join over 10,000 Nigerians who are building wealth with ASUSU. Start your savings journey today with as little as ₦500.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link to="/signup">
                <Button
                  size="lg"
                  className="bg-white text-brand-green hover:bg-white/90 shadow-xl shadow-black/20 border-0 h-14 px-8 text-base font-semibold"
                >
                  Create Your Free Account
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Button
                size="lg"
                variant="outline"
                className="border-white/30 text-white hover:bg-white/10 h-14 px-8 text-base font-semibold"
              >
                <Play className="mr-2 h-5 w-5" />
                Watch Demo
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="flex items-center justify-center gap-8 mt-12 text-white/80">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                <span className="text-sm">Bank-level Security</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                <span className="text-sm">10K+ Active Users</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-5 w-5 fill-current" />
                <span className="text-sm">4.9/5 Rating</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-16 bg-gradient-to-b from-slate-900 to-black text-white" data-aos="fade-up">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-12">
            <div className="md:col-span-2" data-aos="fade-up" data-aos-delay="100">
              <div className="flex items-center gap-3 mb-6">
                <div className="h-10 w-10 rounded-2xl overflow-hidden bg-gradient-to-br from-brand-green to-brand-emerald p-0.5">
                  <div className="h-full w-full rounded-2xl bg-slate-900 flex items-center justify-center">
                    <img
                      src="/lovable-uploads/96b8da77-302c-4b65-87fb-ec3cf6bc86ca.png"
                      alt="Koja Logo"
                      className="h-6 w-6 object-cover"
                    />
                  </div>
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-brand-green to-brand-emerald bg-clip-text text-transparent">ASUSU BY KOJA</span>
              </div>
              <p className="text-slate-400 text-lg leading-relaxed mb-6 max-w-md">
                Transforming traditional Nigerian kolo savings with cutting-edge technology. Building financial futures, one naira at a time.
              </p>
              <div className="flex items-center gap-4">
                <div className="text-sm text-slate-500">
                  <span className="text-brand-green font-semibold">10K+</span> Active Savers
                </div>
                <div className="text-sm text-slate-500">
                  <span className="text-brand-green font-semibold">₦2.5B+</span> Total Saved
                </div>
              </div>
            </div>

            <div data-aos="fade-up" data-aos-delay="200">
              <h3 className="text-lg font-bold mb-6 text-white">Company</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-slate-400 hover:text-brand-green transition-colors duration-300">About Us</a></li>
                <li><a href="#" className="text-slate-400 hover:text-brand-green transition-colors duration-300">Careers</a></li>
                <li><a href="#" className="text-slate-400 hover:text-brand-green transition-colors duration-300">Press</a></li>
                <li><a href="#" className="text-slate-400 hover:text-brand-green transition-colors duration-300">Blog</a></li>
                <li><a href="#" className="text-slate-400 hover:text-brand-green transition-colors duration-300">Investors</a></li>
              </ul>
            </div>

            <div data-aos="fade-up" data-aos-delay="300">
              <h3 className="text-lg font-bold mb-6 text-white">Support</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-slate-400 hover:text-brand-green transition-colors duration-300">Help Center</a></li>
                <li><a href="#" className="text-slate-400 hover:text-brand-green transition-colors duration-300">Contact Us</a></li>
                <li><a href="#" className="text-slate-400 hover:text-brand-green transition-colors duration-300">Security</a></li>
                <li><a href="#" className="text-slate-400 hover:text-brand-green transition-colors duration-300">Privacy Policy</a></li>
                <li><a href="#" className="text-slate-400 hover:text-brand-green transition-colors duration-300">Terms of Service</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-slate-800 mt-12 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <p className="text-slate-500 text-sm">
                © 2024 ASUSU by Koja. All rights reserved. Licensed by CBN.
              </p>
              <div className="flex items-center gap-6">
                <a href="#" className="text-slate-400 hover:text-brand-green transition-colors duration-300">
                  <span className="sr-only">Twitter</span>
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
                <a href="#" className="text-slate-400 hover:text-brand-green transition-colors duration-300">
                  <span className="sr-only">LinkedIn</span>
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
                <a href="#" className="text-slate-400 hover:text-brand-green transition-colors duration-300">
                  <span className="sr-only">Instagram</span>
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.618 5.367 11.986 11.988 11.986s11.987-5.368 11.987-11.986C24.014 5.367 18.635.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.876.876 1.366 2.027 1.366 3.324s-.49 2.448-1.366 3.323c-.875.876-2.026 1.366-3.323 1.366zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.876-.875-1.366-2.026-1.366-3.323s.49-2.448 1.366-3.323c.875-.876 2.026-1.366 3.323-1.366s2.448.49 3.323 1.366c.876.875 1.366 2.026 1.366 3.323s-.49 2.448-1.366 3.323c-.875.876-2.026 1.366-3.323 1.366z"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
