import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Check, Shield, Star, Zap, PiggyBank, CreditCard, BarChart, Users, Moon, Sun } from "lucide-react";
import { Link } from "react-router-dom";
import { useTheme } from "@/hooks/use-theme";

const Index = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <div className="min-h-screen flex flex-col">
      {/* Navigation */}
      <header className="border-b bg-background/80 backdrop-blur-md sticky top-0 z-10" data-aos="fade-down">
        <div className="container mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="h-10 w-10 rounded-full overflow-hidden">
              <img 
                src="/lovable-uploads/96b8da77-302c-4b65-87fb-ec3cf6bc86ca.png" 
                alt="Koja Logo" 
                className="h-full w-full object-cover"
              />
            </div>
            <span className="text-0.95xl font-bold text-brand-blue">ASUSU BY KOJA</span>
          </div>
          <nav className="hidden md:flex gap-6">
            <a href="#features" className="text-sm font-medium hover:text-brand-blue transition-colors">Features</a>
            <a href="#how-it-works" className="text-sm font-medium hover:text-brand-blue transition-colors">How It Works</a>
            <a href="#testimonials" className="text-sm font-medium hover:text-brand-blue transition-colors">Testimonials</a>
          </nav>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleTheme}
              className="h-9 w-9"
            >
              {theme === 'dark' ? (
                <Sun className="h-4 w-4" />
              ) : (
                <Moon className="h-4 w-4" />
              )}
              <span className="sr-only">Toggle theme</span>
            </Button>
            <Link to="/login">
              <Button variant="ghost" className="text-brand-blue">Log In</Button>
            </Link>
            <Link to="/signup">
              <Button className="bg-brand-blue hover:bg-brand-blue/90">Sign Up</Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 md:py-32 bg-gradient-to-b from-background to-brand-lightBlue/20 dark:to-brand-lightBlue/10">
        <div className="container mx-auto px-4 flex flex-col md:flex-row items-center">
          <div className="md:w-1/2 space-y-6" data-aos="fade-right" data-aos-delay="100">
            <div className="inline-block px-3 py-1 text-sm font-medium bg-brand-blue/20 text-brand-blue rounded-full">
              Modern Nigerian Digital Savings
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight">
              <span className="text-brand-blue">ASUSU</span> by Koja: Digital <span className="text-brand-blue">KOLO</span> for Nigeria's Future
            </h1>
            <p className="text-lg text-muted-foreground max-w-lg">
              Asusu brings traditional Nigerian kolo savings into the digital age, helping Nigerians save smarter and achieve their financial goals faster.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <Link to="/signup">
                <Button size="lg" className="bg-brand-blue hover:bg-brand-blue/90">
                  Get Started
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <a href="#how-it-works">
                <Button size="lg" variant="outline">
                  Learn More
                </Button>
              </a>
            </div>
          </div>
          <div className="md:w-1/2 mt-10 md:mt-0 relative" data-aos="fade-left" data-aos-delay="200">
            <div className="absolute -inset-0.5 bg-gradient-to-r from-brand-blue to-brand-yellow rounded-lg blur opacity-30"></div>
            <div className="relative p-5 bg-background rounded-lg shadow-xl dark:bg-card">
              <div className="rounded-lg overflow-hidden shadow-sm">
                <div className="bg-brand-lightBlue rounded-lg p-8 flex items-center justify-center">
                  <div className="w-64 h-64 bg-background rounded-full border-8 border-brand-blue flex items-center justify-center shadow-lg animate-float">
                    <div className="w-48 h-48 bg-brand-yellow/10 rounded-full flex items-center justify-center relative">
                      <PiggyBank className="h-24 w-24 text-brand-blue" />
                      <div className="absolute top-0 -right-4 w-8 h-8 bg-brand-yellow rounded-full flex items-center justify-center animate-bounce-gentle">
                        <span className="text-xs font-bold">₦</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-2xl mx-auto mb-16" data-aos="fade-up">
            <h2 className="text-3xl font-bold mb-4">Why Choose Asusu by Koja</h2>
            <p className="text-muted-foreground">
              Our platform combines traditional Nigerian savings culture with modern technology to give you the best experience.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: <Zap className="h-10 w-10 text-brand-blue" />,
                title: "Easy Daily Savings",
                description:
                  "Make daily contributions effortlessly from your mobile phone, anytime and anywhere.",
              },
              {
                icon: <Shield className="h-10 w-10 text-brand-blue" />,
                title: "Secure & Transparent",
                description:
                  "Your money is protected with bank-level security, and all transactions are fully transparent.",
              },
              {
                icon: <Star className="h-10 w-10 text-brand-blue" />,
                title: "Savings Goals",
                description:
                  "Set personalized savings goals and track your progress towards achieving them.",
              },
            ].map((feature, index) => (
              <div
                key={index}
                className="kola-card p-6 hover:scale-105 transition-transform"
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                <div className="mb-4 rounded-full bg-brand-blue/10 p-3 w-fit">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
                <p className="text-muted-foreground">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How it works */}
      <section id="how-it-works" className="py-20 bg-brand-lightBlue/30">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-2xl mx-auto mb-16" data-aos="fade-up">
            <h2 className="text-3xl font-bold mb-4">How It Works</h2>
            <p className="text-muted-foreground">
              Start your savings journey in just a few simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-6">
            {[
              {
                step: "01",
                title: "Create Account",
                description: "Sign up for free and set up your profile in minutes",
                icon: <Users className="h-8 w-8 text-brand-blue" />
              },
              {
                step: "02",
                title: "Set Your Goals",
                description: "Define what you're saving for and set your target amount",
                icon: <BarChart className="h-8 w-8 text-brand-blue" />
              },
              {
                step: "03",
                title: "Make Daily Deposits",
                description: "Contribute as little as ₦500 daily to grow your savings",
                icon: <CreditCard className="h-8 w-8 text-brand-blue" />
              },
              {
                step: "04",
                title: "Withdraw When Ready",
                description: "Access your funds when you reach your goals",
                icon: <PiggyBank className="h-8 w-8 text-brand-blue" />
              },
            ].map((step, index) => (
              <div
                key={index}
                className="kola-card p-6 relative overflow-hidden group"
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                <div className="absolute top-3 right-3 w-10 h-10 flex items-center justify-center rounded-full bg-brand-blue/10 group-hover:bg-brand-blue/20 transition-colors">
                  {step.icon}
                </div>
                <div className="text-5xl font-bold text-brand-blue/30 mb-4">
                  {step.step}
                </div>
                <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                <p className="text-muted-foreground">{step.description}</p>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center" data-aos="fade-up" data-aos-delay="400">
            <Link to="/signup">
              <Button size="lg" className="bg-brand-blue hover:bg-brand-blue/90">
                Start Saving Today
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonials" className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-2xl mx-auto mb-16" data-aos="fade-up">
            <h2 className="text-3xl font-bold mb-4">What Our Users Say</h2>
            <p className="text-muted-foreground">
              Hear from Nigerians who have transformed their financial habits with Asusu
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                name: "Chioma Okafor",
                role: "Small Business Owner",
                quote:
                  "Asusu helped me save enough to expand my business. The daily contribution system works perfectly with my income flow.",
              },
              {
                name: "Emmanuel Adeyemi",
                role: "Teacher",
                quote:
                  "I've been able to save for my children's education without stress. The app reminds me to make my daily contributions.",
              },
              {
                name: "Ngozi Eze",
                role: "IT Professional",
                quote:
                  "The transparency is what I love most. I can see exactly where my money is going and how it's growing over time.",
              },
            ].map((testimonial, index) => (
              <div
                key={index}
                className="kola-card p-6 group"
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                <div className="mb-4">
                  {Array(5)
                    .fill(0)
                    .map((_, i) => (
                      <Star
                        key={i}
                        className="inline-block h-5 w-5 fill-brand-yellow text-brand-yellow"
                      />
                    ))}
                </div>
                <p className="text-muted-foreground mb-4 italic">
                  "{testimonial.quote}"
                </p>
                <div>
                  <p className="font-bold">{testimonial.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {testimonial.role}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 asusu-gradient" data-aos="fade-up">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-4">
            Ready to Start Your Savings Journey?
          </h2>
          <p className="text-white/80 mb-6 max-w-2xl mx-auto">
            Join thousands of Nigerians who are building a better financial future with Asusu by Koja
          </p>
          <Link to="/signup">
            <Button
              size="lg"
              className="bg-brand-yellow text-brand-blue hover:bg-brand-yellow/90"
            >
              Create Your Free Account
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-slate-900 text-white" data-aos="fade-up">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div data-aos="fade-up" data-aos-delay="100">
              <div className="flex items-center gap-2 mb-4">
                <div className="h-8 w-8 rounded-full overflow-hidden">
                  <img 
                    src="/lovable-uploads/96b8da77-302c-4b65-87fb-ec3cf6bc86ca.png" 
                    alt="Koja Logo" 
                    className="h-full w-full object-cover"
                  />
                </div>
                <span className="text-0.95xl font-bold text-white">ASUSU BY KOJA</span>
              </div>
              <p className="text-slate-400">
                The modern solution for traditional kolo savings in Nigeria.
              </p>
            </div>
            
            <div data-aos="fade-up" data-aos-delay="200">
              <h3 className="text-lg font-bold mb-4">Company</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">About Us</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Press</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Blog</a></li>
              </ul>
            </div>
            
            <div data-aos="fade-up" data-aos-delay="300">
              <h3 className="text-lg font-bold mb-4">Resources</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Contact Us</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Terms of Service</a></li>
              </ul>
            </div>
            
            <div data-aos="fade-up" data-aos-delay="400">
              <h3 className="text-lg font-bold mb-4">Stay Connected</h3>
              <p className="text-slate-400 mb-4">
                Subscribe to our newsletter for updates
              </p>
              <div className="flex">
                <input 
                  type="email" 
                  placeholder="Your email" 
                  className="px-3 py-2 bg-slate-800 text-white rounded-l-md w-full focus:outline-none"
                />
                <Button className="rounded-l-none bg-brand-blue text-white hover:bg-brand-blue/90">
                  Subscribe
                </Button>
              </div>
            </div>
          </div>
          
          <div className="mt-12 pt-8 border-t border-slate-800 text-center text-slate-400" data-aos="fade-up" data-aos-delay="500">
            <p>© {new Date().getFullYear()} Asusu by Koja. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
