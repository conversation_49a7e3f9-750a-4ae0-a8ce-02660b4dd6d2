
import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { useAuthApi, User, SignInCredentials, SignUpData } from './use-auth-api';

interface AuthContextType {
  user: User | null;
  isAdmin: boolean;
  isLoading: boolean;
  signIn: (credentials: SignInCredentials) => Promise<void>;
  signUp: (userData: SignUpData) => Promise<void>;
  signOut: () => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const navigate = useNavigate();
  const { 
    user, 
    isLoading, 
    isAuthenticated,
    signIn: apiSignIn, 
    signUp: apiSignUp, 
    signOut: apiSignOut,
    getCurrentUser
  } = useAuthApi();
  
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    // Try to load user on initial mount
    const loadUser = async () => {
      await refreshUser();
    };
    
    loadUser();
  }, []);

  useEffect(() => {
    // Update isAdmin when user changes
    setIsAdmin(user?.isAdmin || false);
  }, [user]);

  const refreshUser = async () => {
    try {
      await getCurrentUser();
    } catch (error) {
      console.error('Error refreshing user:', error);
    }
  };

  const signIn = async (credentials: SignInCredentials) => {
    try {
      const { data, error } = await apiSignIn(credentials);
      
      if (error) {
        throw new Error(error);
      }
      
      if (data?.user) {
        toast.success('Signed in successfully');
        if (data.user.isAdmin) {
          navigate('/admin/dashboard');
        } else {
          navigate('/user/dashboard');
        }
      }
    } catch (error) {
      console.error('Error signing in:', error);
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error('Failed to sign in');
      }
    }
  };

  const signUp = async (userData: SignUpData) => {
    try {
      const { data, error } = await apiSignUp(userData);
      
      if (error) {
        throw new Error(error);
      }
      
      toast.success('Account created successfully');
      navigate('/user/dashboard');
    } catch (error) {
      console.error('Error signing up:', error);
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error('Failed to create account');
      }
    }
  };

  const signOut = () => {
    apiSignOut();
    navigate('/login');
    toast.success('Logged out successfully');
  };

  const value = {
    user,
    isAdmin,
    isLoading,
    signIn,
    signUp,
    signOut,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
