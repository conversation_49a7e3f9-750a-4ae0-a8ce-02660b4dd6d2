
import React, { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { StatCard } from "@/components/ui/stat-card";
import { StatsService } from '@/services/stats';
import { SavingsRankBadge } from "@/components/analytics/SavingsRankBadge";
import { SavingsChart } from "@/components/analytics/SavingsChart";
import { SavingsGoalProgress } from "@/components/analytics/SavingsGoalProgress";
import { ActivityTimeline } from "@/components/analytics/ActivityTimeline";
import { Button } from "@/components/ui/button";
import { BarChart3, Calendar, CreditCard, DollarSign, Medal, PiggyBank, Target, TrendingUp, Trophy, Users } from "lucide-react";


  // State for user summary
  const [summary, setSummary] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    StatsService.getUserSavingsSummary().then(({ data, error }) => {
      if (error) {
        setError('Failed to load savings summary');
        setSummary(null);
      } else {
        setSummary(data);
        setError(null);
      }
      setLoading(false);
    });
  }, []);

  // Fallbacks for demo fields not in summary
  const savingsRank = "Silver";
  const savingsDuration = 183;
  const consistencyScore = 85;

  const totalSaved = summary?.totalSaved ?? 0;
  const savingsGoal = summary?.totalTargetAmount ?? 0;
  const activePlans = summary?.activePlans ?? 0;

  return (
    <div className="space-y-6">
      {loading && <div className="text-center py-8">Loading analytics...</div>}
      {error && <div className="text-center text-red-500 py-8">{error}</div>}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Savings Analytics</h1>
          <p className="text-muted-foreground">
            Track your savings performance and achievements
          </p>
        </div>
        
        <Button className="bg-brand-yellow text-foreground hover:bg-brand-yellow/90">
          <PiggyBank className="mr-2 h-4 w-4" />
          Start New Savings
        </Button>
      </div>

      {/* User Ranking and Badges Section */}
      <Card className="overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-brand-blue to-brand-deepBlue text-white">
          <CardTitle>Your Savings Performance</CardTitle>
          <CardDescription className="text-white/80">
            Your savings journey and achievement badges
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <SavingsRankBadge 
              rank={savingsRank} 
              duration={savingsDuration} 
              consistencyScore={consistencyScore}
            />
            
            <div className="flex flex-col justify-between">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Your Achievements</h3>
                <p className="text-sm text-muted-foreground">Badges earned through your savings journey</p>
              </div>
              
              <div className="grid grid-cols-3 gap-2 mt-4">
                <div className="flex flex-col items-center p-2 border rounded-lg">
                  <Trophy className="h-8 w-8 text-brand-yellow mb-1" />
                  <span className="text-xs font-medium text-center">First Saver</span>
                </div>
                
                <div className="flex flex-col items-center p-2 border rounded-lg">
                  <Target className="h-8 w-8 text-brand-blue mb-1" />
                  <span className="text-xs font-medium text-center">Goal Setter</span>
                </div>
                
                <div className="flex flex-col items-center p-2 border rounded-lg">
                  <Calendar className="h-8 w-8 text-green-500 mb-1" />
                  <span className="text-xs font-medium text-center">Consistent</span>
                </div>
                
                <div className="flex flex-col items-center p-2 border rounded-lg">
                  <Medal className="h-8 w-8 text-brand-yellow mb-1" />
                  <span className="text-xs font-medium text-center">30 Days</span>
                </div>
                
                <div className="flex flex-col items-center p-2 border rounded-lg">
                  <Medal className="h-8 w-8 text-gray-400 mb-1" />
                  <span className="text-xs font-medium text-center">90 Days</span>
                </div>
                
                <div className="flex flex-col items-center p-2 border rounded-lg border-dashed">
                  <div className="h-8 w-8 flex items-center justify-center rounded-full border-2 border-dashed text-muted-foreground">
                    ?
                  </div>
                  <span className="text-xs font-medium text-center text-muted-foreground">Locked</span>
                </div>
              </div>
            </div>
            
            <div className="flex flex-col justify-between">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Savings Goal Progress</h3>
                <p className="text-sm text-muted-foreground">Track your progress toward your savings goal</p>
              </div>
              
              <SavingsGoalProgress 
                currentAmount={totalSaved}
                goalAmount={savingsGoal}
              />
              
              <div className="mt-2 text-sm text-right">
                <span className="font-medium">₦{(savingsGoal - totalSaved).toLocaleString()}</span> more to reach your goal
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Overview Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total Saved"
          value={loading ? '—' : `₦${totalSaved.toLocaleString()}`}
          description="Your lifetime savings"
          icon={<DollarSign />}
        />
        <StatCard
          title="Consistency Score"
          value={loading ? '—' : `${consistencyScore}%`}
          description="Your savings regularity"
          icon={<TrendingUp />}
        />
        <StatCard
          title="Active Plans"
          value={loading ? '—' : activePlans}
          description="Current savings plans"
          icon={<PiggyBank />}
        />
        <StatCard
          title="Ranking"
          value="#28"
          description="Among all savers"
          icon={<Users />}
        />
      </div>

      {/* Charts and Timeline Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Savings History Chart */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2 text-brand-blue" />
              Savings History
            </CardTitle>
            <CardDescription>
              Your savings deposits over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SavingsChart />
          </CardContent>
        </Card>
        
        {/* Activity Timeline */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Your latest savings activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ActivityTimeline />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
