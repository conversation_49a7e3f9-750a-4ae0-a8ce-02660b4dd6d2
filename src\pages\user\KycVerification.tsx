
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { KycVerificationForm } from "@/components/kyc/KycVerificationForm";
import { useAuth } from "@/hooks/use-auth";
import { toast } from "sonner";
import { uploadKycDocument } from "@/integrations/supabase/auth";
import { LoadingScreen } from "@/components/ui/loading-screen";

const KycVerification = () => {
  const { user, refreshUser } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleSubmit = async (formData: any) => {
    try {
      setIsSubmitting(true);
      
      // Upload ID document
      const { data: idData, error: idError } = await uploadKycDocument(
        formData.idImage,
        formData.idType
      );
      
      if (idError) throw idError;
      
      // Upload proof of address
      const { data: proofData, error: proofError } = await uploadKycDocument(
        formData.proofOfAddressImage,
        'proof_of_address'
      );
      
      if (proofError) throw proofError;
      
      // Update user profile in database with the form data
      // This is separate from the KYC documents
      
      toast.success("KYC documents uploaded successfully. They are pending verification.");
      await refreshUser();
    } catch (error: any) {
      console.error("Error uploading KYC documents:", error);
      toast.error(error.message || "Failed to upload KYC documents");
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const kyc_status = user?.profile?.kyc_status;
  
  if (!user) {
    return <LoadingScreen />;
  }
  
  return (
    <div className="container max-w-3xl py-6">
      <h1 className="text-3xl font-bold mb-6">KYC Verification</h1>
      
      <Card>
        <CardHeader>
          <CardTitle>Identity Verification</CardTitle>
          <CardDescription>
            {kyc_status === 'verified' 
              ? "Your identity has been verified successfully."
              : kyc_status === 'rejected'
              ? "Your previous verification was rejected. Please submit your documents again."
              : "Please provide your identification documents to verify your identity."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {kyc_status === 'verified' ? (
            <div className="bg-green-50 border border-green-200 text-green-700 p-4 rounded-md">
              <p className="font-medium">Your identity has been verified</p>
              <p className="text-sm mt-1">You have full access to all features of the platform.</p>
            </div>
          ) : (
            <KycVerificationForm 
              onSubmit={handleSubmit}
              isLoading={isSubmitting}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default KycVerification;
