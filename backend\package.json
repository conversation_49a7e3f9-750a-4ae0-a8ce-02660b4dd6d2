{"name": "backend", "version": "1.0.0", "description": "", "license": "ISC", "author": "", "type": "commonjs", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon index.js", "start": "node index.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mongoose": "^8.14.2", "multer": "^1.4.5-lts.2", "multer-s3": "^3.0.1", "node-cron": "^4.0.7", "nodemailer": "^7.0.4"}, "devDependencies": {"nodemon": "^3.1.10"}}