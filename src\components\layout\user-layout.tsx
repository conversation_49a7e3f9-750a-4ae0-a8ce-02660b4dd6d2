
import React from 'react';
import { Sidebar } from './sidebar';
import { Navbar } from './navbar';
import { useAuth } from '@/hooks/use-auth';

interface UserLayoutProps {
  children: React.ReactNode;
}

export const UserLayout = ({ children }: UserLayoutProps) => {
  const { isAdmin } = useAuth();

  return (
    <div className="flex h-screen flex-col">
      <Navbar isAdmin={isAdmin} />
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />
        <main className="flex-1 overflow-y-auto p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default UserLayout;
