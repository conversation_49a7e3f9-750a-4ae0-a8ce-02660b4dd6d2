const express = require('express');
const GroupSavingsPlan = require('../models/groupSavingsPlan');
const authMiddleware = require('../middleware/authMiddleware');
const router = express.Router();

// Create a new group savings plan
const { sendGroupInvite } = require('../utils/mailer');
router.post('/group-plan', authMiddleware, async (req, res) => {
  try {
    const { title, depositFrequency, depositAmount, targetDate, targetAmount, isPublic, pendingInvites } = req.body;
    const owner = req.user && req.user.id;
    const ownerEmail = req.user && req.user.email;
    if (!owner) {
      return res.status(401).json({ error: 'User authentication failed. Please log in again.' });
    }
    if (!title || !depositFrequency || !depositAmount || !targetDate || !targetAmount) {
      return res.status(400).json({ error: 'All fields are required.' });
    }
    if (typeof depositAmount !== 'number' || depositAmount <= 0) {
      return res.status(400).json({ error: 'Deposit amount must be a positive number.' });
    }
    if (typeof targetAmount !== 'number' || targetAmount <= 0) {
      return res.status(400).json({ error: 'Target amount must be a positive number.' });
    }
    if (isNaN(Date.parse(targetDate))) {
      return res.status(400).json({ error: 'Target date is invalid.' });
    }
    const groupPlan = new GroupSavingsPlan({
      title,
      depositFrequency,
      depositAmount,
      targetDate,
      targetAmount,
      owner,
      members: [owner],
      isPublic: !!isPublic,
      pendingInvites: Array.isArray(pendingInvites) ? pendingInvites : [],
    });
    const savedPlan = await groupPlan.save();

    // Send invite emails if there are pendingInvites
    if (Array.isArray(pendingInvites) && pendingInvites.length > 0) {
      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:8050';
      const inviteLink = `${baseUrl}/group-invite/${savedPlan._id}`;
      for (const email of pendingInvites) {
        try {
          await sendGroupInvite(email, title, inviteLink);
        } catch (err) {
          console.error(`Failed to send invite to ${email}:`, err);
        }
      }
    }

    res.status(201).json(savedPlan);
  } catch (error) {
    console.error('Error creating group savings plan:', error);
    res.status(500).json({ error: 'Failed to create group savings plan', details: error.message });
  }
});

// List all public group savings plans
router.get('/group-plans/public', async (req, res) => {
  try {
    const plans = await GroupSavingsPlan.find({ isPublic: true }).sort({ createdAt: -1 });
    res.status(200).json(plans);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch public group savings plans', details: error.message });
  }
});

// Join a public group savings plan
router.post('/group-plans/join/:id', authMiddleware, async (req, res) => {
  try {
    const userId = req.user && req.user.id;
    const { id } = req.params;
    const plan = await GroupSavingsPlan.findById(id);
    if (!plan) return res.status(404).json({ error: 'Group savings plan not found.' });
    if (!plan.isPublic) return res.status(403).json({ error: 'This group is not public.' });
    if (plan.members.includes(userId)) return res.status(400).json({ error: 'Already a member.' });
    plan.members.push(userId);
    await plan.save();
    res.status(200).json({ message: 'Joined group successfully', plan });
  } catch (error) {
    res.status(500).json({ error: 'Failed to join group', details: error.message });
  }
});

// Accept invite to a private group
router.post('/group-plans/accept-invite/:id', authMiddleware, async (req, res) => {
  try {
    const userId = req.user && req.user.id;
    const userEmail = req.user && req.user.email;
    const { id } = req.params;
    const plan = await GroupSavingsPlan.findById(id);
    if (!plan) return res.status(404).json({ error: 'Group savings plan not found.' });
    if (!plan.pendingInvites.includes(userEmail)) return res.status(403).json({ error: 'No invite found for this email.' });
    if (plan.members.includes(userId)) return res.status(400).json({ error: 'Already a member.' });
    plan.members.push(userId);
    plan.pendingInvites = plan.pendingInvites.filter(email => email !== userEmail);
    await plan.save();
    res.status(200).json({ message: 'Joined group successfully', plan });
  } catch (error) {
    res.status(500).json({ error: 'Failed to accept invite', details: error.message });
  }
});

// Get all group savings plans for the logged-in user (owner or member)
router.get('/group-plans/my', authMiddleware, async (req, res) => {
  try {
    const userId = req.user && req.user.id;
    const plans = await GroupSavingsPlan.find({ $or: [ { owner: userId }, { members: userId } ] }).sort({ createdAt: -1 });
    res.status(200).json(plans);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch user group savings plans', details: error.message });
  }
});

module.exports = router;

// --- NEW: Get all group savings (regular + rotational) for the logged-in user ---
const RotationalGroupSavings = require('../models/rotationalGroupSavings');
router.get('/all-my', authMiddleware, async (req, res) => {
  try {
    const userId = req.user && req.user.id;
    // Regular group savings (owner or member)
    const groupPlans = await GroupSavingsPlan.find({ $or: [ { owner: userId }, { members: userId } ] }).sort({ createdAt: -1 });
    // Rotational group savings (createdBy or member)
    const rotationalGroups = await RotationalGroupSavings.find({
      $or: [
        { createdBy: userId },
        { 'members.userId': userId }
      ]
    }).sort({ createdAt: -1 });
    res.status(200).json({ groupPlans, rotationalGroups });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch all user group savings', details: error.message });
  }
});
