
import { AuthProvider } from '../auth/auth-service';

// Current provider - should match auth-service
const currentProvider = AuthProvider.MOCK;

export interface PaymentRequest {
  userId: string;
  amount: number;
  savingsPlanId?: string;
  paymentMethod: string;
  transactionType: 'deposit' | 'withdrawal' | 'transfer';
  description?: string;
  metadata?: Record<string, any>;
}

export const PaymentService = {
  // Process a payment
  processPayment: async (paymentData: PaymentRequest) => {
    try {
      if (currentProvider === AuthProvider.MOCK) {
        // Mock implementation
        return { data: { success: true, transactionId: 'mock-tx-' + Date.now() }, error: null };
      } else {
        return { data: null, error: new Error('Provider not supported') };
      }
    } catch (error) {
      console.error('Payment processing error:', error);
      return { data: null, error };
    }
  },

  // Upload payment proof
  uploadPaymentProof: async (file: File, metadata: {
    userId: string;
    transactionId?: string;
    amount: number;
    paymentMethod: string;
    description?: string;
  }) => {
    try {
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { 
        data: { 
          success: true, 
          fileUrl: 'mock-file-url',
          uploadId: 'mock-upload-' + Date.now()
        }, 
        error: null 
      };
    } catch (error) {
      console.error('Payment proof upload error:', error);
      return { data: null, error };
    }
  }
};
