
import type {
  GroupSavingsPlan,
  GroupMember,
  RotationalGroup,
  GroupTransaction,
  CreateGroupSavingsInput,
  CreateRotationalGroupInput
} from '@/types/group-savings';

const BACKEND_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:8080';

export const GroupSavingsService = {

  getGroupSavingsPlans: async (): Promise<{ data: GroupSavingsPlan[] | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/group-savings`);
      if (!res.ok) throw new Error('Failed to fetch group savings plans');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching group savings plans:', error);
      return { data: null, error };
    }
  },

  getRotationalGroups: async (): Promise<{ data: RotationalGroup[] | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/rotational-group-savings`);
      if (!res.ok) throw new Error('Failed to fetch rotational groups');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching rotational groups:', error);
      return { data: null, error };
    }
  },

  getGroupMembers: async (groupId: string): Promise<{ data: GroupMember[] | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/group-savings/${groupId}/members`);
      if (!res.ok) throw new Error('Failed to fetch group members');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching group members:', error);
      return { data: null, error };
    }
  },

  getGroupTransactions: async (groupId: string): Promise<{ data: GroupTransaction[] | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/group-savings/${groupId}/transactions`);
      if (!res.ok) throw new Error('Failed to fetch group transactions');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching group transactions:', error);
      return { data: null, error };
    }
  },

  createGroupSavingsPlan: async (planData: CreateGroupSavingsInput): Promise<{ data: GroupSavingsPlan | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/group-savings`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(planData)
      });
      if (!res.ok) throw new Error('Failed to create group savings plan');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error creating group savings plan:', error);
      return { data: null, error };
    }
  },

  createRotationalGroup: async (groupData: CreateRotationalGroupInput & { createdBy?: string; nextPayoutDate?: string }): Promise<{ data: RotationalGroup | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/rotational-group-savings/rgs`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(groupData)
      });
      if (!res.ok) throw new Error('Failed to create rotational group');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error creating rotational group:', error);
      return { data: null, error };
    }
  },
  joinRotationalGroup: async (groupId: string, userId: string): Promise<{ data: any | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/rotational-group-savings/${groupId}/join`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      });
      if (!res.ok) throw new Error('Failed to join group');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error joining rotational group:', error);
      return { data: null, error };
    }
  },

  joinGroupSavingsPlan: async (groupId: string): Promise<{ data: any | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/group-savings/${groupId}/join`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      if (!res.ok) throw new Error('Failed to join group savings plan');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error joining group savings plan:', error);
      return { data: null, error };
    }
  },

  addMemberToGroup: async (groupId: string, email: string, contributionAmount: number, role: 'admin' | 'member' = 'member'): Promise<{ data: any | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/group-savings/${groupId}/add-member`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, contributionAmount, role })
      });
      if (!res.ok) throw new Error('Failed to add member to group');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error adding member to group:', error);
      return { data: null, error };
    }
  }
};
