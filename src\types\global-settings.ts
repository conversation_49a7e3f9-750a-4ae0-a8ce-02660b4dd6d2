// Global Settings Types for ASUSU by Koja
// TypeScript interfaces and types for global settings management

export interface Logo {
  url: string | null;
  filename: string | null;
  uploadedAt: Date | null;
}

export interface Favicon {
  url: string | null;
  filename: string | null;
}

export interface ColorTheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
}

export interface PaystackConfig {
  publicKey: string | null;
  secretKey: string | null;
  isLive: boolean;
}

export interface AWSConfig {
  accessKeyId: string | null;
  secretAccessKey: string | null;
  region: string;
  s3Bucket: string | null;
}

export interface APIKeys {
  paystack: PaystackConfig;
  aws: AWSConfig;
}

export interface FeatureItem {
  title: string;
  description: string;
  icon: string;
  image: {
    url: string;
    filename: string;
  };
}

export interface Testimonial {
  name: string;
  role: string;
  content: string;
  avatar: {
    url: string;
    filename: string;
  };
  rating: number;
}

export interface LandingPageContent {
  heroTitle: string;
  heroSubtitle: string;
  heroImage: {
    url: string;
    filename: string;
  };
  features: FeatureItem[];
  testimonials: Testimonial[];
}

export interface AboutPageContent {
  title: string;
  content: string;
  images: Array<{
    url: string;
    filename: string;
    caption: string;
  }>;
}

export interface ContentManagement {
  landingPage: LandingPageContent;
  aboutPage: AboutPageContent;
}

export interface SystemConfiguration {
  maintenanceMode: boolean;
  maintenanceMessage: string;
  allowRegistration: boolean;
  emailVerificationRequired: boolean;
  kycRequired: boolean;
  minimumDeposit: number;
  maximumDeposit: number;
}

export interface SocialMedia {
  facebook?: string;
  twitter?: string;
  instagram?: string;
  linkedin?: string;
}

export interface ContactInformation {
  email: string;
  phone: string;
  address: string;
  socialMedia: SocialMedia;
}

export interface GlobalSettings {
  _id?: string;
  appName: string;
  appDescription: string;
  appVersion: string;
  logo: Logo;
  favicon: Favicon;
  colors: ColorTheme;
  apiKeys: APIKeys;
  content: ContentManagement;
  system: SystemConfiguration;
  contact: ContactInformation;
  lastUpdatedBy?: string;
  version: number;
  createdAt?: Date;
  updatedAt?: Date;
}

// Public settings (without sensitive data)
export interface PublicGlobalSettings {
  appName: string;
  appDescription: string;
  appVersion: string;
  logo: Logo;
  favicon: Favicon;
  colors: ColorTheme;
  content: ContentManagement;
  contact: Omit<ContactInformation, 'socialMedia'> & {
    socialMedia: SocialMedia;
  };
}

// API Response types
export interface GlobalSettingsResponse {
  success: boolean;
  data: GlobalSettings;
  message?: string;
}

export interface PublicSettingsResponse {
  success: boolean;
  data: PublicGlobalSettings;
  message?: string;
}

// Update request types
export interface AppConfigUpdate {
  appName?: string;
  appDescription?: string;
  appVersion?: string;
}

export interface ColorThemeUpdate {
  primary?: string;
  secondary?: string;
  accent?: string;
  background?: string;
}

export interface BrandingUpdate {
  logoUrl?: string;
  logoFilename?: string;
  faviconUrl?: string;
  faviconFilename?: string;
}

export interface APIKeysUpdate {
  paystack?: Partial<PaystackConfig>;
  aws?: Partial<AWSConfig>;
}

export interface ContentUpdate {
  landingPage?: Partial<LandingPageContent>;
  aboutPage?: Partial<AboutPageContent>;
}

export interface SystemConfigUpdate {
  maintenanceMode?: boolean;
  maintenanceMessage?: string;
  allowRegistration?: boolean;
  emailVerificationRequired?: boolean;
  kycRequired?: boolean;
  minimumDeposit?: number;
  maximumDeposit?: number;
}

export interface ContactUpdate {
  email?: string;
  phone?: string;
  address?: string;
  socialMedia?: Partial<SocialMedia>;
}

// Settings history type
export interface SettingsHistory {
  currentVersion: number;
  lastUpdatedBy: {
    firstName: string;
    lastName: string;
    email: string;
  } | null;
  lastUpdatedAt: Date;
}

export interface SettingsHistoryResponse {
  success: boolean;
  data: SettingsHistory;
  message?: string;
}

// Form validation types
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormState<T> {
  data: T;
  loading: boolean;
  errors: ValidationError[];
  isDirty: boolean;
}

// Global settings context types
export interface GlobalSettingsContextType {
  settings: PublicGlobalSettings | null;
  adminSettings: GlobalSettings | null;
  loading: boolean;
  error: string | null;
  refreshSettings: () => Promise<void>;
  refreshAdminSettings: () => Promise<void>;
  updateAppConfig: (data: AppConfigUpdate) => Promise<boolean>;
  updateColors: (data: ColorThemeUpdate) => Promise<boolean>;
  updateBranding: (data: BrandingUpdate) => Promise<boolean>;
  updateAPIKeys: (data: APIKeysUpdate) => Promise<boolean>;
  updateContent: (data: ContentUpdate) => Promise<boolean>;
  updateSystem: (data: SystemConfigUpdate) => Promise<boolean>;
  updateContact: (data: ContactUpdate) => Promise<boolean>;
  applyThemeColors: (colors: ColorTheme) => void;
}

// Hook return types
export interface UseGlobalSettingsReturn {
  settings: PublicGlobalSettings | null;
  loading: boolean;
  error: string | null;
  refreshSettings: () => Promise<void>;
}

export interface UseAdminSettingsReturn {
  adminSettings: GlobalSettings | null;
  loading: boolean;
  error: string | null;
  refreshAdminSettings: () => Promise<void>;
  updateAppConfig: (data: AppConfigUpdate) => Promise<boolean>;
  updateColors: (data: ColorThemeUpdate) => Promise<boolean>;
  updateBranding: (data: BrandingUpdate) => Promise<boolean>;
  updateAPIKeys: (data: APIKeysUpdate) => Promise<boolean>;
  updateContent: (data: ContentUpdate) => Promise<boolean>;
  updateSystem: (data: SystemConfigUpdate) => Promise<boolean>;
  updateContact: (data: ContactUpdate) => Promise<boolean>;
}
