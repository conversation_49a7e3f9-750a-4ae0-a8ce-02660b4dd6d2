const mongoose = require('mongoose');

const depositSchema = new mongoose.Schema({
  amount: {
    type: Number,
    required: true,
  },
  paystackReference: {
    type: String,
    required: true,
  },
  paystackStatus: {
    type: String,
    enum: ['pending', 'success', 'failed'],
    default: 'pending',
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  status: {
    type: String,
    enum: ['pending', 'success', 'failed'],
    default: 'pending',
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
});

module.exports = mongoose.model('Deposit', depositSchema);