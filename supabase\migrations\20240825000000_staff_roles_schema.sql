
-- Staff roles and permissions schema
-- This migration creates the necessary tables to support staff role management

-- Create role types enum
CREATE TYPE public.staff_role AS ENUM ('admin', 'user_manager', 'plan_manager', 'support_staff');

-- Create permissions enum
CREATE TYPE public.permission AS ENUM (
  'manage_users', 
  'view_users', 
  'edit_users', 
  'create_users',
  'manage_plans', 
  'view_plans', 
  'create_plans', 
  'edit_plans',
  'manage_transactions',
  'view_transactions',
  'manage_notifications',
  'approve_withdrawals'
);

-- Create staff roles table
CREATE TABLE IF NOT EXISTS public.staff_roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create role permissions junction table
CREATE TABLE IF NOT EXISTS public.role_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  role_id UUID REFERENCES public.staff_roles(id) ON DELETE CASCADE,
  permission permission NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(role_id, permission)
);

-- Create staff assignments table to assign roles to users
CREATE TABLE IF NOT EXISTS public.staff_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role_id UUID REFERENCES public.staff_roles(id) ON DELETE CASCADE,
  assigned_by UUID REFERENCES auth.users(id),
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(user_id, role_id)
);

-- Add RLS policies
ALTER TABLE public.staff_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.staff_assignments ENABLE ROW LEVEL SECURITY;

-- Policy for staff_roles table
CREATE POLICY "Admins can manage roles" 
  ON public.staff_roles
  FOR ALL 
  TO authenticated
  USING (public.is_admin(auth.uid()));

-- Policy for role_permissions table
CREATE POLICY "Admins can manage permissions" 
  ON public.role_permissions
  FOR ALL 
  TO authenticated
  USING (public.is_admin(auth.uid()));

-- Policy for staff_assignments table
CREATE POLICY "Admins can manage staff assignments" 
  ON public.staff_assignments
  FOR ALL 
  TO authenticated
  USING (public.is_admin(auth.uid()));

CREATE POLICY "Users can view their own assignments" 
  ON public.staff_assignments
  FOR SELECT 
  TO authenticated
  USING (auth.uid() = user_id);

-- Create function to check if a user has a specific permission
CREATE OR REPLACE FUNCTION public.user_has_permission(user_id UUID, required_permission permission)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- If user is admin, they have all permissions
  IF public.is_admin(user_id) THEN
    RETURN TRUE;
  END IF;
  
  -- Check if user has the specific permission through any of their roles
  RETURN EXISTS (
    SELECT 1 
    FROM public.staff_assignments sa
    JOIN public.role_permissions rp ON sa.role_id = rp.role_id
    WHERE sa.user_id = user_id
    AND rp.permission = required_permission
  );
END;
$$;

-- Create function to get all permissions for a user
CREATE OR REPLACE FUNCTION public.get_user_permissions(user_id UUID)
RETURNS SETOF permission
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- If user is admin, return all permissions
  IF public.is_admin(user_id) THEN
    RETURN QUERY SELECT unnest(enum_range(NULL::permission));
  END IF;
  
  -- Return the user's permissions based on their roles
  RETURN QUERY 
    SELECT DISTINCT rp.permission
    FROM public.staff_assignments sa
    JOIN public.role_permissions rp ON sa.role_id = rp.role_id
    WHERE sa.user_id = user_id;
END;
$$;

-- Insert default roles
INSERT INTO public.staff_roles (name, description)
VALUES 
  ('Admin', 'Full system access'),
  ('User Manager', 'Can manage users'),
  ('Plan Manager', 'Can manage savings plans'),
  ('Support Staff', 'Customer support capabilities')
ON CONFLICT (name) DO NOTHING;

-- Insert default permissions for roles
DO $$
DECLARE
  admin_role_id UUID;
  user_manager_role_id UUID;
  plan_manager_role_id UUID;
  support_staff_role_id UUID;
BEGIN
  -- Get role IDs
  SELECT id INTO admin_role_id FROM public.staff_roles WHERE name = 'Admin';
  SELECT id INTO user_manager_role_id FROM public.staff_roles WHERE name = 'User Manager';
  SELECT id INTO plan_manager_role_id FROM public.staff_roles WHERE name = 'Plan Manager';
  SELECT id INTO support_staff_role_id FROM public.staff_roles WHERE name = 'Support Staff';

  -- Admin permissions (all)
  INSERT INTO public.role_permissions (role_id, permission)
  SELECT admin_role_id, unnest(enum_range(NULL::permission))
  ON CONFLICT (role_id, permission) DO NOTHING;
  
  -- User Manager permissions
  INSERT INTO public.role_permissions (role_id, permission)
  VALUES 
    (user_manager_role_id, 'view_users'),
    (user_manager_role_id, 'edit_users'),
    (user_manager_role_id, 'create_users')
  ON CONFLICT (role_id, permission) DO NOTHING;
  
  -- Plan Manager permissions
  INSERT INTO public.role_permissions (role_id, permission)
  VALUES 
    (plan_manager_role_id, 'view_plans'),
    (plan_manager_role_id, 'create_plans'),
    (plan_manager_role_id, 'edit_plans')
  ON CONFLICT (role_id, permission) DO NOTHING;
  
  -- Support Staff permissions
  INSERT INTO public.role_permissions (role_id, permission)
  VALUES 
    (support_staff_role_id, 'view_users'),
    (support_staff_role_id, 'view_transactions'),
    (support_staff_role_id, 'manage_notifications')
  ON CONFLICT (role_id, permission) DO NOTHING;
END
$$;

-- Grant appropriate privileges
GRANT USAGE ON TYPE public.staff_role TO authenticated, anon, service_role;
GRANT USAGE ON TYPE public.permission TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION public.user_has_permission TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION public.get_user_permissions TO authenticated, anon, service_role;
