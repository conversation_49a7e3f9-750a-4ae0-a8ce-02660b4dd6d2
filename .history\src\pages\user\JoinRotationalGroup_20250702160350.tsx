
import { useParams, useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import { useRotationalGroups, useJoinRotationalGroup } from '@/hooks/use-group-savings';
import { useAuth } from '@/hooks/use-auth';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';

export default function JoinRotationalGroupPage() {
  const { groupId } = useParams<{ groupId: string }>();
  const { user } = useAuth();
  const { data: groups = [] } = useRotationalGroups();
  const joinMutation = useJoinRotationalGroup();
  const navigate = useNavigate();

  const group = Array.isArray(groups) ? groups.find((g: any) => g._id === groupId || g.id === groupId) : null;
  if (group) {
    // Log the group object for debugging
    // eslint-disable-next-line no-console
    console.log('JoinRotationalGroupPage group:', group);
  }

  useEffect(() => {
    if (!user && groupId) {
      // Store pending group join for after login
      localStorage.setItem('pendingJoinGroupId', groupId);
    }
    if (user && groupId && group) {
      // Optionally auto-join if not already a member
      const isMember = group.current_members?.some((m: any) => m.userId === user.id || m.id === user.id);
      if (!isMember) {
        joinMutation.mutate({ groupId, userId: user.id }, {
          onSuccess: () => {
            toast.success('You have joined the group!');
            localStorage.removeItem('pendingJoinGroupId');
            navigate(`/user/rotational-savings`);
          },
          onError: (error: any) => {
            toast.error(error?.message || 'Failed to join group');
            localStorage.removeItem('pendingJoinGroupId');
          }
        });
      }
    }
  }, [user, groupId, group]);

  if (!groupId) return <div className="p-8 text-center">Invalid group link.</div>;
  if (!group) return <div className="p-8 text-center">Loading group info...</div>;

  return (
    <div className="max-w-md mx-auto mt-12 p-6 bg-white rounded shadow">
      <h2 className="text-2xl font-bold mb-2">Join Group: {group.name}</h2>
      <p className="mb-4 text-muted-foreground">{group.description}</p>
      <div className="mb-4">
        <strong>Contribution:</strong> ₦{
          group.contribution_amount === undefined || group.contribution_amount === null
            ? 'N/A'
            : (typeof group.contribution_amount === 'number' && !isNaN(group.contribution_amount))
              ? group.contribution_amount.toLocaleString()
              : (typeof group.contribution_amount === 'string')
                ? group.contribution_amount // show string as-is (even if empty)
                : String(group.contribution_amount)
        }<br />
        <strong>Frequency:</strong> {group.frequency === undefined || group.frequency === null ? 'N/A' : group.frequency}<br />
        <strong>Members:</strong> {(group.current_members?.length ?? 0)}/{group.max_members === undefined || group.max_members === null ? 'N/A' : group.max_members}
      </div>
      {user ? (
        <Button
          disabled={joinMutation.isPending}
          onClick={() => joinMutation.mutate({ groupId, userId: user.id })}
          className="w-full"
        >
          {joinMutation.isPending ? 'Joining...' : 'Join Group'}
        </Button>
      ) : (
        <div className="text-red-500">You must be logged in to join this group.</div>
      )}
    </div>
  );
}
