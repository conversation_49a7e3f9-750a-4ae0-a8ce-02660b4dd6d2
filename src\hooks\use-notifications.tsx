
import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { toast } from 'sonner';
import { AlertCircle, CheckCircle, Info } from 'lucide-react';

export type NotificationType = 'success' | 'error' | 'info' | 'warning';
export type NotificationChannel = 'in-app' | 'email' | 'sms' | 'all';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  timestamp: Date;
  read: boolean;
  channel: NotificationChannel;
  priority?: 'low' | 'medium' | 'high';
  actionUrl?: string;
  userId?: string;
  metadata?: Record<string, any> | null;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearNotifications: () => void;
  deleteNotification: (id: string) => void;
  filterNotifications: (type?: NotificationType, channel?: NotificationChannel) => Notification[];
  fetchNotifications: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // Helper function to generate unique IDs
  const generateId = () => Math.random().toString(36).substr(2, 9);

  // Helper function to show toast
  const showNotificationToast = (title: string, message: string, type: NotificationType) => {
    switch (type) {
      case 'success':
        toast.success(title, {
          description: message,
        });
        break;
      case 'error':
        toast.error(title, {
          description: message,
        });
        break;
      case 'warning':
        toast(title, {
          description: message,
          icon: <AlertCircle className="h-5 w-5 text-amber-500" />,
        });
        break;
      case 'info':
      default:
        toast.info(title, {
          description: message,
        });
        break;
    }
  };

  // Update unread count when notifications change
  useEffect(() => {
    const count = notifications.filter(notification => !notification.read).length;
    setUnreadCount(count);
  }, [notifications]);

  // Fetch notifications (placeholder for future implementation)
  const fetchNotifications = async () => {
    // Currently just a placeholder since we're storing in memory
    console.log('Fetching notifications...');
  };

  // Add notification
  const addNotification = useCallback((notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: generateId(),
      timestamp: new Date(),
      read: false,
    };

    setNotifications(prev => [newNotification, ...prev]);
    
    // Show toast for new notification
    showNotificationToast(
      newNotification.title,
      newNotification.message,
      newNotification.type
    );
  }, []);

  // Mark as read
  const markAsRead = useCallback((id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, read: true } 
          : notification
      )
    );
  }, []);

  // Mark all as read
  const markAllAsRead = useCallback(() => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
  }, []);

  // Clear notifications
  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Delete notification
  const deleteNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  // Filter notifications
  const filterNotifications = useCallback((type?: NotificationType, channel?: NotificationChannel) => {
    let filtered = [...notifications];
    
    if (type) {
      filtered = filtered.filter(notification => notification.type === type);
    }
    
    if (channel) {
      filtered = filtered.filter(notification => 
        notification.channel === channel || notification.channel === 'all'
      );
    }
    
    return filtered;
  }, [notifications]);

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        addNotification,
        markAsRead,
        markAllAsRead,
        clearNotifications,
        deleteNotification,
        filterNotifications,
        fetchNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
