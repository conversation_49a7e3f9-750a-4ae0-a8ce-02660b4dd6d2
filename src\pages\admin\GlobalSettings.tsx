// Global Settings Admin Page for ASUSU by Koja
// Comprehensive admin interface for managing all global settings

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useAdminSettings } from '@/hooks/use-global-settings';
import {
  Settings,
  Palette,
  Image,
  Key,
  FileText,
  Shield,
  Phone,
  Save,
  RefreshCw,
  Upload,
  Eye,
  EyeOff,
  AlertCircle,
  CheckCircle,
  History
} from 'lucide-react';

export default function GlobalSettings() {
  const { toast } = useToast();
  const {
    adminSettings,
    loading,
    error,
    refreshAdminSettings,
    updateAppConfig,
    updateColors,
    updateBranding,
    updateAPIKeys,
    updateContent,
    updateSystem,
    updateContact
  } = useAdminSettings();

  const [activeTab, setActiveTab] = useState('app-config');
  const [showSecrets, setShowSecrets] = useState(false);
  const [formData, setFormData] = useState({
    appConfig: {
      appName: '',
      appDescription: '',
      appVersion: ''
    },
    colors: {
      primary: '#16A34A',
      secondary: '#15803D',
      accent: '#10B981',
      background: '#FFFFFF'
    },
    branding: {
      logoUrl: '',
      logoFilename: '',
      faviconUrl: '',
      faviconFilename: ''
    },
    apiKeys: {
      paystack: {
        publicKey: '',
        secretKey: '',
        isLive: false
      },
      aws: {
        accessKeyId: '',
        secretAccessKey: '',
        region: 'eu-north-1',
        s3Bucket: ''
      }
    },
    content: {
      landingPage: {
        heroTitle: '',
        heroSubtitle: ''
      },
      aboutPage: {
        title: '',
        content: ''
      }
    },
    system: {
      maintenanceMode: false,
      maintenanceMessage: '',
      allowRegistration: true,
      emailVerificationRequired: true,
      kycRequired: true,
      minimumDeposit: 500,
      maximumDeposit: 1000000
    },
    contact: {
      email: '',
      phone: '',
      address: '',
      socialMedia: {
        facebook: '',
        twitter: '',
        instagram: '',
        linkedin: ''
      }
    }
  });

  // Update form data when admin settings load
  useEffect(() => {
    if (adminSettings) {
      setFormData({
        appConfig: {
          appName: adminSettings.appName || '',
          appDescription: adminSettings.appDescription || '',
          appVersion: adminSettings.appVersion || ''
        },
        colors: {
          primary: adminSettings.colors?.primary || '#16A34A',
          secondary: adminSettings.colors?.secondary || '#15803D',
          accent: adminSettings.colors?.accent || '#10B981',
          background: adminSettings.colors?.background || '#FFFFFF'
        },
        branding: {
          logoUrl: adminSettings.logo?.url || '',
          logoFilename: adminSettings.logo?.filename || '',
          faviconUrl: adminSettings.favicon?.url || '',
          faviconFilename: adminSettings.favicon?.filename || ''
        },
        apiKeys: {
          paystack: {
            publicKey: adminSettings.apiKeys?.paystack?.publicKey || '',
            secretKey: adminSettings.apiKeys?.paystack?.secretKey || '',
            isLive: adminSettings.apiKeys?.paystack?.isLive || false
          },
          aws: {
            accessKeyId: adminSettings.apiKeys?.aws?.accessKeyId || '',
            secretAccessKey: adminSettings.apiKeys?.aws?.secretAccessKey || '',
            region: adminSettings.apiKeys?.aws?.region || 'eu-north-1',
            s3Bucket: adminSettings.apiKeys?.aws?.s3Bucket || ''
          }
        },
        content: {
          landingPage: {
            heroTitle: adminSettings.content?.landingPage?.heroTitle || '',
            heroSubtitle: adminSettings.content?.landingPage?.heroSubtitle || ''
          },
          aboutPage: {
            title: adminSettings.content?.aboutPage?.title || '',
            content: adminSettings.content?.aboutPage?.content || ''
          }
        },
        system: {
          maintenanceMode: adminSettings.system?.maintenanceMode || false,
          maintenanceMessage: adminSettings.system?.maintenanceMessage || '',
          allowRegistration: adminSettings.system?.allowRegistration ?? true,
          emailVerificationRequired: adminSettings.system?.emailVerificationRequired ?? true,
          kycRequired: adminSettings.system?.kycRequired ?? true,
          minimumDeposit: adminSettings.system?.minimumDeposit || 500,
          maximumDeposit: adminSettings.system?.maximumDeposit || 1000000
        },
        contact: {
          email: adminSettings.contact?.email || '',
          phone: adminSettings.contact?.phone || '',
          address: adminSettings.contact?.address || '',
          socialMedia: {
            facebook: adminSettings.contact?.socialMedia?.facebook || '',
            twitter: adminSettings.contact?.socialMedia?.twitter || '',
            instagram: adminSettings.contact?.socialMedia?.instagram || '',
            linkedin: adminSettings.contact?.socialMedia?.linkedin || ''
          }
        }
      });
    }
  }, [adminSettings]);

  const handleSaveAppConfig = async () => {
    const success = await updateAppConfig(formData.appConfig);
    if (success) {
      await refreshAdminSettings();
    }
  };

  const handleSaveColors = async () => {
    const success = await updateColors(formData.colors);
    if (success) {
      await refreshAdminSettings();
    }
  };

  const handleSaveBranding = async () => {
    const success = await updateBranding(formData.branding);
    if (success) {
      await refreshAdminSettings();
    }
  };

  const handleSaveAPIKeys = async () => {
    const success = await updateAPIKeys(formData.apiKeys);
    if (success) {
      await refreshAdminSettings();
    }
  };

  const handleSaveContent = async () => {
    const success = await updateContent(formData.content);
    if (success) {
      await refreshAdminSettings();
    }
  };

  const handleSaveSystem = async () => {
    const success = await updateSystem(formData.system);
    if (success) {
      await refreshAdminSettings();
    }
  };

  const handleSaveContact = async () => {
    const success = await updateContact(formData.contact);
    if (success) {
      await refreshAdminSettings();
    }
  };

  const validateHexColor = (color: string): boolean => {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-6 w-6 animate-spin text-brand-blue" />
          <span className="text-lg">Loading global settings...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center text-red-600">
              <AlertCircle className="h-5 w-5 mr-2" />
              Error Loading Settings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={refreshAdminSettings} className="w-full">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Global Settings</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your application configuration, branding, and integrations
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="flex items-center">
            <CheckCircle className="h-3 w-3 mr-1" />
            Version {adminSettings?.version || 1}
          </Badge>
          <Button variant="outline" onClick={refreshAdminSettings}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Settings Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3 md:grid-cols-7 mb-6">
          <TabsTrigger value="app-config" className="flex items-center">
            <Settings className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">App</span>
          </TabsTrigger>
          <TabsTrigger value="colors" className="flex items-center">
            <Palette className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">Colors</span>
          </TabsTrigger>
          <TabsTrigger value="branding" className="flex items-center">
            <Image className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">Branding</span>
          </TabsTrigger>
          <TabsTrigger value="api-keys" className="flex items-center">
            <Key className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">API Keys</span>
          </TabsTrigger>
          <TabsTrigger value="content" className="flex items-center">
            <FileText className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">Content</span>
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center">
            <Shield className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">System</span>
          </TabsTrigger>
          <TabsTrigger value="contact" className="flex items-center">
            <Phone className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">Contact</span>
          </TabsTrigger>
        </TabsList>

        {/* App Configuration Tab */}
        <TabsContent value="app-config" className="space-y-6">
          <Card className="card-3d">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2 text-brand-blue" />
                Application Configuration
              </CardTitle>
              <CardDescription>
                Configure basic application information and metadata
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="appName">Application Name</Label>
                  <Input
                    id="appName"
                    value={formData.appConfig.appName}
                    onChange={(e) => setFormData({
                      ...formData,
                      appConfig: { ...formData.appConfig, appName: e.target.value }
                    })}
                    placeholder="ASUSU by Koja"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="appVersion">Version</Label>
                  <Input
                    id="appVersion"
                    value={formData.appConfig.appVersion}
                    onChange={(e) => setFormData({
                      ...formData,
                      appConfig: { ...formData.appConfig, appVersion: e.target.value }
                    })}
                    placeholder="1.0.0"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="appDescription">Description</Label>
                <Textarea
                  id="appDescription"
                  value={formData.appConfig.appDescription}
                  onChange={(e) => setFormData({
                    ...formData,
                    appConfig: { ...formData.appConfig, appDescription: e.target.value }
                  })}
                  placeholder="Smart savings platform for Nigerians"
                  rows={3}
                />
              </div>
              <div className="flex justify-end">
                <Button onClick={handleSaveAppConfig} className="btn-3d">
                  <Save className="h-4 w-4 mr-2" />
                  Save Configuration
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Color Theme Tab */}
        <TabsContent value="colors" className="space-y-6">
          <Card className="card-3d">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Palette className="h-5 w-5 mr-2 text-brand-blue" />
                Color Theme Configuration
              </CardTitle>
              <CardDescription>
                Customize the application's color scheme and branding
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="primaryColor">Primary Color</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="primaryColor"
                      type="color"
                      value={formData.colors.primary}
                      onChange={(e) => setFormData({
                        ...formData,
                        colors: { ...formData.colors, primary: e.target.value }
                      })}
                      className="w-16 h-10 p-1 border rounded-[20px]"
                    />
                    <Input
                      value={formData.colors.primary}
                      onChange={(e) => setFormData({
                        ...formData,
                        colors: { ...formData.colors, primary: e.target.value }
                      })}
                      placeholder="#16A34A"
                      className={`flex-1 ${!validateHexColor(formData.colors.primary) ? 'border-red-500' : ''}`}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="secondaryColor">Secondary Color</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="secondaryColor"
                      type="color"
                      value={formData.colors.secondary}
                      onChange={(e) => setFormData({
                        ...formData,
                        colors: { ...formData.colors, secondary: e.target.value }
                      })}
                      className="w-16 h-10 p-1 border rounded-[20px]"
                    />
                    <Input
                      value={formData.colors.secondary}
                      onChange={(e) => setFormData({
                        ...formData,
                        colors: { ...formData.colors, secondary: e.target.value }
                      })}
                      placeholder="#15803D"
                      className={`flex-1 ${!validateHexColor(formData.colors.secondary) ? 'border-red-500' : ''}`}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="accentColor">Accent Color</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="accentColor"
                      type="color"
                      value={formData.colors.accent}
                      onChange={(e) => setFormData({
                        ...formData,
                        colors: { ...formData.colors, accent: e.target.value }
                      })}
                      className="w-16 h-10 p-1 border rounded-[20px]"
                    />
                    <Input
                      value={formData.colors.accent}
                      onChange={(e) => setFormData({
                        ...formData,
                        colors: { ...formData.colors, accent: e.target.value }
                      })}
                      placeholder="#10B981"
                      className={`flex-1 ${!validateHexColor(formData.colors.accent) ? 'border-red-500' : ''}`}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="backgroundColor">Background Color</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="backgroundColor"
                      type="color"
                      value={formData.colors.background}
                      onChange={(e) => setFormData({
                        ...formData,
                        colors: { ...formData.colors, background: e.target.value }
                      })}
                      className="w-16 h-10 p-1 border rounded-[20px]"
                    />
                    <Input
                      value={formData.colors.background}
                      onChange={(e) => setFormData({
                        ...formData,
                        colors: { ...formData.colors, background: e.target.value }
                      })}
                      placeholder="#FFFFFF"
                      className={`flex-1 ${!validateHexColor(formData.colors.background) ? 'border-red-500' : ''}`}
                    />
                  </div>
                </div>
              </div>

              {/* Color Preview */}
              <div className="mt-6 p-4 border rounded-[20px] bg-gray-50 dark:bg-gray-800">
                <h4 className="text-sm font-medium mb-3">Color Preview</h4>
                <div className="flex space-x-2">
                  <div
                    className="w-12 h-12 rounded-[20px] border-2 border-white shadow-md"
                    style={{ backgroundColor: formData.colors.primary }}
                    title="Primary"
                  />
                  <div
                    className="w-12 h-12 rounded-[20px] border-2 border-white shadow-md"
                    style={{ backgroundColor: formData.colors.secondary }}
                    title="Secondary"
                  />
                  <div
                    className="w-12 h-12 rounded-[20px] border-2 border-white shadow-md"
                    style={{ backgroundColor: formData.colors.accent }}
                    title="Accent"
                  />
                  <div
                    className="w-12 h-12 rounded-[20px] border-2 border-gray-300 shadow-md"
                    style={{ backgroundColor: formData.colors.background }}
                    title="Background"
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={handleSaveColors} className="btn-3d">
                  <Save className="h-4 w-4 mr-2" />
                  Apply Colors
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Branding Tab */}
        <TabsContent value="branding" className="space-y-6">
          <Card className="card-3d">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Image className="h-5 w-5 mr-2 text-brand-blue" />
                Branding & Assets
              </CardTitle>
              <CardDescription>
                Manage your application logo, favicon, and visual assets
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Logo Configuration</h4>
                  <div className="space-y-2">
                    <Label htmlFor="logoUrl">Logo URL</Label>
                    <Input
                      id="logoUrl"
                      value={formData.branding.logoUrl}
                      onChange={(e) => setFormData({
                        ...formData,
                        branding: { ...formData.branding, logoUrl: e.target.value }
                      })}
                      placeholder="https://example.com/logo.png"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="logoFilename">Logo Filename</Label>
                    <Input
                      id="logoFilename"
                      value={formData.branding.logoFilename}
                      onChange={(e) => setFormData({
                        ...formData,
                        branding: { ...formData.branding, logoFilename: e.target.value }
                      })}
                      placeholder="logo.png"
                    />
                  </div>
                  {formData.branding.logoUrl && (
                    <div className="mt-2">
                      <Label>Logo Preview</Label>
                      <div className="mt-1 p-4 border rounded-[20px] bg-gray-50 dark:bg-gray-800">
                        <img
                          src={formData.branding.logoUrl}
                          alt="Logo Preview"
                          className="max-h-16 object-contain"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Favicon Configuration</h4>
                  <div className="space-y-2">
                    <Label htmlFor="faviconUrl">Favicon URL</Label>
                    <Input
                      id="faviconUrl"
                      value={formData.branding.faviconUrl}
                      onChange={(e) => setFormData({
                        ...formData,
                        branding: { ...formData.branding, faviconUrl: e.target.value }
                      })}
                      placeholder="https://example.com/favicon.ico"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="faviconFilename">Favicon Filename</Label>
                    <Input
                      id="faviconFilename"
                      value={formData.branding.faviconFilename}
                      onChange={(e) => setFormData({
                        ...formData,
                        branding: { ...formData.branding, faviconFilename: e.target.value }
                      })}
                      placeholder="favicon.ico"
                    />
                  </div>
                  {formData.branding.faviconUrl && (
                    <div className="mt-2">
                      <Label>Favicon Preview</Label>
                      <div className="mt-1 p-4 border rounded-[20px] bg-gray-50 dark:bg-gray-800">
                        <img
                          src={formData.branding.faviconUrl}
                          alt="Favicon Preview"
                          className="w-8 h-8 object-contain"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={handleSaveBranding} className="btn-3d">
                  <Save className="h-4 w-4 mr-2" />
                  Save Branding
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* API Keys Tab */}
        <TabsContent value="api-keys" className="space-y-6">
          <Card className="card-3d">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Key className="h-5 w-5 mr-2 text-brand-blue" />
                API Keys & Integrations
              </CardTitle>
              <CardDescription>
                Configure external service integrations and API credentials
              </CardDescription>
              <div className="flex items-center space-x-2 mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowSecrets(!showSecrets)}
                >
                  {showSecrets ? <EyeOff className="h-4 w-4 mr-1" /> : <Eye className="h-4 w-4 mr-1" />}
                  {showSecrets ? 'Hide' : 'Show'} Secrets
                </Button>
                <Badge variant="destructive" className="text-xs">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Sensitive Data
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Paystack Configuration */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <h4 className="font-medium">Paystack Configuration</h4>
                  <Badge variant={formData.apiKeys.paystack.isLive ? "default" : "secondary"}>
                    {formData.apiKeys.paystack.isLive ? "Live" : "Test"}
                  </Badge>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="paystackPublicKey">Public Key</Label>
                    <Input
                      id="paystackPublicKey"
                      type={showSecrets ? "text" : "password"}
                      value={formData.apiKeys.paystack.publicKey}
                      onChange={(e) => setFormData({
                        ...formData,
                        apiKeys: {
                          ...formData.apiKeys,
                          paystack: { ...formData.apiKeys.paystack, publicKey: e.target.value }
                        }
                      })}
                      placeholder="pk_test_..."
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="paystackSecretKey">Secret Key</Label>
                    <Input
                      id="paystackSecretKey"
                      type={showSecrets ? "text" : "password"}
                      value={formData.apiKeys.paystack.secretKey}
                      onChange={(e) => setFormData({
                        ...formData,
                        apiKeys: {
                          ...formData.apiKeys,
                          paystack: { ...formData.apiKeys.paystack, secretKey: e.target.value }
                        }
                      })}
                      placeholder="sk_test_..."
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="paystackLive"
                    checked={formData.apiKeys.paystack.isLive}
                    onCheckedChange={(checked) => setFormData({
                      ...formData,
                      apiKeys: {
                        ...formData.apiKeys,
                        paystack: { ...formData.apiKeys.paystack, isLive: checked }
                      }
                    })}
                  />
                  <Label htmlFor="paystackLive">Use Live Environment</Label>
                </div>
              </div>

              <Separator />

              {/* AWS Configuration */}
              <div className="space-y-4">
                <h4 className="font-medium">AWS Configuration</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="awsAccessKey">Access Key ID</Label>
                    <Input
                      id="awsAccessKey"
                      type={showSecrets ? "text" : "password"}
                      value={formData.apiKeys.aws.accessKeyId}
                      onChange={(e) => setFormData({
                        ...formData,
                        apiKeys: {
                          ...formData.apiKeys,
                          aws: { ...formData.apiKeys.aws, accessKeyId: e.target.value }
                        }
                      })}
                      placeholder="AKIA..."
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="awsSecretKey">Secret Access Key</Label>
                    <Input
                      id="awsSecretKey"
                      type={showSecrets ? "text" : "password"}
                      value={formData.apiKeys.aws.secretAccessKey}
                      onChange={(e) => setFormData({
                        ...formData,
                        apiKeys: {
                          ...formData.apiKeys,
                          aws: { ...formData.apiKeys.aws, secretAccessKey: e.target.value }
                        }
                      })}
                      placeholder="Secret key..."
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="awsRegion">Region</Label>
                    <Input
                      id="awsRegion"
                      value={formData.apiKeys.aws.region}
                      onChange={(e) => setFormData({
                        ...formData,
                        apiKeys: {
                          ...formData.apiKeys,
                          aws: { ...formData.apiKeys.aws, region: e.target.value }
                        }
                      })}
                      placeholder="eu-north-1"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="awsS3Bucket">S3 Bucket</Label>
                    <Input
                      id="awsS3Bucket"
                      value={formData.apiKeys.aws.s3Bucket}
                      onChange={(e) => setFormData({
                        ...formData,
                        apiKeys: {
                          ...formData.apiKeys,
                          aws: { ...formData.apiKeys.aws, s3Bucket: e.target.value }
                        }
                      })}
                      placeholder="my-bucket-name"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={handleSaveAPIKeys} className="btn-3d">
                  <Save className="h-4 w-4 mr-2" />
                  Save API Keys
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Content Management Tab */}
        <TabsContent value="content" className="space-y-6">
          <Card className="card-3d">
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2 text-brand-blue" />
                Content Management
              </CardTitle>
              <CardDescription>
                Manage frontend content, text, and images across the application
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Landing Page Content */}
              <div className="space-y-4">
                <h4 className="font-medium">Landing Page Content</h4>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="heroTitle">Hero Title</Label>
                    <Input
                      id="heroTitle"
                      value={formData.content.landingPage.heroTitle}
                      onChange={(e) => setFormData({
                        ...formData,
                        content: {
                          ...formData.content,
                          landingPage: { ...formData.content.landingPage, heroTitle: e.target.value }
                        }
                      })}
                      placeholder="Save Smarter with ASUSU"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="heroSubtitle">Hero Subtitle</Label>
                    <Textarea
                      id="heroSubtitle"
                      value={formData.content.landingPage.heroSubtitle}
                      onChange={(e) => setFormData({
                        ...formData,
                        content: {
                          ...formData.content,
                          landingPage: { ...formData.content.landingPage, heroSubtitle: e.target.value }
                        }
                      })}
                      placeholder="Traditional Nigerian savings meets modern technology"
                      rows={2}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* About Page Content */}
              <div className="space-y-4">
                <h4 className="font-medium">About Page Content</h4>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="aboutTitle">About Title</Label>
                    <Input
                      id="aboutTitle"
                      value={formData.content.aboutPage.title}
                      onChange={(e) => setFormData({
                        ...formData,
                        content: {
                          ...formData.content,
                          aboutPage: { ...formData.content.aboutPage, title: e.target.value }
                        }
                      })}
                      placeholder="About ASUSU"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="aboutContent">About Content</Label>
                    <Textarea
                      id="aboutContent"
                      value={formData.content.aboutPage.content}
                      onChange={(e) => setFormData({
                        ...formData,
                        content: {
                          ...formData.content,
                          aboutPage: { ...formData.content.aboutPage, content: e.target.value }
                        }
                      })}
                      placeholder="ASUSU brings traditional Nigerian savings into the digital age."
                      rows={4}
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={handleSaveContent} className="btn-3d">
                  <Save className="h-4 w-4 mr-2" />
                  Save Content
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* System Configuration Tab */}
        <TabsContent value="system" className="space-y-6">
          <Card className="card-3d">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2 text-brand-blue" />
                System Configuration
              </CardTitle>
              <CardDescription>
                Configure system-wide settings, security, and operational parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Maintenance Mode */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Maintenance Mode</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Enable to temporarily disable user access
                    </p>
                  </div>
                  <Switch
                    checked={formData.system.maintenanceMode}
                    onCheckedChange={(checked) => setFormData({
                      ...formData,
                      system: { ...formData.system, maintenanceMode: checked }
                    })}
                  />
                </div>
                {formData.system.maintenanceMode && (
                  <div className="space-y-2">
                    <Label htmlFor="maintenanceMessage">Maintenance Message</Label>
                    <Textarea
                      id="maintenanceMessage"
                      value={formData.system.maintenanceMessage}
                      onChange={(e) => setFormData({
                        ...formData,
                        system: { ...formData.system, maintenanceMessage: e.target.value }
                      })}
                      placeholder="System is under maintenance. Please check back later."
                      rows={2}
                    />
                  </div>
                )}
              </div>

              <Separator />

              {/* User Registration Settings */}
              <div className="space-y-4">
                <h4 className="font-medium">User Registration Settings</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="allowRegistration">Allow New Registrations</Label>
                    <Switch
                      id="allowRegistration"
                      checked={formData.system.allowRegistration}
                      onCheckedChange={(checked) => setFormData({
                        ...formData,
                        system: { ...formData.system, allowRegistration: checked }
                      })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="emailVerification">Require Email Verification</Label>
                    <Switch
                      id="emailVerification"
                      checked={formData.system.emailVerificationRequired}
                      onCheckedChange={(checked) => setFormData({
                        ...formData,
                        system: { ...formData.system, emailVerificationRequired: checked }
                      })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="kycRequired">Require KYC Verification</Label>
                    <Switch
                      id="kycRequired"
                      checked={formData.system.kycRequired}
                      onCheckedChange={(checked) => setFormData({
                        ...formData,
                        system: { ...formData.system, kycRequired: checked }
                      })}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Transaction Limits */}
              <div className="space-y-4">
                <h4 className="font-medium">Transaction Limits</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="minimumDeposit">Minimum Deposit (₦)</Label>
                    <Input
                      id="minimumDeposit"
                      type="number"
                      value={formData.system.minimumDeposit}
                      onChange={(e) => setFormData({
                        ...formData,
                        system: { ...formData.system, minimumDeposit: Number(e.target.value) }
                      })}
                      placeholder="500"
                      min="0"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maximumDeposit">Maximum Deposit (₦)</Label>
                    <Input
                      id="maximumDeposit"
                      type="number"
                      value={formData.system.maximumDeposit}
                      onChange={(e) => setFormData({
                        ...formData,
                        system: { ...formData.system, maximumDeposit: Number(e.target.value) }
                      })}
                      placeholder="1000000"
                      min="0"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={handleSaveSystem} className="btn-3d">
                  <Save className="h-4 w-4 mr-2" />
                  Save System Config
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Contact Information Tab */}
        <TabsContent value="contact" className="space-y-6">
          <Card className="card-3d">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Phone className="h-5 w-5 mr-2 text-brand-blue" />
                Contact Information
              </CardTitle>
              <CardDescription>
                Manage contact details and social media links
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Contact Info */}
              <div className="space-y-4">
                <h4 className="font-medium">Basic Contact Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="contactEmail">Email Address</Label>
                    <Input
                      id="contactEmail"
                      type="email"
                      value={formData.contact.email}
                      onChange={(e) => setFormData({
                        ...formData,
                        contact: { ...formData.contact, email: e.target.value }
                      })}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="contactPhone">Phone Number</Label>
                    <Input
                      id="contactPhone"
                      value={formData.contact.phone}
                      onChange={(e) => setFormData({
                        ...formData,
                        contact: { ...formData.contact, phone: e.target.value }
                      })}
                      placeholder="+234-XXX-XXX-XXXX"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contactAddress">Address</Label>
                  <Textarea
                    id="contactAddress"
                    value={formData.contact.address}
                    onChange={(e) => setFormData({
                      ...formData,
                      contact: { ...formData.contact, address: e.target.value }
                    })}
                    placeholder="Lagos, Nigeria"
                    rows={2}
                  />
                </div>
              </div>

              <Separator />

              {/* Social Media Links */}
              <div className="space-y-4">
                <h4 className="font-medium">Social Media Links</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="facebook">Facebook</Label>
                    <Input
                      id="facebook"
                      value={formData.contact.socialMedia.facebook}
                      onChange={(e) => setFormData({
                        ...formData,
                        contact: {
                          ...formData.contact,
                          socialMedia: { ...formData.contact.socialMedia, facebook: e.target.value }
                        }
                      })}
                      placeholder="https://facebook.com/yourpage"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="twitter">Twitter</Label>
                    <Input
                      id="twitter"
                      value={formData.contact.socialMedia.twitter}
                      onChange={(e) => setFormData({
                        ...formData,
                        contact: {
                          ...formData.contact,
                          socialMedia: { ...formData.contact.socialMedia, twitter: e.target.value }
                        }
                      })}
                      placeholder="https://twitter.com/yourhandle"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="instagram">Instagram</Label>
                    <Input
                      id="instagram"
                      value={formData.contact.socialMedia.instagram}
                      onChange={(e) => setFormData({
                        ...formData,
                        contact: {
                          ...formData.contact,
                          socialMedia: { ...formData.contact.socialMedia, instagram: e.target.value }
                        }
                      })}
                      placeholder="https://instagram.com/yourhandle"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="linkedin">LinkedIn</Label>
                    <Input
                      id="linkedin"
                      value={formData.contact.socialMedia.linkedin}
                      onChange={(e) => setFormData({
                        ...formData,
                        contact: {
                          ...formData.contact,
                          socialMedia: { ...formData.contact.socialMedia, linkedin: e.target.value }
                        }
                      })}
                      placeholder="https://linkedin.com/company/yourcompany"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={handleSaveContact} className="btn-3d">
                  <Save className="h-4 w-4 mr-2" />
                  Save Contact Info
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
