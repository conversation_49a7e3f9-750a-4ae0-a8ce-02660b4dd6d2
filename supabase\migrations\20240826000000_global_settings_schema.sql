-- Global Settings Schema for Supabase
-- This migration creates tables for storing global application settings

-- Create enum for content types
CREATE TYPE public.content_type AS ENUM ('text', 'image', 'video', 'html');

-- Create enum for setting categories
CREATE TYPE public.setting_category AS ENUM (
  'app_config', 
  'branding', 
  'colors', 
  'api_keys', 
  'content', 
  'system', 
  'contact'
);

-- Main global settings table
CREATE TABLE IF NOT EXISTS public.global_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category setting_category NOT NULL,
  key TEXT NOT NULL,
  value JSONB,
  data_type TEXT DEFAULT 'string',
  is_encrypted BOOLEAN DEFAULT false,
  is_public BOOLEAN DEFAULT true,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_by UUID REFERENCES auth.users(id),
  version INTEGER DEFAULT 1,
  UNIQUE(category, key)
);

-- Settings history table for audit trail
CREATE TABLE IF NOT EXISTS public.settings_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  setting_id UUID REFERENCES public.global_settings(id) ON DELETE CASCADE,
  old_value JSONB,
  new_value JSONB,
  changed_by UUID REFERENCES auth.users(id),
  change_reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Content management table for frontend sections
CREATE TABLE IF NOT EXISTS public.content_sections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  section_key TEXT UNIQUE NOT NULL,
  title TEXT NOT NULL,
  content JSONB NOT NULL,
  content_type content_type DEFAULT 'text',
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_by UUID REFERENCES auth.users(id)
);

-- Media assets table for logos, images, etc.
CREATE TABLE IF NOT EXISTS public.media_assets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  filename TEXT NOT NULL,
  original_filename TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size INTEGER,
  mime_type TEXT,
  alt_text TEXT,
  usage_context TEXT, -- 'logo', 'hero_image', 'feature_image', etc.
  is_active BOOLEAN DEFAULT true,
  metadata JSONB DEFAULT '{}',
  uploaded_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- API keys table (encrypted storage)
CREATE TABLE IF NOT EXISTS public.api_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  service_name TEXT NOT NULL,
  key_type TEXT NOT NULL, -- 'public', 'secret', 'webhook'
  encrypted_value TEXT NOT NULL,
  is_live BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  expires_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_by UUID REFERENCES auth.users(id),
  UNIQUE(service_name, key_type)
);

-- Insert default settings
INSERT INTO public.global_settings (category, key, value, description, is_public) VALUES
-- App Configuration
('app_config', 'app_name', '"ASUSU by Koja"', 'Application name', true),
('app_config', 'app_description', '"Smart savings platform for Nigerians"', 'Application description', true),
('app_config', 'app_version', '"1.0.0"', 'Application version', true),

-- Branding
('branding', 'logo_url', 'null', 'Main application logo URL', true),
('branding', 'favicon_url', 'null', 'Favicon URL', true),
('branding', 'brand_tagline', '"Save Smarter with ASUSU"', 'Brand tagline', true),

-- Colors
('colors', 'primary_color', '"#16A34A"', 'Primary brand color (Green)', true),
('colors', 'secondary_color', '"#15803D"', 'Secondary brand color (Dark Green)', true),
('colors', 'accent_color', '"#10B981"', 'Accent color (Emerald)', true),
('colors', 'background_color', '"#FFFFFF"', 'Background color', true),

-- System Configuration
('system', 'maintenance_mode', 'false', 'Enable maintenance mode', false),
('system', 'maintenance_message', '"System is under maintenance. Please check back later."', 'Maintenance mode message', false),
('system', 'allow_registration', 'true', 'Allow new user registration', false),
('system', 'email_verification_required', 'true', 'Require email verification', false),
('system', 'kyc_required', 'true', 'Require KYC verification', false),
('system', 'minimum_deposit', '500', 'Minimum deposit amount', false),
('system', 'maximum_deposit', '1000000', 'Maximum deposit amount', false),

-- Contact Information
('contact', 'support_email', '"<EMAIL>"', 'Support email address', true),
('contact', 'support_phone', '"+234-XXX-XXX-XXXX"', 'Support phone number', true),
('contact', 'company_address', '"Lagos, Nigeria"', 'Company address', true),
('contact', 'social_media', '{"facebook": "", "twitter": "", "instagram": "", "linkedin": ""}', 'Social media links', true);

-- Insert default content sections
INSERT INTO public.content_sections (section_key, title, content, content_type) VALUES
('hero_section', 'Hero Section', '{
  "title": "Save Smarter with ASUSU",
  "subtitle": "Traditional Nigerian savings meets modern technology",
  "cta_text": "Get Started",
  "background_image": null
}', 'html'),

('features_section', 'Features Section', '{
  "title": "How It Works",
  "features": [
    {
      "title": "Create Account",
      "description": "Sign up for free and set up your profile in minutes",
      "icon": "users"
    },
    {
      "title": "Set Your Goals",
      "description": "Define what you are saving for and set your target amount",
      "icon": "target"
    },
    {
      "title": "Make Daily Deposits",
      "description": "Contribute as little as ₦500 daily to grow your savings",
      "icon": "credit-card"
    },
    {
      "title": "Withdraw When Ready",
      "description": "Access your funds when you reach your goals",
      "icon": "piggy-bank"
    }
  ]
}', 'html'),

('testimonials_section', 'Testimonials Section', '{
  "title": "What Our Users Say",
  "subtitle": "Hear from Nigerians who have transformed their financial habits with ASUSU",
  "testimonials": [
    {
      "name": "Adebayo Johnson",
      "role": "Small Business Owner",
      "content": "ASUSU helped me save for my business expansion. The daily savings approach made it so much easier!",
      "rating": 5
    },
    {
      "name": "Fatima Mohammed",
      "role": "Teacher",
      "content": "I love how ASUSU brings the traditional kolo system online. It is secure and convenient.",
      "rating": 5
    },
    {
      "name": "Chinedu Okafor",
      "role": "Engineer",
      "content": "The group savings feature is amazing. My colleagues and I saved together for our project.",
      "rating": 5
    }
  ]
}', 'html');

-- Create indexes for better performance
CREATE INDEX idx_global_settings_category ON public.global_settings(category);
CREATE INDEX idx_global_settings_key ON public.global_settings(key);
CREATE INDEX idx_global_settings_public ON public.global_settings(is_public);
CREATE INDEX idx_content_sections_active ON public.content_sections(is_active);
CREATE INDEX idx_content_sections_sort ON public.content_sections(sort_order);
CREATE INDEX idx_media_assets_context ON public.media_assets(usage_context);
CREATE INDEX idx_api_keys_service ON public.api_keys(service_name);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  NEW.version = OLD.version + 1;
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_global_settings_updated_at 
  BEFORE UPDATE ON public.global_settings 
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_content_sections_updated_at 
  BEFORE UPDATE ON public.content_sections 
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_api_keys_updated_at 
  BEFORE UPDATE ON public.api_keys 
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Create function to log settings changes
CREATE OR REPLACE FUNCTION public.log_settings_change()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.settings_history (setting_id, old_value, new_value, changed_by)
  VALUES (NEW.id, OLD.value, NEW.value, NEW.updated_by);
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for settings history
CREATE TRIGGER log_global_settings_changes 
  AFTER UPDATE ON public.global_settings 
  FOR EACH ROW EXECUTE FUNCTION public.log_settings_change();

-- Enable RLS (Row Level Security)
ALTER TABLE public.global_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.settings_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.media_assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.api_keys ENABLE ROW LEVEL SECURITY;

-- Create policies for global_settings
CREATE POLICY "Public settings are viewable by everyone" ON public.global_settings
  FOR SELECT USING (is_public = true);

CREATE POLICY "Admin can view all settings" ON public.global_settings
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Admin can update settings" ON public.global_settings
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Create policies for content_sections
CREATE POLICY "Active content is viewable by everyone" ON public.content_sections
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admin can manage content" ON public.content_sections
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Create policies for media_assets
CREATE POLICY "Active media is viewable by everyone" ON public.media_assets
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admin can manage media" ON public.media_assets
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Create policies for api_keys (admin only)
CREATE POLICY "Admin can manage API keys" ON public.api_keys
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Create policies for settings_history (admin only)
CREATE POLICY "Admin can view settings history" ON public.settings_history
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );
