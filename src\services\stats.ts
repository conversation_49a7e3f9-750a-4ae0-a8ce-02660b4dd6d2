const BACKEND_URL = import.meta.env.VITE_BACKEND_URL;

export const StatsService = {
  getUserSavingsSummary: async () => {
    try {
      let token = localStorage.getItem('token');
      if (!token) token = localStorage.getItem('access_token');
      const res = await fetch(`${BACKEND_URL}/api/savings/summary`, {
        headers: token ? { 'Authorization': `Bearer ${token}` } : {}
      });
      if (!res.ok) throw new Error('Failed to fetch user savings summary');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching user savings summary:', error);
      return { data: null, error };
    }
  },

  getUserAchievements: async () => {
    try {
      let token = localStorage.getItem('token');
      if (!token) token = localStorage.getItem('access_token');
      const res = await fetch(`${BACKEND_URL}/api/savings/achievements`, {
        headers: token ? { 'Authorization': `Bearer ${token}` } : {}
      });
      if (!res.ok) throw new Error('Failed to fetch user achievements');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching user achievements:', error);
      return { data: null, error };
    }
  },

  getUserSavingsHistory: async () => {
    try {
      let token = localStorage.getItem('token');
      if (!token) token = localStorage.getItem('access_token');
      const res = await fetch(`${BACKEND_URL}/api/savings/history`, {
        headers: token ? { 'Authorization': `Bearer ${token}` } : {}
      });
      if (!res.ok) throw new Error('Failed to fetch user savings history');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching user savings history:', error);
      return { data: null, error };
    }
  },

  getUserTimeline: async () => {
    try {
      let token = localStorage.getItem('token');
      if (!token) token = localStorage.getItem('access_token');
      const res = await fetch(`${BACKEND_URL}/api/savings/timeline`, {
        headers: token ? { 'Authorization': `Bearer ${token}` } : {}
      });
      if (!res.ok) throw new Error('Failed to fetch user timeline');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching user timeline:', error);
      return { data: null, error };
    }
  },
};
