
import React from 'react';
import { createRoot } from 'react-dom/client';
import { ThemeProvider } from './hooks/use-theme';
import { NotificationProvider } from './hooks/use-notifications';
import App from './App.tsx';
import './index.css';

const container = document.getElementById("root");
if (!container) {
  throw new Error("Root element not found");
}

const root = createRoot(container);
root.render(
  <React.StrictMode>
    <ThemeProvider>
      <NotificationProvider>
        <App />
      </NotificationProvider>
    </ThemeProvider>
  </React.StrictMode>
);
