
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Search, Phone, Mail, MessageCircle, HelpCircle, Book, CreditCard, Shield, Settings } from 'lucide-react';

const HelpCenter = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const faqData = [
    {
      category: 'Account & Security',
      icon: <Shield className="h-5 w-5" />,
      questions: [
        {
          question: 'How do I reset my password?',
          answer: 'You can reset your password by clicking "Forgot Password" on the login page. Enter your email address and follow the instructions sent to your email.'
        },
        {
          question: 'How do I enable two-factor authentication?',
          answer: 'Go to Settings > Security > Two-Factor Authentication. Follow the setup process using your preferred authenticator app.'
        },
        {
          question: 'Why is my account locked?',
          answer: 'Accounts are locked after multiple failed login attempts for security. Wait 30 minutes or contact support to unlock immediately.'
        }
      ]
    },
    {
      category: 'Savings Plans',
      icon: <CreditCard className="h-5 w-5" />,
      questions: [
        {
          question: 'How do I create a savings plan?',
          answer: 'Navigate to Savings Plans > Create New Plan. Choose your plan type, set target amount, duration, and contribution frequency.'
        },
        {
          question: 'Can I modify my savings plan?',
          answer: 'Yes, you can modify active savings plans by going to your plan details and clicking "Edit Plan". Some restrictions may apply.'
        },
        {
          question: 'What happens if I miss a contribution?',
          answer: 'Missing contributions may affect your savings timeline. You can make up missed payments or adjust your plan accordingly.'
        }
      ]
    },
    {
      category: 'Payments & Withdrawals',
      icon: <CreditCard className="h-5 w-5" />,
      questions: [
        {
          question: 'How long do withdrawals take?',
          answer: 'Withdrawals typically process within 1-3 business days. Emergency withdrawals may have additional fees.'
        },
        {
          question: 'What payment methods are accepted?',
          answer: 'We accept bank transfers, debit cards, and mobile money payments. Credit cards are not currently supported.'
        },
        {
          question: 'Are there withdrawal limits?',
          answer: 'Daily withdrawal limits vary by account type and verification level. Check your account settings for current limits.'
        }
      ]
    }
  ];

  const quickActions = [
    { title: 'Contact Support', description: 'Get help from our support team', icon: <Phone className="h-5 w-5" />, action: 'contact' },
    { title: 'Submit Ticket', description: 'Report an issue or request help', icon: <MessageCircle className="h-5 w-5" />, action: 'ticket' },
    { title: 'User Guide', description: 'Learn how to use ASUSU', icon: <Book className="h-5 w-5" />, action: 'guide' },
    { title: 'Account Settings', description: 'Manage your account preferences', icon: <Settings className="h-5 w-5" />, action: 'settings' }
  ];

  const filteredFAQ = faqData.map(category => ({
    ...category,
    questions: category.questions.filter(q => 
      q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      q.answer.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => category.questions.length > 0);

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      <div className="text-center mb-8" data-aos="fade-up">
        <h1 className="text-3xl font-bold mb-2">Help Center</h1>
        <p className="text-muted-foreground">Find answers to your questions and get support</p>
      </div>

      {/* Search Bar */}
      <div className="relative mb-8 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search for help articles, FAQs, or guides..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 h-12"
        />
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {quickActions.map((action, index) => (
          <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow" data-aos="fade-up" data-aos-delay={100 + index * 50}>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                {action.icon}
                <CardTitle className="text-sm">{action.title}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-xs">{action.description}</CardDescription>
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs defaultValue="faq" className="w-full" data-aos="fade-up" data-aos-delay="300">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="faq">FAQ</TabsTrigger>
          <TabsTrigger value="contact">Contact Support</TabsTrigger>
          <TabsTrigger value="guides">User Guides</TabsTrigger>
        </TabsList>

        <TabsContent value="faq" className="mt-6">
          <div className="space-y-6">
            {filteredFAQ.map((category, categoryIndex) => (
              <Card key={categoryIndex} data-aos="fade-up" data-aos-delay={categoryIndex * 100}>
                <CardHeader>
                  <div className="flex items-center gap-2">
                    {category.icon}
                    <CardTitle className="text-lg">{category.category}</CardTitle>
                    <Badge variant="secondary">{category.questions.length}</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <Accordion type="single" collapsible className="w-full">
                    {category.questions.map((faq, faqIndex) => (
                      <AccordionItem key={faqIndex} value={`${categoryIndex}-${faqIndex}`}>
                        <AccordionTrigger className="text-left">
                          {faq.question}
                        </AccordionTrigger>
                        <AccordionContent>
                          <p className="text-muted-foreground">{faq.answer}</p>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="contact" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card data-aos="fade-right">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Phone className="h-5 w-5" />
                  Phone Support
                </CardTitle>
                <CardDescription>Speak with our support team directly</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="font-medium">Customer Support</p>
                  <p className="text-muted-foreground">+234 800 ASUSU (27878)</p>
                  <p className="text-sm text-muted-foreground">Mon-Fri 8AM-6PM WAT</p>
                </div>
                <Button className="w-full">Call Support</Button>
              </CardContent>
            </Card>

            <Card data-aos="fade-left">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  Email Support
                </CardTitle>
                <CardDescription>Send us a detailed message</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="font-medium">Support Email</p>
                  <p className="text-muted-foreground"><EMAIL></p>
                  <p className="text-sm text-muted-foreground">Response within 24 hours</p>
                </div>
                <Button className="w-full">Send Email</Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="guides" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              { title: 'Getting Started', description: 'Learn the basics of using ASUSU', duration: '5 min read' },
              { title: 'Creating Savings Plans', description: 'Step-by-step guide to setting up your first savings plan', duration: '8 min read' },
              { title: 'Managing Payments', description: 'How to add payment methods and make transactions', duration: '6 min read' },
              { title: 'Group Savings', description: 'Join or create group savings plans with friends', duration: '10 min read' },
              { title: 'Security Best Practices', description: 'Keep your account safe and secure', duration: '7 min read' },
              { title: 'Troubleshooting', description: 'Common issues and how to resolve them', duration: '12 min read' }
            ].map((guide, index) => (
              <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow" data-aos="fade-up" data-aos-delay={index * 50}>
                <CardHeader>
                  <CardTitle className="text-base">{guide.title}</CardTitle>
                  <CardDescription className="text-sm">{guide.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Badge variant="outline" className="text-xs">{guide.duration}</Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default HelpCenter;
