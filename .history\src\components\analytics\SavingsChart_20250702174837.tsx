
import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>ltip, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts";

// Accepts data as prop, falls back to sample if not provided
type ChartDatum = { month: string; amount: number };
interface SavingsChartProps {
  data?: ChartDatum[];
}

const sampleData: ChartDatum[] = [
  { month: "Jan", amount: 50000 },
  { month: "Feb", amount: 60000 },
  { month: "Mar", amount: 55000 },
  { month: "Apr", amount: 75000 },
  { month: "May", amount: 40000 },
  { month: "Jun", amount: 60000 },
];

export function SavingsChart({ data }: SavingsChartProps) {
  // Format number to Naira with K/M suffix for readability
  const formatAmount = (amount: number) => {
    if (amount >= 1000000) {
      return `₦${(amount / 1000000).toFixed(1)}M`;
    }
    if (amount >= 1000) {
      return `₦${(amount / 1000).toFixed(0)}K`;
    }
    return `₦${amount}`;
  };

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-2 border rounded shadow-sm">
          <p className="font-medium">{label}</p>
          <p className="text-brand-blue">
            Amount: <span className="font-medium">₦{payload[0].value.toLocaleString()}</span>
          </p>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="w-full h-72">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data && data.length > 0 ? data : sampleData}
          margin={{ top: 10, right: 30, left: 0, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="month" />
          <YAxis tickFormatter={formatAmount} />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="amount" fill="#1231B8" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
