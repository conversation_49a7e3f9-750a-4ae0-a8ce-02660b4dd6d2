const express = require('express');
const axios = require('axios');
const router = express.Router();

const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY;
const PAYSTACK_BASE_URL = process.env.PAYSTACK_BASE_URL || 'https://api.paystack.co';

// Initialize a Paystack payment (get payment link)
router.post('/initialize', async (req, res) => {
  const { amount, email, user_id, metadata } = req.body;
  console.log('[Paystack] POST /deposit/initialize', {
    amount, email, user_id, PAYSTACK_SECRET_KEY: !!PAYSTACK_SECRET_KEY, PAYSTACK_BASE_URL
  });
  if (!amount || !email) {
    return res.status(400).json({ status: false, message: 'Amount and email are required' });
  }
  try {
    // Set redirect_url to frontend site with success param
    const redirect_url = `${process.env.FRONTEND_URL || 'http://localhost:8030'}/user/payments?deposit=success`;
    const response = await axios.post(`${PAYSTACK_BASE_URL}/transaction/initialize`, {
      amount: Math.round(Number(amount)),
      email,
      metadata: { user_id, ...metadata },
      currency: 'NGN',
      redirect_url,
    }, {
      headers: {
        Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
    });
    res.json(response.data);
  } catch (error) {
    console.error('[Paystack] Error initializing deposit:', error.response?.data || error.message);
    res.status(500).json({ status: false, message: 'Failed to initialize deposit', error: error.message, details: error.response?.data });
  }
});

// Verify a Paystack payment
router.get('/verify/:reference', async (req, res) => {
  const { reference } = req.params;
  console.log('[Paystack] GET /deposit/verify', { reference, PAYSTACK_SECRET_KEY: !!PAYSTACK_SECRET_KEY, PAYSTACK_BASE_URL });
  if (!reference) {
    return res.status(400).json({ status: false, message: 'Reference is required' });
  }
  try {
    const response = await axios.get(`${PAYSTACK_BASE_URL}/transaction/verify/${reference}`, {
      headers: {
        Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
      },
    });
    res.json(response.data);
  } catch (error) {
    console.error('[Paystack] Error verifying deposit:', error.response?.data || error.message);
    res.status(500).json({ status: false, message: 'Failed to verify deposit', error: error.message, details: error.response?.data });
  }
});

module.exports = router;
