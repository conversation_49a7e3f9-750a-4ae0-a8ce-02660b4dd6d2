const express = require('express');
const router = express.Router();
const User = require('../models/user');

// Paystack webhook endpoint to update user balance
router.post('/paystack/webhook', async (req, res) => {
  try {
    // Paystack sends events as JSON
    const event = req.body;
    // Only handle successful charge events
    if (event.event === 'charge.success' && event.data && event.data.status === 'success') {
      // Extract userId and amount from metadata (set this in your Paystack payment initialization)
      const metadata = event.data.metadata || {};
      const userId = metadata.userId;
      const amount = event.data.amount; // Paystack sends amount in kobo
      if (!userId || !amount) {
        return res.status(400).json({ error: 'Missing userId or amount in webhook' });
      }
      // Convert amount from kobo to naira
      const nairaAmount = amount / 100;
      // Update user balance atomically
      const user = await User.findByIdAndUpdate(
        userId,
        { $inc: { balance: nairaAmount } },
        { new: true }
      );
      if (!user) return res.status(404).json({ error: 'User not found' });
      return res.status(200).json({ success: true, balance: user.balance });
    }
    // Ignore other events
    res.status(200).json({ received: true });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

module.exports = router;
