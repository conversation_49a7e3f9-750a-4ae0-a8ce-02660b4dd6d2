
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.42.7";

const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

// Create a Supabase client with the service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// CORS headers for browser requests
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface SignUpData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
  is_admin?: boolean;
}

interface SignInData {
  email: string;
  password: string;
}

interface PasswordResetData {
  email: string;
}

interface UpdatePasswordData {
  access_token: string;
  new_password: string;
}

interface CreateAdminData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: corsHeaders,
      status: 204,
    });
  }

  try {
    const url = new URL(req.url);
    const action = url.searchParams.get("action");
    
    // Get the request body
    let data;
    if (req.method === "POST") {
      data = await req.json();
      // If action wasn't in URL params, check body
      if (!action && data.action) {
        console.log(`Auth function called with action from body: ${data.action}`);
      }
    }
    
    const actionToUse = action || (data?.action);
    console.log(`Auth function called with action: ${actionToUse}`);

    if (!actionToUse) {
      throw new Error("No action specified");
    }

    // Route to the appropriate handler based on the action
    switch (actionToUse) {
      case "signup":
        return await handleSignUp(data);
      case "signin":
        return await handleSignIn(data);
      case "reset-password":
        return await handleResetPassword(data, req);
      case "update-password":
        return await handleUpdatePassword(data);
      case "create-admin":
        return await handleCreateAdmin(data);
      default:
        throw new Error(`Invalid action: ${actionToUse}`);
    }
  } catch (error) {
    console.error("Auth function error:", error.message);
    
    return new Response(
      JSON.stringify({
        error: error.message || "Unknown error occurred",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 400,
      }
    );
  }
});

async function handleSignUp(data: SignUpData) {
  const { email, password, first_name, last_name, phone, is_admin } = data;
  
  if (!email || !password || !first_name || !last_name) {
    throw new Error("Missing required fields");
  }
  
  console.log(`Attempting to create user with email: ${email}`);
  
  try {
    // User registration with metadata for profile
    const { data: authData, error } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm email for development
      user_metadata: {
        first_name,
        last_name,
        phone,
      },
    });
    
    if (error) {
      console.error("Sign up error:", error);
      throw error;
    }
    
    console.log(`User created successfully: ${authData.user.id}`);
    
    // Wait a moment to allow the handle_new_user trigger to execute
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Double-check if profile was created by trigger
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single();
      
    if (profileError) {
      console.log("Profile not found, manually creating profile");
      
      // If profile doesn't exist, create it manually
      const { error: insertError } = await supabase
        .from('profiles')
        .insert({
          id: authData.user.id,
          email: email,
          first_name: first_name,
          last_name: last_name,
          phone: phone || null
        });
        
      if (insertError) {
        console.error("Error creating profile:", insertError);
        // Continue anyway - the user is created, just log the error
      }
      
      // Also ensure user role is created
      const { error: roleError } = await supabase
        .from('user_roles')
        .insert({
          user_id: authData.user.id,
          role: is_admin ? 'admin' : 'user'
        });
        
      if (roleError) {
        console.error("Error creating user role:", roleError);
        // Continue anyway - the user is created, just log the error
      }
    } else {
      console.log("Profile already created by trigger:", profileData);
      
      // If user should be admin, ensure they have admin role
      if (is_admin) {
        const { error: roleError } = await supabase
          .from('user_roles')
          .upsert({
            user_id: authData.user.id,
            role: 'admin'
          });
          
        if (roleError) {
          console.error("Error creating admin role:", roleError);
        }
      }
    }
    
    return new Response(
      JSON.stringify({
        user: authData.user,
        message: "User created successfully",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 201,
      }
    );
  } catch (error) {
    console.error("Sign up process error:", error.message);
    throw error;
  }
}

async function handleSignIn(data: SignInData) {
  const { email, password } = data;
  
  if (!email || !password) {
    throw new Error("Email and password are required");
  }
  
  console.log(`Attempting to sign in user with email: ${email}`);
  
  const { data: authData, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  
  if (error) {
    console.error("Sign in error:", error);
    throw error;
  }
  
  console.log(`User signed in successfully: ${authData.user.id}`);
  
  return new Response(
    JSON.stringify({
      session: authData.session,
      user: authData.user,
    }),
    {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    }
  );
}

async function handleResetPassword(data: PasswordResetData, req: Request) {
  const { email } = data;
  
  if (!email) {
    throw new Error("Email is required");
  }
  
  console.log(`Password reset requested for email: ${email}`);
  
  const origin = new URL(req.url).origin;
  
  const { data: resetData, error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${origin}/reset-password`,
  });
  
  if (error) {
    console.error("Password reset error:", error);
    throw error;
  }
  
  return new Response(
    JSON.stringify({
      message: "Password reset instructions sent to your email",
    }),
    {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    }
  );
}

async function handleUpdatePassword(data: UpdatePasswordData) {
  const { access_token, new_password } = data;
  
  if (!access_token || !new_password) {
    throw new Error("Access token and new password are required");
  }
  
  console.log("Password update requested");
  
  // Update the user's password
  const { data: updateData, error } = await supabase.auth.updateUser(
    { password: new_password },
    { 
      auth: {
        access_token,
      }
    }
  );
  
  if (error) {
    console.error("Update password error:", error);
    throw error;
  }
  
  return new Response(
    JSON.stringify({
      message: "Password updated successfully",
      user: updateData.user,
    }),
    {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    }
  );
}

async function handleCreateAdmin(data: CreateAdminData) {
  const { email, password, first_name, last_name, phone } = data;
  
  if (!email || !password || !first_name || !last_name) {
    throw new Error("Missing required fields for admin creation");
  }
  
  console.log(`Attempting to create admin user with email: ${email}`);
  
  try {
    // First check if admin user with this email already exists
    const { data: existingUsers, error: listError } = await supabase.auth.admin.listUsers();
    
    if (listError) {
      console.error("Error listing users:", listError);
      throw listError;
    }
    
    const existingUser = existingUsers?.users?.find(user => user.email === email);
    
    if (existingUser) {
      console.log(`User with email ${email} already exists. Ensuring they have admin role.`);
      
      // Check if user already has admin role
      const { data: roleData, error: roleCheckError } = await supabase
        .from('user_roles')
        .select('*')
        .eq('user_id', existingUser.id)
        .eq('role', 'admin')
        .maybeSingle();
        
      if (roleCheckError) {
        console.error("Error checking admin role:", roleCheckError);
        throw roleCheckError;
      }
      
      // If not an admin, make them an admin
      if (!roleData) {
        const { error: roleInsertError } = await supabase
          .from('user_roles')
          .insert({
            user_id: existingUser.id,
            role: 'admin'
          });
          
        if (roleInsertError) {
          console.error("Error creating admin role:", roleInsertError);
          throw roleInsertError;
        }
      }
      
      return new Response(
        JSON.stringify({
          message: "User already exists and has been granted admin privileges",
          user: existingUser,
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 200,
        }
      );
    }
    
    // Create new admin user
    const { data: authData, error } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm email for development
      user_metadata: {
        first_name,
        last_name,
        phone,
      },
    });
    
    if (error) {
      console.error("Admin creation error:", error);
      throw error;
    }
    
    console.log(`Admin user created successfully: ${authData.user.id}`);
    
    // Wait for trigger to create profile
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check if profile was created
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single();
      
    if (profileError) {
      console.log("Profile not found, manually creating profile for admin");
      
      // Create profile manually
      const { error: insertError } = await supabase
        .from('profiles')
        .insert({
          id: authData.user.id,
          email: email,
          first_name: first_name,
          last_name: last_name,
          phone: phone || null
        });
        
      if (insertError) {
        console.error("Error creating admin profile:", insertError);
        // Continue anyway
      }
    }
    
    // Add admin role
    const { error: roleError } = await supabase
      .from('user_roles')
      .insert({
        user_id: authData.user.id,
        role: 'admin'
      });
      
    if (roleError) {
      console.error("Error creating admin role:", roleError);
      throw roleError;
    }
    
    return new Response(
      JSON.stringify({
        message: "Admin user created successfully",
        user: authData.user,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 201,
      }
    );
  } catch (error) {
    console.error("Admin creation process error:", error.message);
    throw error;
  }
}
