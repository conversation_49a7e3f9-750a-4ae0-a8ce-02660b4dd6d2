// Hook for joining a rotational group (requires groupId and userId)
export const useJoinRotationalGroup = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ groupId, userId }: { groupId: string; userId: string }) => {
      const { data, error } = await GroupSavingsService.joinRotationalGroup(groupId, userId);
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['rotational-groups'] });
      toast.success('Successfully joined the rotational group!');
    },
    onError: (error) => {
      console.error('Error joining rotational group:', error);
      toast.error('Failed to join rotational group');
    }
  });
};

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { GroupSavingsService } from '@/services/group-savings';
import { CreateGroupSavingsInput, CreateRotationalGroupInput } from '@/types/group-savings';
import { toast } from 'sonner';

export const useGroupSavingsPlans = () => {
  return useQuery({
    queryKey: ['group-savings-plans'],
    queryFn: async () => {
      const { data, error } = await GroupSavingsService.getGroupSavingsPlans();
      if (error) throw error;
      return data;
    }
  });
};

export const useRotationalGroups = () => {
  return useQuery({
    queryKey: ['rotational-groups'],
    queryFn: async () => {
      const { data, error } = await GroupSavingsService.getRotationalGroups();
      if (error) throw error;
      return data;
    }
  });
};

export const useGroupMembers = (groupId: string) => {
  return useQuery({
    queryKey: ['group-members', groupId],
    queryFn: async () => {
      const { data, error } = await GroupSavingsService.getGroupMembers(groupId);
      if (error) throw error;
      return data;
    },
    enabled: !!groupId
  });
};

export const useGroupTransactions = (groupId: string) => {
  return useQuery({
    queryKey: ['group-transactions', groupId],
    queryFn: async () => {
      const { data, error } = await GroupSavingsService.getGroupTransactions(groupId);
      if (error) throw error;
      return data;
    },
    enabled: !!groupId
  });
};

export const useCreateGroupSavingsPlan = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: CreateGroupSavingsInput) => {
      const { data: result, error } = await GroupSavingsService.createGroupSavingsPlan(data);
      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['group-savings-plans'] });
      toast.success('Group savings plan created successfully!');
    },
    onError: (error) => {
      console.error('Error creating group savings plan:', error);
      toast.error('Failed to create group savings plan');
    }
  });
};


// Accepts extra fields for backend (createdBy, nextPayoutDate)
export const useCreateRotationalGroup = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: CreateRotationalGroupInput & { createdBy?: string; nextPayoutDate?: string }) => {
      const { data: result, error } = await GroupSavingsService.createRotationalGroup(data);
      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['rotational-groups'] });
      toast.success('Rotational group created successfully!');
    },
    onError: (error) => {
      console.error('Error creating rotational group:', error);
      toast.error('Failed to create rotational group');
    }
  });
};

// Hook for joining a rotational group (requires groupId and userId)
export const useJoinRotationalGroup = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ groupId, userId }: { groupId: string; userId: string }) => {
      const { data, error } = await GroupSavingsService.joinRotationalGroup(groupId, userId);
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['rotational-groups'] });
      toast.success('Successfully joined the rotational group!');
    },
    onError: (error) => {
      console.error('Error joining rotational group:', error);
      toast.error('Failed to join rotational group');
    }
  });
};

export const useJoinGroupSavingsPlan = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (groupId: string) => {
      const { data, error } = await GroupSavingsService.joinGroupSavingsPlan(groupId);
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['group-savings-plans'] });
      toast.success('Successfully joined the group!');
    },
    onError: (error) => {
      console.error('Error joining group:', error);
      toast.error('Failed to join group');
    }
  });
};

export const useAddMemberToGroup = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ groupId, email, contributionAmount, role }: { 
      groupId: string; 
      email: string; 
      contributionAmount: number; 
      role?: 'admin' | 'member' 
    }) => {
      const { data, error } = await GroupSavingsService.addMemberToGroup(groupId, email, contributionAmount, role);
      if (error) throw error;
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['group-members', variables.groupId] });
      queryClient.invalidateQueries({ queryKey: ['group-savings-plans'] });
      toast.success('Member added successfully!');
    },
    onError: (error) => {
      console.error('Error adding member:', error);
      toast.error('Failed to add member');
    }
  });
};
