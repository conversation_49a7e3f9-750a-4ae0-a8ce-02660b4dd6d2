// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://vqxbuhicyaukgscncdud.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZxeGJ1aGljeWF1a2dzY25jZHVkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMTc4OTYsImV4cCI6MjA1NjU5Mzg5Nn0._u2oq6V_25omHwCX2ATvYjldKZ6ULVI_oUQKCtTebtQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);