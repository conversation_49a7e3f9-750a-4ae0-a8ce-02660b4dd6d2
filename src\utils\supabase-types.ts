
import { PostgrestError } from '@supabase/supabase-js';
import { Json } from '@/integrations/supabase/types';

// Type guard to check if an object is a PostgrestError
export function isPostgrestError(obj: any): obj is PostgrestError {
  return obj && typeof obj === 'object' && 'code' in obj && 'message' in obj;
}

// Safe function to check if a query result has data
export function hasData<T>(result: { data: T | null, error: PostgrestError | null }): result is { data: T, error: null } {
  return result.data !== null && !result.error;
}

// Safely handle array access for potentially undefined or error objects
export function safeArray<T>(data: T | null | undefined): T[] {
  if (Array.isArray(data)) return data;
  return [];
}

// Convert Json type to Record<string, any> safely
export function jsonToRecord(json: Json | null): Record<string, any> | null {
  if (json === null) return null;
  if (typeof json === 'object' && json !== null) return json as Record<string, any>;
  if (typeof json === 'string') {
    try {
      return JSON.parse(json);
    } catch (e) {
      return null;
    }
  }
  return null;
}

// Cast Json to a specific type safely
export function safeJsonCast<T>(json: Json | null): T | null {
  if (json === null) return null;
  try {
    return json as unknown as T;
  } catch (e) {
    return null;
  }
}

// Type guard to check if an object is a user profile
export function isUserProfile(obj: any): obj is {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  avatar_url?: string;
  status?: string;
  kyc_status?: string;
} {
  return obj && typeof obj === 'object' && 'id' in obj && 'email' in obj;
}
