import { GroupSavingsPlan, GroupMember, RotationalGroup, GroupTransaction, CreateGroupSavingsInput, CreateRotationalGroupInput } from '@/types/group-savings';
import { supabase } from '@/integrations/supabase/client';

// Mock data for development
const MOCK_GROUP_SAVINGS_PLANS: GroupSavingsPlan[] = [
  {
    id: '1',
    name: 'iPhone 15 Pro Group',
    description: 'Save together to buy the latest iPhone 15 Pro',
    category: 'phone',
    target_amount: 500000,
    contribution_amount: 50000,
    start_date: '2024-01-01',
    end_date: '2024-12-31',
    status: 'recruiting',
    max_members: 10,
    current_members: 6,
    created_at: '2023-12-01T00:00:00Z',
    created_by: 'user1',
    interest_rate: 5,
    auto_deduct: true
  },
  {
    id: '2',
    name: 'Car Purchase Group',
    description: 'Pool funds together for a new car',
    category: 'car',
    target_amount: 2000000,
    contribution_amount: 100000,
    start_date: '2024-02-01',
    end_date: '2024-12-31',
    status: 'active',
    max_members: 20,
    current_members: 15,
    created_at: '2023-12-15T00:00:00Z',
    created_by: 'user2',
    interest_rate: 7,
    auto_deduct: false
  }
];

const MOCK_ROTATIONAL_GROUPS: RotationalGroup[] = [
  {
    id: '101',
    name: 'Weekly Savings Circle',
    description: 'A group that saves weekly for household needs',
    creator_id: 'user1',
    contribution_amount: 10000,
    frequency: 'weekly',
    max_members: 5,
    current_members: [],
    status: 'recruiting',
    start_date: '2024-01-15',
    next_withdrawal: {
      member_id: 'user3',
      date: '2024-01-22',
      amount: 50000
    },
    invite_link: 'http://example.com/invite/weekly',
    total_cycles: 5,
    current_cycle: 1,
    created_at: '2023-11-20T00:00:00Z'
  },
  {
    id: '102',
    name: 'Monthly Investment Pool',
    description: 'Saving monthly for investment opportunities',
    creator_id: 'user2',
    contribution_amount: 50000,
    frequency: 'monthly',
    max_members: 10,
    current_members: [],
    status: 'active',
    start_date: '2024-01-01',
    next_withdrawal: {
      member_id: 'user5',
      date: '2024-02-01',
      amount: 500000
    },
    invite_link: 'http://example.com/invite/monthly',
    total_cycles: 10,
    current_cycle: 3,
    created_at: '2023-10-10T00:00:00Z'
  }
];

const MOCK_GROUP_MEMBERS: GroupMember[] = [
  {
    id: '201',
    user_id: 'user3',
    group_id: '1',
    name: 'Alice Smith',
    email: '<EMAIL>',
    avatar_url: 'http://example.com/avatars/alice.jpg',
    contribution_amount: 50000,
    total_contributed: 300000,
    joined_at: '2024-01-05',
    role: 'member',
    status: 'active',
    withdrawal_order: 3,
    has_withdrawn: false
  },
  {
    id: '202',
    user_id: 'user4',
    group_id: '1',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    avatar_url: 'http://example.com/avatars/bob.jpg',
    contribution_amount: 50000,
    total_contributed: 350000,
    joined_at: '2024-01-10',
    role: 'admin',
    status: 'active',
    withdrawal_order: 1,
    has_withdrawn: true
  }
];

const MOCK_GROUP_TRANSACTIONS: GroupTransaction[] = [
  {
    id: '301',
    group_id: '1',
    member_id: 'user3',
    type: 'contribution',
    amount: 50000,
    description: 'Weekly contribution',
    date: '2024-01-05',
    status: 'completed'
  },
  {
    id: '302',
    group_id: '1',
    member_id: 'user4',
    type: 'withdrawal',
    amount: 500000,
    description: 'iPhone 15 Pro purchase',
    date: '2024-01-12',
    status: 'completed'
  }
];

export const GroupSavingsService = {
  getGroupSavingsPlans: async (): Promise<{ data: GroupSavingsPlan[] | null; error: any }> => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      return { data: MOCK_GROUP_SAVINGS_PLANS, error: null };
    } catch (error) {
      console.error('Error fetching group savings plans:', error);
      return { data: null, error };
    }
  },

  getRotationalGroups: async (): Promise<{ data: RotationalGroup[] | null; error: any }> => {
    try {
      const res = await fetch('/api/rotational-group-savings');
      if (!res.ok) throw new Error('Failed to fetch rotational groups');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching rotational groups:', error);
      return { data: null, error };
    }
  },

  getGroupMembers: async (groupId: string): Promise<{ data: GroupMember[] | null; error: any }> => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      const members = MOCK_GROUP_MEMBERS.filter(member => member.group_id === groupId);
      return { data: members, error: null };
    } catch (error) {
      console.error('Error fetching group members:', error);
      return { data: null, error };
    }
  },

  getGroupTransactions: async (groupId: string): Promise<{ data: GroupTransaction[] | null; error: any }> => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      const transactions = MOCK_GROUP_TRANSACTIONS.filter(transaction => transaction.group_id === groupId);
      return { data: transactions, error: null };
    } catch (error) {
      console.error('Error fetching group transactions:', error);
      return { data: null, error };
    }
  },

  createGroupSavingsPlan: async (planData: CreateGroupSavingsInput): Promise<{ data: GroupSavingsPlan | null; error: any }> => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const newPlan: GroupSavingsPlan = {
        id: String(MOCK_GROUP_SAVINGS_PLANS.length + 1),
        name: planData.name,
        description: planData.description,
        category: planData.category as any,
        target_amount: planData.target_amount,
        contribution_amount: planData.contribution_amount,
        start_date: planData.start_date,
        end_date: planData.end_date,
        status: 'recruiting',
        max_members: planData.max_members,
        current_members: 1,
        created_at: new Date().toISOString(),
        created_by: 'currentUser', // Replace with actual user ID
        interest_rate: planData.interest_rate,
        auto_deduct: planData.auto_deduct
      };

      MOCK_GROUP_SAVINGS_PLANS.push(newPlan);
      return { data: newPlan, error: null };
    } catch (error) {
      console.error('Error creating group savings plan:', error);
      return { data: null, error };
    }
  },

  createRotationalGroup: async (groupData: CreateRotationalGroupInput & { createdBy?: string; nextPayoutDate?: string }): Promise<{ data: RotationalGroup | null; error: any }> => {
    try {
      // You may want to add createdBy and nextPayoutDate from context or UI
      const res = await fetch('/api/rotational-group-savings/rgs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(groupData)
      });
      if (!res.ok) throw new Error('Failed to create rotational group');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error creating rotational group:', error);
      return { data: null, error };
    }
  },
  joinRotationalGroup: async (groupId: string, userId: string): Promise<{ data: any | null; error: any }> => {
    try {
      const res = await fetch(`/api/rotational-group-savings/${groupId}/join`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      });
      if (!res.ok) throw new Error('Failed to join group');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error joining rotational group:', error);
      return { data: null, error };
    }
  },

  joinGroupSavingsPlan: async (groupId: string): Promise<{ data: any | null; error: any }> => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const plan = MOCK_GROUP_SAVINGS_PLANS.find(plan => plan.id === groupId);
      if (!plan) {
        throw new Error('Group savings plan not found');
      }

      if (plan.current_members >= plan.max_members) {
        throw new Error('Group is already full');
      }

      plan.current_members += 1;
      return { data: { message: 'Successfully joined the group!' }, error: null };
    } catch (error) {
      console.error('Error joining group savings plan:', error);
      return { data: null, error };
    }
  },

  addMemberToGroup: async (groupId: string, email: string, contributionAmount: number, role: 'admin' | 'member' = 'member'): Promise<{ data: any | null; error: any }> => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const group = MOCK_GROUP_SAVINGS_PLANS.find(plan => plan.id === groupId);
      if (!group) {
        throw new Error('Group savings plan not found');
      }

      if (group.current_members >= group.max_members) {
        throw new Error('Group is already full');
      }

      const newMember: GroupMember = {
        id: String(MOCK_GROUP_MEMBERS.length + 1),
        group_id: groupId,
        user_id: String(MOCK_GROUP_MEMBERS.length + 1), // Mock user ID
        name: 'New Member', // Fetch from user profile
        email: email,
        contribution_amount: contributionAmount,
        total_contributed: 0,
        joined_at: new Date().toISOString(),
        role: role,
        status: 'active',
        withdrawal_order: group.current_members + 1,
        has_withdrawn: false
      };

      MOCK_GROUP_MEMBERS.push(newMember);
      group.current_members += 1;

      return { data: { message: 'Member added successfully!' }, error: null };
    } catch (error) {
      console.error('Error adding member to group:', error);
      return { data: null, error };
    }
  }
};
