
import { AuthProvider } from '../auth/auth-service';

// Current provider - should match auth-service
const currentProvider = AuthProvider.MOCK;

export const UserService = {
  // Admin creates a new user
  adminCreateUser: async (userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
    isAdmin?: boolean;
  }) => {
    try {
      if (currentProvider === AuthProvider.MOCK) {
        // Mock implementation
        return { data: { success: true }, error: null };
      } else {
        // Placeholder for other providers
        return { data: null, error: new Error('Provider not supported') };
      }
    } catch (error) {
      console.error('Admin create user error:', error);
      return { data: null, error };
    }
  },

  // Get all users (admin only)
  getAllUsers: async () => {
    try {
      if (currentProvider === AuthProvider.MOCK) {
        // Mock implementation
        return { data: [], error: null };
      } else {
        // Placeholder for other providers
        return { data: null, error: new Error('Provider not supported') };
      }
    } catch (error) {
      console.error('Get all users error:', error);
      return { data: null, error };
    }
  },

  // Update user by admin
  updateUserByAdmin: async (userId: string, userData: {
    firstName?: string;
    lastName?: string;
    phone?: string;
    status?: 'active' | 'suspended' | 'blocked';
    isAdmin?: boolean;
  }) => {
    try {
      if (currentProvider === AuthProvider.MOCK) {
        // Mock implementation
        return { data: { success: true }, error: null };
      } else {
        // Placeholder for other providers
        return { data: null, error: new Error('Provider not supported') };
      }
    } catch (error) {
      console.error('Update user by admin error:', error);
      return { data: null, error };
    }
  },

  // Create initial admin user
  createInitialAdminUser: async () => {
    try {
      if (currentProvider === AuthProvider.MOCK) {
        // Mock implementation
        return { data: { success: true }, error: null };
      } else {
        // Placeholder for other providers
        return { data: null, error: new Error('Provider not supported') };
      }
    } catch (error) {
      console.error('Create initial admin error:', error);
      return { data: null, error };
    }
  }
};
