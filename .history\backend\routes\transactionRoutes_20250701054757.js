const express = require('express');
const Transaction = require('../models/transaction');
const SavingsPlan = require('../models/savingsPlan');
const router = express.Router();

// Helper to generate a 6-character alphanumeric reference
function generateReference() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let ref = '';
  for (let i = 0; i < 6; i++) {
    ref += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return ref;
}

// Create a new transaction
router.post('/trans', async (req, res) => {
  console.log('[Transaction] POST /trans called with body:', req.body);
  try {
    const { userId, type, amount, description, savingsPlanId, status } = req.body;
    // Validate required fields
    if (!userId || !type || !amount || !description) {
      console.warn('[Transaction] Missing required fields:', { userId, type, amount, description });
      return res.status(400).json({ error: 'User, type, amount, and description are required.' });
    }
    if (typeof amount !== 'number' || amount <= 0) {
      console.warn('[Transaction] Invalid amount:', amount);
      return res.status(400).json({ error: 'Amount must be a positive number.' });
    }
    if (!['deposit', 'withdrawal', 'transfer', 'profit'].includes(type)) {
      console.warn('[Transaction] Invalid transaction type:', type);
      return res.status(400).json({ error: 'Invalid transaction type.' });
    }
    // Generate unique reference
    let reference;
    let exists = true;
    while (exists) {
      reference = generateReference();
      exists = await Transaction.exists({ reference });
    }
    // Create new transaction
    const newTransaction = new Transaction({
      userId,
      type,
      amount,
      description,
      savingsPlanId,
      status: status || 'completed',
      reference,
    });
    const savedTransaction = await newTransaction.save();
    console.log('[Transaction] Transaction saved:', savedTransaction);
    // If this is a deposit to a savings plan, update the plan's progress
    if (type === 'deposit' && savingsPlanId) {
      // Find the savings plan
      const savingsPlan = await SavingsPlan.findById(savingsPlanId);
      if (savingsPlan) {
        console.log('[Transaction] Deposit to savings plan:', savingsPlanId);
        // You could update savings plan progress here if needed
      }
    }
    res.status(201).json(savedTransaction);
  } catch (error) {
    console.error('[Transaction] Error creating transaction:', error);
    res.status(500).json({ error: 'Failed to create transaction', details: error.message });
  }
});

// Get all transactions for a user
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 20, page = 1, type } = req.query;
    
    // Build query
    const query = { userId };
    if (type) {
      query.type = type;
    }
    
    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Fetch transactions with pagination
    const transactions = await Transaction.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    // Get total count for pagination
    const totalCount = await Transaction.countDocuments(query);
    
    res.status(200).json({
      transactions,
      pagination: {
        total: totalCount,
        page: parseInt(page),
        pages: Math.ceil(totalCount / parseInt(limit)),
      },
    });
  } catch (error) {
    console.error('Error fetching transactions:', error);
    res.status(500).json({ error: 'Failed to fetch transactions', details: error.message });
  }
});

// Get recent transactions for a user
router.get('/recent', async (req, res) => {
  try {
    const { userId, limit = 5 } = req.query;
    
    // Validate userId
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    // Fetch recent transactions
    const recentTransactions = await Transaction.find({ userId })
      .sort({ createdAt: -1 })
      .limit(parseInt(limit));
    
    res.status(200).json(recentTransactions);
  } catch (error) {
    console.error('Error fetching recent transactions:', error);
    res.status(500).json({ error: 'Failed to fetch recent transactions', details: error.message });
  }
});

// Admin: Get all transactions
router.get('/all', async (req, res) => {
  try {
    const { limit = 50, page = 1, type } = req.query;
    const query = {};
    if (type) query.type = type;
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const transactions = await Transaction.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('userId', 'firstName lastName');
    const totalCount = await Transaction.countDocuments(query);
    res.status(200).json({
      transactions,
      pagination: {
        total: totalCount,
        page: parseInt(page),
        pages: Math.ceil(totalCount / parseInt(limit)),
      },
    });
  } catch (error) {
    console.error('Error fetching all transactions:', error);
    res.status(500).json({ error: 'Failed to fetch all transactions', details: error.message });
  }
});

// GET /api/transactions/stats
router.get('/stats', async (req, res) => {
  try {
    const totalTransactions = await Transaction.countDocuments();
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const transactionsToday = await Transaction.countDocuments({ createdAt: { $gte: today } });
    const transactionsValue = await Transaction.aggregate([
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);
    res.json({
      totalTransactions,
      transactionsToday,
      transactionsValue: transactionsValue[0] ? transactionsValue[0].total : 0
    });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch transaction stats' });
  }
});

// Get a specific transaction
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const transaction = await Transaction.findById(id);
    
    if (!transaction) {
      return res.status(404).json({ error: 'Transaction not found' });
    }
    
    res.status(200).json(transaction);
  } catch (error) {
    console.error('Error fetching transaction:', error);
    res.status(500).json({ error: 'Failed to fetch transaction', details: error.message });
  }
});

// Update a transaction status
router.patch('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    if (!status) {
      return res.status(400).json({ error: 'Status is required' });
    }
    
    const updatedTransaction = await Transaction.findByIdAndUpdate(
      id, 
      { status }, 
      { new: true }
    );
    
    if (!updatedTransaction) {
      return res.status(404).json({ error: 'Transaction not found' });
    }
    
    res.status(200).json(updatedTransaction);
  } catch (error) {
    console.error('Error updating transaction status:', error);
    res.status(500).json({ error: 'Failed to update transaction status', details: error.message });
  }
});

module.exports = router;
