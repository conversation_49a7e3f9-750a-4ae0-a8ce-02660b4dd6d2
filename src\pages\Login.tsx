
import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { Mail, Lock, Phone } from "lucide-react";
import { OtpVerification } from "@/components/profile/OtpVerification";
import { useAuth } from "@/hooks/use-auth";

const Login = () => {
  const navigate = useNavigate();
  const { signIn, user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [phone, setPhone] = useState("");
  const [loginMethod, setLoginMethod] = useState<"email" | "phone">("email");
  const [showOtp, setShowOtp] = useState(false);

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      if (user.isAdmin) {
        navigate('/admin/dashboard');
      } else {
        navigate('/user/dashboard');
      }
    }
  }, [user, navigate]);

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      await signIn({ email, password });
    } catch (error) {
      console.error("Login error:", error);
    } finally {
      setLoading(false);
    }
  };
  
  const handlePhoneLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // Mock phone login - in real implementation this would send OTP
      setShowOtp(true);
      toast.success("Verification code sent to your phone");
    } catch (error: any) {
      console.error("Phone login error:", error);
      toast.error("Failed to send verification code");
    } finally {
      setLoading(false);
    }
  };
  
  const handleOtpVerified = async () => {
    try {
      setLoading(true);
      // Mock OTP verification - use regular user for phone auth
      await signIn({ email: '<EMAIL>', password: 'password' });
      setShowOtp(false);
    } catch (error: any) {
      console.error("OTP verification error:", error);
      toast.error("Failed to verify OTP");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-muted/50 to-background p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-6">
          <div className="flex items-center justify-center gap-1 mb-1">
            <div className="h-6 w-6 rounded-full overflow-hidden">
              <img 
                src="/lovable-uploads/96b8da77-302c-4b65-87fb-ec3cf6bc86ca.png" 
                alt="Koja Logo" 
                className="h-full w-full object-cover"
              />
            </div>
            <h1 className="text-2xl font-bold text-brand-blue">
              <span className="text-brand-yellow uppercase tracking-wider font-unica-one">ASUSU</span> by Koja
            </h1>
          </div>
          <p className="text-muted-foreground text-sm">Your daily savings partner</p>
        </div>
        
        <Card className="w-full animate-scale-in">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">Sign In</CardTitle>
            <CardDescription className="text-center">
              Enter your credentials to access your account
            </CardDescription>
          </CardHeader>
          
          {showOtp ? (
            <CardContent>
              <OtpVerification 
                phoneNumber={phone}
                onVerified={handleOtpVerified}
                onCancel={() => {
                  setShowOtp(false);
                }}
              />
            </CardContent>
          ) : (
            <CardContent>
              <Tabs defaultValue="email" value={loginMethod} onValueChange={(value) => setLoginMethod(value as "email" | "phone")}>
                <TabsList className="grid w-full grid-cols-2 mb-4">
                  <TabsTrigger value="email">Email</TabsTrigger>
                  <TabsTrigger value="phone">Phone</TabsTrigger>
                </TabsList>
                
                <TabsContent value="email">
                  <form onSubmit={handleEmailLogin} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="email"
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          placeholder="<EMAIL>"
                          required
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="password">Password</Label>
                        <Link 
                          to="/forgot-password"
                          className="text-sm text-brand-blue hover:underline"
                        >
                          Forgot Password?
                        </Link>
                      </div>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="password"
                          type="password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          placeholder="••••••••"
                          required
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <Button 
                      type="submit" 
                      className="w-full h-12"
                      variant="brand"
                      disabled={loading}
                    >
                      {loading ? (
                        <span className="flex items-center justify-center">
                          <span className="animate-spin mr-2">⟳</span> Signing in...
                        </span>
                      ) : "Sign In"}
                    </Button>
                  </form>
                </TabsContent>
                
                <TabsContent value="phone">
                  <form onSubmit={handlePhoneLogin} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="phone"
                          type="tel"
                          value={phone}
                          onChange={(e) => setPhone(e.target.value)}
                          placeholder="+234 ************"
                          required
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <Button 
                      type="submit" 
                      className="w-full h-12"
                      variant="brand"
                      disabled={loading}
                    >
                      {loading ? (
                        <span className="flex items-center justify-center">
                          <span className="animate-spin mr-2">⟳</span> Sending code...
                        </span>
                      ) : "Continue with Phone"}
                    </Button>
                  </form>
                </TabsContent>
              </Tabs>
            </CardContent>
          )}
          
          <CardFooter className="flex flex-col space-y-4">
            <div className="text-center text-sm text-muted-foreground w-full">
              Don't have an account?{" "}
              <Link
                to="/signup"
                className="text-brand-blue font-medium hover:underline clickable-link"
              >
                Create Account
              </Link>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default Login;
