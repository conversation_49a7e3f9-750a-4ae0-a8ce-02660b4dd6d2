/* Base Styling and Reset */
#root {
  width: 100%;
  min-height: 100vh;
}

/* Custom animations and transitions */
.hover-yellow {
  transition: all 0.3s ease;
}

.hover-yellow:hover {
  box-shadow: 0 4px 14px -2px rgba(253, 227, 20, 0.5);
  transform: translateY(-2px);
}

.card-hover-effect {
  transition: all 0.3s ease;
}

.card-hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Shimmer animation for loading states */
.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background-image: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0,
    rgba(255, 255, 255, 0.2) 20%,
    rgba(255, 255, 255, 0.5) 60%,
    rgba(255, 255, 255, 0)
  );
  animation: shimmer 2s infinite;
  content: '';
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* Yellow accent pulse */
.yellow-pulse {
  transition: all 0.3s ease;
  animation: yellow-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes yellow-pulse {
  0%, 100% { 
    box-shadow: 0 0 0 0 rgba(253, 227, 20, 0.4);
  }
  50% { 
    box-shadow: 0 0 20px 5px rgba(253, 227, 20, 0.4);
  }
}

/* Typography improvements */
.font-montserrat {
  font-family: 'Montserrat', system-ui, sans-serif;
}

/* Yellow glow on focus */
.yellow-focus:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(253, 227, 20, 0.5);
}

/* Floating animation */
.float-animation {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Yellow shadow */
.shadow-yellow {
  box-shadow: 0 4px 14px -2px rgba(253, 227, 20, 0.5);
}

/* Progress bar custom colors */
.progress-yellow::-webkit-progress-value {
  background-color: #FDE314;
}
.progress-yellow::-moz-progress-bar {
  background-color: #FDE314;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .mobile-hide {
    display: none;
  }

  .mobile-full-width {
    width: 100% !important;
  }

  .mobile-stack {
    flex-direction: column !important;
  }

  .mobile-text-center {
    text-align: center !important;
  }

  .mobile-mb-4 {
    margin-bottom: 1rem !important;
  }

  /* Mobile-specific button improvements */
  .mobile-button-stack {
    flex-direction: column !important;
    gap: 0.5rem !important;
  }

  .mobile-button-full {
    width: 100% !important;
  }

  /* Mobile card improvements */
  .mobile-card-padding {
    padding: 1rem !important;
  }

  /* Mobile text improvements */
  .mobile-text-sm {
    font-size: 0.875rem !important;
  }

  .mobile-text-xs {
    font-size: 0.75rem !important;
  }
}

/* Enhanced card styles for thick blue and shiny */
.blue-card {
  background-color: #1231B8;
  color: #FFFFFF;
  border: 2px solid rgba(253, 227, 20, 0.3);
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px rgba(18, 49, 184, 0.15), 0 0 0 2px rgba(18, 49, 184, 0.05);
}

.blue-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(18, 49, 184, 0.25), 0 0 0 3px rgba(18, 49, 184, 0.1);
  border-color: rgba(253, 227, 20, 0.5);
}

.blue-card .card-subtitle {
  color: rgba(255, 255, 255, 0.8);
}

/* Blue card with yellow text for dashboard */
.blue-card-yellow-text {
  background-color: #1231B8;
  color: #FDE314;
  border: 2px solid rgba(253, 227, 20, 0.3);
  box-shadow: 0 10px 25px rgba(18, 49, 184, 0.15);
}

.blue-card-yellow-text:hover {
  box-shadow: 0 15px 35px rgba(18, 49, 184, 0.25), 0 0 0 2px rgba(253, 227, 20, 0.3);
  transform: translateY(-3px);
}

.blue-card-yellow-text .card-subtitle {
  color: rgba(253, 227, 20, 0.9);
}

/* Yellow accented blue card */
.blue-card-accented {
  background-color: #1231B8;
  color: #FFFFFF;
  border: 2px solid #FDE314;
  box-shadow: 0 10px 25px rgba(18, 49, 184, 0.15), 0 0 0 2px rgba(253, 227, 20, 0.1);
}

.blue-card-accented:hover {
  box-shadow: 0 15px 35px rgba(18, 49, 184, 0.25), 0 0 0 3px rgba(253, 227, 20, 0.2);
  transform: translateY(-3px);
}

.blue-card-accented .card-title {
  color: #FDE314;
}

.blue-card-accented .card-value {
  font-size: 1.75rem;
  font-weight: bold;
  margin-top: 0.5rem;
  color: #FFFFFF;
}

/* Yellow hover effects */
.yellow-hover {
  transition: all 0.2s ease;
}

.yellow-hover:hover {
  background-color: rgba(253, 227, 20, 0.1);
  border-color: rgba(253, 227, 20, 0.5);
}

/* Admin verification buttons */
.verify-button {
  background-color: #1231B8;
  color: white;
  border: 1px solid #FDE314;
}

.verify-button:hover {
  background-color: #0e2696;
  box-shadow: 0 0 0 2px rgba(253, 227, 20, 0.3);
}

/* OTP verification styles */
.otp-input {
  width: 100%;
  text-align: center;
  letter-spacing: 0.5em;
  font-size: 1.25rem;
}

/* Animated check mark for verified status */
.verified-check {
  display: inline-block;
  transform-origin: center;
  animation: check-animation 0.5s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

@keyframes check-animation {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Glass effect for cards */
.glass-card {
  backdrop-filter: blur(12px);
  background-color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Fix for mobile sidebar z-index */
@media (max-width: 768px) {
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 20;
  }
}

/* Responsive tables */
@media (max-width: 768px) {
  .responsive-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .responsive-table {
    min-width: 650px;
  }

  /* Mobile dialog improvements */
  [data-radix-dialog-content] {
    max-width: calc(100vw - 2rem) !important;
    max-height: calc(100vh - 2rem) !important;
    margin: 1rem !important;
  }

  /* Mobile form improvements */
  .mobile-form-spacing {
    gap: 1rem !important;
  }

  .mobile-input-full {
    width: 100% !important;
  }
}

/* Admin role badges */
.role-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #1231B8;
  color: white;
}

.role-super-admin {
  background-color: #1231B8;
  border: 1px solid #FDE314;
}

.role-manager {
  background-color: #2563eb;
}

.role-support {
  background-color: #0891b2;
}

/* Admin savers list styles */
.saver-row:hover {
  background-color: rgba(18, 49, 184, 0.05);
}

.saver-status-active {
  background-color: #10b981;
  color: white;
}

.saver-status-inactive {
  background-color: #6b7280;
  color: white;
}

/* Admin permissions checklist */
.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 0.75rem;
}

.permission-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.permission-label {
  display: flex;
  flex-direction: column;
}

.permission-name {
  font-size: 0.875rem;
  font-weight: 500;
}

.permission-description {
  font-size: 0.75rem;
  color: rgb(100, 116, 139);
}

/* Responsive breakpoints for extra small devices */
@media (min-width: 480px) {
  .xs\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .xs\:flex-row {
    flex-direction: row;
  }

  .xs\:w-auto {
    width: auto;
  }

  .xs\:inline {
    display: inline;
  }

  .xs\:hidden {
    display: none;
  }

  .xs\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}
