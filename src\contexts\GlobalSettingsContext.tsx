// Global Settings Context for ASUSU by Koja
// React context provider for global settings that can modify UI dynamically

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import globalSettingsService from '@/services/global-settings.service';
import { useToast } from '@/hooks/use-toast';
import {
  GlobalSettings,
  PublicGlobalSettings,
  GlobalSettingsContextType,
  AppConfigUpdate,
  ColorThemeUpdate,
  BrandingUpdate,
  APIKeysUpdate,
  ContentUpdate,
  SystemConfigUpdate,
  ContactUpdate,
  ColorTheme
} from '@/types/global-settings';

const GlobalSettingsContext = createContext<GlobalSettingsContextType | undefined>(undefined);

interface GlobalSettingsProviderProps {
  children: React.ReactNode;
}

export function GlobalSettingsProvider({ children }: GlobalSettingsProviderProps) {
  const [settings, setSettings] = useState<PublicGlobalSettings | null>(null);
  const [adminSettings, setAdminSettings] = useState<GlobalSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Apply theme colors to CSS variables
  const applyThemeColors = useCallback((colors: ColorTheme) => {
    const root = document.documentElement;
    
    // Apply primary color
    root.style.setProperty('--color-primary', colors.primary);
    root.style.setProperty('--color-brand-blue', colors.primary);
    
    // Apply secondary color
    root.style.setProperty('--color-secondary', colors.secondary);
    root.style.setProperty('--color-brand-green', colors.secondary);
    
    // Apply accent color
    root.style.setProperty('--color-accent', colors.accent);
    root.style.setProperty('--color-brand-yellow', colors.accent);
    
    // Apply background color
    root.style.setProperty('--color-background', colors.background);
    
    // Update Tailwind CSS variables
    root.style.setProperty('--tw-color-primary', colors.primary);
    root.style.setProperty('--tw-color-secondary', colors.secondary);
    root.style.setProperty('--tw-color-accent', colors.accent);
    
    // Update app name in document title if available
    if (settings?.appName) {
      document.title = settings.appName;
    }
    
    // Update favicon if available
    if (settings?.favicon?.url) {
      const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
      if (favicon) {
        favicon.href = settings.favicon.url;
      }
    }
  }, [settings]);

  // Refresh public settings
  const refreshSettings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await globalSettingsService.getPublicSettings();
      setSettings(data);
      
      // Apply theme colors to UI
      if (data.colors) {
        applyThemeColors(data.colors);
      }
      
      // Update document title
      if (data.appName) {
        document.title = data.appName;
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch settings';
      setError(errorMessage);
      console.error('Error fetching public settings:', err);
    } finally {
      setLoading(false);
    }
  }, [applyThemeColors]);

  // Refresh admin settings
  const refreshAdminSettings = useCallback(async () => {
    try {
      const data = await globalSettingsService.getAdminSettings();
      setAdminSettings(data);
    } catch (err) {
      console.error('Error fetching admin settings:', err);
      // Don't set error for admin settings as user might not be admin
    }
  }, []);

  // Update app configuration
  const updateAppConfig = useCallback(async (data: AppConfigUpdate): Promise<boolean> => {
    try {
      const updatedSettings = await globalSettingsService.updateAppConfig(data);
      setAdminSettings(updatedSettings);
      
      // Update public settings if app name changed
      if (data.appName && settings) {
        setSettings({ ...settings, appName: data.appName });
        document.title = data.appName;
      }
      
      toast({
        title: "Success",
        description: "App configuration updated successfully",
        variant: "default"
      });
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update app configuration';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast, settings]);

  // Update colors
  const updateColors = useCallback(async (data: ColorThemeUpdate): Promise<boolean> => {
    try {
      const updatedSettings = await globalSettingsService.updateColors(data);
      setAdminSettings(updatedSettings);
      
      // Update public settings colors
      if (settings) {
        const newColors = { ...settings.colors, ...data };
        setSettings({ ...settings, colors: newColors });
        applyThemeColors(newColors);
      }
      
      toast({
        title: "Success",
        description: "Color theme updated successfully",
        variant: "default"
      });
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update color theme';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast, settings, applyThemeColors]);

  // Update branding
  const updateBranding = useCallback(async (data: BrandingUpdate): Promise<boolean> => {
    try {
      const updatedSettings = await globalSettingsService.updateBranding(data);
      setAdminSettings(updatedSettings);
      
      // Update public settings branding
      if (settings) {
        const updatedPublicSettings = { ...settings };
        if (data.logoUrl) {
          updatedPublicSettings.logo = {
            ...updatedPublicSettings.logo,
            url: data.logoUrl,
            filename: data.logoFilename || null
          };
        }
        if (data.faviconUrl) {
          updatedPublicSettings.favicon = {
            ...updatedPublicSettings.favicon,
            url: data.faviconUrl,
            filename: data.faviconFilename || null
          };
          
          // Update favicon in DOM
          const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
          if (favicon) {
            favicon.href = data.faviconUrl;
          }
        }
        setSettings(updatedPublicSettings);
      }
      
      toast({
        title: "Success",
        description: "Branding updated successfully",
        variant: "default"
      });
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update branding';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast, settings]);

  // Update API keys
  const updateAPIKeys = useCallback(async (data: APIKeysUpdate): Promise<boolean> => {
    try {
      const updatedSettings = await globalSettingsService.updateAPIKeys(data);
      setAdminSettings(updatedSettings);
      
      toast({
        title: "Success",
        description: "API keys updated successfully",
        variant: "default"
      });
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update API keys';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  // Update content
  const updateContent = useCallback(async (data: ContentUpdate): Promise<boolean> => {
    try {
      const updatedSettings = await globalSettingsService.updateContent(data);
      setAdminSettings(updatedSettings);
      
      // Update public settings content
      if (settings) {
        const updatedContent = { ...settings.content };
        if (data.landingPage) {
          updatedContent.landingPage = { ...updatedContent.landingPage, ...data.landingPage };
        }
        if (data.aboutPage) {
          updatedContent.aboutPage = { ...updatedContent.aboutPage, ...data.aboutPage };
        }
        setSettings({ ...settings, content: updatedContent });
      }
      
      toast({
        title: "Success",
        description: "Content updated successfully",
        variant: "default"
      });
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update content';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast, settings]);

  // Update system configuration
  const updateSystem = useCallback(async (data: SystemConfigUpdate): Promise<boolean> => {
    try {
      const updatedSettings = await globalSettingsService.updateSystem(data);
      setAdminSettings(updatedSettings);
      
      toast({
        title: "Success",
        description: "System configuration updated successfully",
        variant: "default"
      });
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update system configuration';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  // Update contact information
  const updateContact = useCallback(async (data: ContactUpdate): Promise<boolean> => {
    try {
      const updatedSettings = await globalSettingsService.updateContact(data);
      setAdminSettings(updatedSettings);
      
      // Update public settings contact
      if (settings) {
        const updatedContact = { ...settings.contact, ...data };
        setSettings({ ...settings, contact: updatedContact });
      }
      
      toast({
        title: "Success",
        description: "Contact information updated successfully",
        variant: "default"
      });
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update contact information';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast, settings]);

  // Initialize settings on mount
  useEffect(() => {
    refreshSettings();
  }, [refreshSettings]);

  const contextValue: GlobalSettingsContextType = {
    settings,
    adminSettings,
    loading,
    error,
    refreshSettings,
    refreshAdminSettings,
    updateAppConfig,
    updateColors,
    updateBranding,
    updateAPIKeys,
    updateContent,
    updateSystem,
    updateContact,
    applyThemeColors
  };

  return (
    <GlobalSettingsContext.Provider value={contextValue}>
      {children}
    </GlobalSettingsContext.Provider>
  );
}

// Hook to use global settings context
export function useGlobalSettingsContext() {
  const context = useContext(GlobalSettingsContext);
  if (context === undefined) {
    throw new Error('useGlobalSettingsContext must be used within a GlobalSettingsProvider');
  }
  return context;
}
