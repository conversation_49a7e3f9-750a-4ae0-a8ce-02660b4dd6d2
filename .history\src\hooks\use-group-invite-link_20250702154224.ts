import { useCallback } from 'react';

/**
 * Returns a copyable invite link for a group, based on the current window location and groupId.
 * Example: https://yourapp.com/join/group/12345
 */
export function useGroupInviteLink(groupId?: string) {
  return useCallback(() => {
    if (!groupId) return '';
    const { origin } = window.location;
    return `${origin}/join/group/${groupId}`;
  }, [groupId]);
}
