const mongoose = require('mongoose');

const RotationalGroupMemberSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  hasPaid: { type: Boolean, default: false },
  hasReceivedPayout: { type: Boolean, default: false },
  joinedAt: { type: Date, default: Date.now }
});

const RotationalGroupSavingsSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  amountPerInterval: { type: Number, required: true },
  intervalType: { type: String, enum: ['daily', 'weekly', 'monthly', 'yearly'], required: true },
  members: [RotationalGroupMemberSchema],
  currentCycle: { type: Number, default: 1 },
  nextPayoutDate: { type: Date, required: true },
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  createdAt: { type: Date, default: Date.now },
  isActive: { type: Boolean, default: true },
  payouts: [
    {
      userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      amount: { type: Number },
      date: { type: Date },
      cycle: { type: Number }
    }
  ]
});

module.exports = mongoose.model('RotationalGroupSavings', RotationalGroupSavingsSchema);
