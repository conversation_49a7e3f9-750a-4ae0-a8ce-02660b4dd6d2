import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
// import { PaymentProofUpload } from "@/components/payments/PaymentProofUpload";
import { WithdrawalRequestForm } from "@/components/payments/WithdrawalRequestForm";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { useBalance } from "@/hooks/use-balance";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { PayoutModeDialog } from "@/components/payments/PayoutModeDialog";
import { Wallet, Upload } from "lucide-react";

// Sample payment methods data
const paymentMethods = [
  {
    id: "1",
    name: "Bank Transfer",
    bankName: "First Bank of Nigeria",
    accountNumber: "**********",
    instructions: "Please include your user ID in the transfer description",
  },
  {
    id: "2",
    name: "Mobile Money",
    bankName: "GTBank",
    accountNumber: "**********",
    instructions: "Transfer to this number using any mobile money platform",
  }
];

// Sample transactions data
const transactions = [
  {
    id: "1",
    type: "deposit",
    amount: "50000",
    date: "2023-08-15",
    status: "completed",
    reference: "DEP12345"
  },
  {
    id: "2",
    type: "withdrawal",
    amount: "25000",
    date: "2023-08-14",
    status: "pending",
    reference: "WIT67890"
  },
  {
    id: "3",
    type: "deposit",
    amount: "30000",
    date: "2023-08-10",
    status: "completed",
    reference: "DEP54321"
  }
];

export default function Payments() {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null);
  const [depositAmount, setDepositAmount] = useState("");
  const [depositStep, setDepositStep] = useState<"amount" | "paystack" | "success">("amount");
  const [isPaying, setIsPaying] = useState(false);
  const [paystackUrl, setPaystackUrl] = useState("");
  const [activeTab, setActiveTab] = useState("deposit");
  const [transactions, setTransactions] = useState([]);
  const [showPayoutSettings, setShowPayoutSettings] = useState(false);
  const [showPaymentProofModal, setShowPaymentProofModal] = useState(false);
  const { balance, updateBalance } = useBalance();

  // Load transactions from localStorage
  useEffect(() => {
    const savedTransactions = localStorage.getItem('user_transactions');
    if (savedTransactions) {
      setTransactions(JSON.parse(savedTransactions));
    }
  }, []);

  const handleDepositAmountSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const amount = parseFloat(depositAmount);
    if (isNaN(amount) || amount <= 0) {
      toast.error("Please enter a valid deposit amount");
      return;
    }
    setIsPaying(true);
    // Get user email from localStorage
    const stored = localStorage.getItem('auth_user');
    const user = stored ? JSON.parse(stored) : null;
    const email = user?.email;
    if (!email) {
      toast.error("User email not found. Please log in again.");
      setIsPaying(false);
      return;
    }
    try {
      const res = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/paystack/deposit/initialize`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount: Math.round(amount * 100), // Paystack expects kobo
          email,
          user_id: user?._id || user?.id || undefined
        })
      });
      const data = await res.json();
      if (data.status && data.data?.authorization_url) {
        setPaystackUrl(data.data.authorization_url);
        setDepositStep("paystack");
      } else {
        toast.error(data.message || "Failed to initialize payment");
      }
    } catch (err) {
      toast.error("Network error. Please try again.");
    }
    setIsPaying(false);
  };

  const handleSelectPaymentMethod = (id: string) => {
    setSelectedPaymentMethod(id);
    setDepositStep("upload");
  };

  const handlePaymentProofUpload = async (formData) => {
    // Add amount to user's balance
    const amount = parseFloat(depositAmount);
    await updateBalance(amount, 'add');
    
    // Create a transaction record in localStorage
    const newTransaction = {
      id: `txn-${Date.now()}`,
      type: 'deposit',
      amount: amount,
      date: new Date().toISOString().split('T')[0],
      status: 'completed',
      reference: `DEP${Math.floor(Math.random() * 100000)}`
    };
    
    const savedTransactions = JSON.parse(localStorage.getItem('user_transactions') || '[]');
    savedTransactions.push(newTransaction);
    localStorage.setItem('user_transactions', JSON.stringify(savedTransactions));
    setTransactions(savedTransactions);
    
    toast.success("Deposit submitted successfully!");
    
    setDepositAmount("");
    setSelectedPaymentMethod(null);
    setDepositStep("amount");
    setShowPaymentProofModal(false);
  };

  const handleDirectPaymentProofSubmit = (formData) => {
    // Process the payment proof submission
    const amount = parseFloat(formData.amount);
    if (!isNaN(amount) && amount > 0) {
      updateBalance(amount, 'add');
      
      // Create a transaction record
      const newTransaction = {
        id: `txn-${Date.now()}`,
        type: 'deposit',
        amount: amount,
        date: new Date().toISOString().split('T')[0],
        status: 'pending',
        reference: formData.reference || `DEP${Math.floor(Math.random() * 100000)}`
      };
      
      const savedTransactions = JSON.parse(localStorage.getItem('user_transactions') || '[]');
      savedTransactions.push(newTransaction);
      localStorage.setItem('user_transactions', JSON.stringify(savedTransactions));
      setTransactions(savedTransactions);
      
      toast.success("Payment proof submitted successfully! We'll verify your payment soon.");
    } else {
      toast.error("Please enter a valid amount");
    }
    
    setShowPaymentProofModal(false);
  };

  const handleWithdrawalSubmit = async (formData) => {
    // Get user info for withdrawal reference (optional)
    const stored = localStorage.getItem('auth_user');
    const user = stored ? JSON.parse(stored) : null;
    try {
      const res = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/paystack/transfer`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount: formData.amount,
          bank_code: formData.bankCode,
          account_number: formData.accountNumber,
          account_name: formData.accountName,
          user_id: user?._id || user?.id || undefined
        })
      });
      const data = await res.json();
      if (data.status) {
        toast.success('Withdrawal initiated!');
        setActiveTab('transactions');
      } else {
        toast.error(data.message || 'Withdrawal failed');
      }
    } catch (err) {
      toast.error('Network error. Please try again.');
    }
  };

  // Helper function to determine badge variant based on status
  const getStatusBadgeVariant = (status: string) => {
    if (status === "completed") return "success";
    if (status === "failed") return "destructive";
    return "outline"; // For "pending" status
  };

  const renderDepositStep = () => {
    switch (depositStep) {
      case "amount":
        return (
          <form onSubmit={handleDepositAmountSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="amount">Deposit Amount (₦)</Label>
              <Input
                id="amount"
                type="number"
                placeholder="Enter amount to deposit"
                value={depositAmount}
                onChange={(e) => setDepositAmount(e.target.value)}
                required
              />
            </div>
            <Button type="submit" className="w-full" disabled={isPaying}>
              {isPaying ? "Processing..." : "Pay with Paystack"}
            </Button>
          </form>
        );
      case "paystack":
        return (
          <div className="space-y-4 text-center">
            <h3 className="text-lg font-medium">Complete Your Payment</h3>
            <p className="mb-2">Click the button below to pay securely with Paystack.</p>
            <Button asChild className="w-full" size="lg">
              <a href={paystackUrl} target="_blank" rel="noopener noreferrer">Open Paystack Payment Page</a>
            </Button>
            <Button variant="outline" className="w-full" onClick={() => setDepositStep("amount")}>Cancel</Button>
          </div>
        );
      case "success":
        return (
          <div className="space-y-4 text-center">
            <h3 className="text-lg font-medium text-green-600">Payment Successful!</h3>
            <p>Your deposit has been received and your balance updated.</p>
            <Button className="w-full" onClick={() => { setDepositStep("amount"); setDepositAmount(""); }}>Make Another Deposit</Button>
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold">Payments</h1>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            className="flex items-center gap-2"
            onClick={() => setShowPaymentProofModal(true)}
          >
            <Upload className="h-4 w-4" />
            I've Made a Payment
          </Button>
          <Button 
            variant="outline" 
            className="flex items-center gap-2"
            onClick={() => setShowPayoutSettings(true)}
          >
            <Wallet className="h-4 w-4" />
            Payout Settings
          </Button>
        </div>
      </div>
      
      <Dialog open={showPayoutSettings} onOpenChange={setShowPayoutSettings}>
        <PayoutModeDialog onClose={() => setShowPayoutSettings(false)} />
      </Dialog>
      
      {/*
      <Dialog open={showPaymentProofModal} onOpenChange={setShowPaymentProofModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Submit Payment Proof</DialogTitle>
          </DialogHeader>
          <PaymentProofUpload 
            onSubmit={handleDirectPaymentProofSubmit}
            isLoading={false}
          />
        </DialogContent>
      </Dialog>
      */}
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="deposit">Deposit</TabsTrigger>
          <TabsTrigger value="withdraw">Withdraw</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
        </TabsList>
        
        <TabsContent value="deposit" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Make a Deposit</CardTitle>
              <CardDescription>
                Deposit funds into your savings account
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderDepositStep()}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="withdraw" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Request Withdrawal</CardTitle>
              <CardDescription>
                Withdraw funds from your savings account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <WithdrawalRequestForm onSubmit={handleWithdrawalSubmit} />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Transaction History</CardTitle>
              <CardDescription>
                View your recent deposit and withdrawal transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type</TableHead>
                    <TableHead>Amount (₦)</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.length > 0 ? (
                    transactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell className="font-medium capitalize">{transaction.type}</TableCell>
                        <TableCell>{transaction.amount.toLocaleString()}</TableCell>
                        <TableCell>{transaction.date}</TableCell>
                        <TableCell>{transaction.reference}</TableCell>
                        <TableCell>
                          <Badge 
                            variant={getStatusBadgeVariant(transaction.status)}
                          >
                            {transaction.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                        No transactions yet
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
