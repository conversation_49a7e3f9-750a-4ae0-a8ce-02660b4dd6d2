
import { supabase } from '@/integrations/supabase/client';

export interface KycDocument {
  userId: string;
  documentType: 'id_card' | 'passport' | 'drivers_license' | 'utility_bill' | 'bank_statement' | 'other';
  file: File;
  metadata?: Record<string, any>;
}

export const KycService = {
  // Upload KYC document
  uploadDocument: async (document: KycDocument) => {
    try {
      const formData = new FormData();
      formData.append('file', document.file);
      formData.append('userId', document.userId);
      formData.append('documentType', document.documentType);
      
      if (document.metadata) {
        formData.append('metadata', JSON.stringify(document.metadata));
      }
      
      // Call the Supabase Edge Function
      const functionUrl = import.meta.env.VITE_SUPABASE_FUNCTION_URL || 'https://vqxbuhicyaukgscncdud.supabase.co/functions/v1';
      const response = await fetch(
        `${functionUrl}/upload-kyc-document`,
        {
          method: 'POST',
          body: formData,
        }
      );
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload KYC document');
      }
      
      const successData = await response.json();
      return { data: successData, error: null };
    } catch (error) {
      console.error('KYC document upload error:', error);
      return { data: null, error };
    }
  },

  // Get KYC verification status
  getVerificationStatus: async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('kyc_documents')
        .select('*')
        .eq('user_id', userId);
        
      if (error) throw error;
      
      return { data, error: null };
    } catch (error) {
      console.error('Get KYC status error:', error);
      return { data: null, error };
    }
  }
};
