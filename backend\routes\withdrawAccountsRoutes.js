// Renamed from withdrawAcountsRoutes.js to withdrawAccountsRoutes.js
const express = require('express');
const router = express.Router();
const WithdrawAccount = require('../models/withdrawAccounts');
const authMiddleware = require('../middleware/authMiddleware');

// Create a new withdrawal account
router.post('/cwa', authMiddleware, async (req, res) => {
  try {
    console.log('[POST /cwa] req.user:', req.user);
    if (!req.user || !req.user.id) {
      console.error('[POST /cwa] No user found in req.user. JWT may be missing or invalid.');
      return res.status(401).json({ error: 'User authentication failed. Please log in again.' });
    }
    const { bankName, accountNumber, accountName, accountType, isDefault } = req.body;
    const userId = req.user.id;

    // Validate required fields
    if (!bankName || !accountNumber || !accountName || !accountType) {
      return res.status(400).json({ error: 'All fields are required.' });
    }
    if (!['Savings', 'Current'].includes(accountType)) {
      return res.status(400).json({ error: 'Account type must be Savings or Current.' });
    }
    if (!/^[0-9]{10}$/.test(accountNumber)) {
      return res.status(400).json({ error: 'Account number must be 10 digits.' });
    }

    const newAccount = new WithdrawAccount({
      userId,
      bankName,
      accountNumber,
      accountName,
      accountType,
      isDefault,
    });
    const savedAccount = await newAccount.save();
    res.status(201).json(savedAccount);
  } catch (error) {
    console.error('Error creating withdrawal account:', error); // Log the error
    console.error('Request body:', req.body); // Log the incoming request body
    console.error('Validation error details:', error.errors); // Log detailed validation errors
    res.status(500).json({ error: 'Failed to create withdrawal account', details: error.message });
  }
});

// Update an existing withdrawal account
router.put('/wa/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;
    const updatedAccount = await WithdrawAccount.findByIdAndUpdate(id, updates, { new: true });
    if (!updatedAccount) {
      return res.status(404).json({ error: 'Withdrawal account not found' });
    }
    res.status(200).json(updatedAccount);
  } catch (error) {
    res.status(500).json({ error: 'Failed to update withdrawal account', details: error.message });
  }
});

// Delete a withdrawal account
router.delete('/wa/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const deletedAccount = await WithdrawAccount.findByIdAndDelete(id);
    if (!deletedAccount) {
      return res.status(404).json({ error: 'Withdrawal account not found' });
    }
    res.status(200).json({ message: 'Withdrawal account deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to delete withdrawal account', details: error.message });
  }
});

// Get all withdrawal accounts
router.get('/all', async (req, res) => {
  try {
    const accounts = await WithdrawAccount.find();
    res.status(200).json(accounts);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch withdrawal accounts', details: error.message });
  }
});

// Get withdrawal accounts for a specific user
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    if (!userId) {
      return res.status(400).json({ error: 'userId parameter is required.' });
    }
    const accounts = await WithdrawAccount.find({ userId });
    if (!accounts || accounts.length === 0) {
      return res.status(404).json({ error: 'No withdrawal accounts found for this user.' });
    }
    res.status(200).json(accounts);
  } catch (error) {
    console.error('Error fetching withdrawal accounts for user:', error);
    res.status(500).json({ error: 'Failed to fetch withdrawal accounts', details: error.message });
  }
});

// Get withdrawal accounts for the logged-in user (used by frontend)
router.get('/', authMiddleware, async (req, res) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'User authentication failed. Please log in again.' });
    }
    const userId = req.user.id;
    const accounts = await WithdrawAccount.find({ userId });
    res.status(200).json(accounts);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch withdrawal accounts', details: error.message });
  }
});

// Get count of pending withdrawals for a specific user (for sidebar badge)
router.get('/user/:userId/pending-count', async (req, res) => {
  try {
    const { userId } = req.params;
    if (!userId) {
      return res.status(400).json({ error: 'userId parameter is required.' });
    }
    // Assuming status field is 'pending' for pending withdrawals
    const count = await WithdrawAccount.countDocuments({ userId, status: 'pending' });
    res.status(200).json({ count });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch pending withdrawal count', details: error.message });
  }
});

module.exports = router;
