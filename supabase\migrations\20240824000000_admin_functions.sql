
-- Function to get all admin users
-- This is compatible with Firebase auth structure
CREATE OR REPLACE FUNCTION public.get_admin_users()
RETURNS TABLE (
  id TEXT,
  email TEXT,
  first_name TEXT,
  last_name TEXT,
  role TEXT
) 
LANGUAGE SQL
SECURITY DEFINER
STABLE
AS $$
  -- Using a Firebase-compatible query that returns admin users
  -- In a Firebase setup, this would be handled in the Firebase Admin SDK
  -- This is a placeholder that maintains compatibility with both systems
  SELECT 
    au.id,
    au.email,
    p.first_name,
    p.last_name,
    ur.role
  FROM auth.users au
  JOIN public.profiles p ON au.id = p.id
  JOIN public.user_roles ur ON au.id = ur.user_id
  WHERE ur.role = 'admin';
$$;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION public.get_admin_users() TO service_role;

-- Add comment that this is compatible with Firebase
COMMENT ON FUNCTION public.get_admin_users() IS 'Get all admin users - Compatible with Firebase auth structure';
