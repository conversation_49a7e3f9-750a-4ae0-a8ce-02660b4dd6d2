
import { useState } from 'react';
import { sendNotification } from '@/services/notification/notification-service';

// Types
export type NotificationType = 'success' | 'error' | 'info' | 'warning';
export type NotificationChannel = 'in-app' | 'email' | 'sms' | 'all';

export interface NotificationData {
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  channel: NotificationChannel;
  priority?: 'low' | 'medium' | 'high';
  actionUrl?: string;
  metadata?: Record<string, any> | null;
}

export const useNotificationsApi = () => {
  const [isLoading, setIsLoading] = useState(false);

  const send = async (data: NotificationData) => {
    setIsLoading(true);
    try {
      const result = await sendNotification(data);
      return { success: result.success, error: result.success ? null : result.error };
    } catch (error) {
      console.error('Error sending notification:', error);
      return { success: false, error };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    sendNotification: send
  };
};
