import { useState, useEffect } from 'react';
import { AuthService } from '@/services/auth/auth-service';

export interface User {
  id: string;
  email: string;
  profile?: {
    first_name: string;
    last_name: string;
    phone?: string;
    avatar_url?: string;
    status?: string;
    kyc_status?: string;
  };
  role?: string;
  isAdmin?: boolean;
}

export interface SignInCredentials {
  email: string;
  password: string;
}

export interface SignUpData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

export interface CreateUserData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  isAdmin?: boolean;
}

export const useAuthApi = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  const isAuthenticated = !!user;

  // Initialize user from localStorage on mount
  useEffect(() => {
    const initializeAuth = () => {
      try {
        const storedUser = localStorage.getItem('auth_user');
        if (storedUser) {
          const parsedUser = JSON.parse(storedUser);
          setUser(parsedUser);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        localStorage.removeItem('auth_user');
      } finally {
        setIsInitialized(true);
      }
    };

    initializeAuth();
  }, []);

  const signIn = async (credentials: SignInCredentials) => {
    setIsLoading(true);
    try {
      const { data, error } = await AuthService.signIn(credentials.email, credentials.password);
      
      if (error) {
        return { data: null, error: error.message || 'Failed to sign in' };
      }

      if (data?.user) {
        const userData: User = {
          id: data.user.id,
          email: data.user.email,
          profile: {
            first_name: data.user.profile?.first_name || '',
            last_name: data.user.profile?.last_name || '',
            phone: data.user.profile?.phone,
            avatar_url: data.user.profile?.avatar_url,
            status: data.user.profile?.status,
            kyc_status: data.user.profile?.kyc_status
          },
          role: data.user.role || 'user',
          isAdmin: data.user.isAdmin || false
        };
        
        setUser(userData);
        localStorage.setItem('auth_user', JSON.stringify(userData));
        if (data.token) {
          localStorage.setItem('access_token', data.token);
        }
        return { data: userData, error: null };
      }

      return { data: null, error: 'Invalid response from server' };
    } catch (error) {
      console.error('Sign in error:', error);
      return { data: null, error: 'An unexpected error occurred' };
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (userData: SignUpData) => {
    setIsLoading(true);
    try {
      const { data, error } = await AuthService.signUp(
        userData.email, 
        userData.password, 
        {
          first_name: userData.firstName,
          last_name: userData.lastName,
          phone: userData.phone
        }
      );
      
      if (error) {
        return { data: null, error: error.message || 'Failed to create account' };
      }

      if (data?.user) {
        // Backend does not return user object, so just return email and names
        const newUser: User = {
          id: '',
          email: data.user.email,
          profile: {
            first_name: data.user.first_name || '',
            last_name: data.user.last_name || '',
            phone: data.user.phone || '',
          },
          role: 'user',
          isAdmin: false
        };
        setUser(newUser);
        localStorage.setItem('auth_user', JSON.stringify(newUser));
        return { data: newUser, error: null };
      }

      return { data: null, error: 'Invalid response from server' };
    } catch (error) {
      console.error('Sign up error:', error);
      return { data: null, error: 'An unexpected error occurred' };
    } finally {
      setIsLoading(false);
    }
  };

  const createUser = async (userData: CreateUserData) => {
    setIsLoading(true);
    try {
      const { data, error } = await AuthService.signUp(
        userData.email, 
        userData.password, 
        {
          first_name: userData.firstName,
          last_name: userData.lastName,
          phone: userData.phone
        }
      );
      
      if (error) {
        return { data: null, error: error.message || 'Failed to create user' };
      }

      return { data: data?.user, error: null };
    } catch (error) {
      console.error('Create user error:', error);
      return { data: null, error: 'An unexpected error occurred' };
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = () => {
    setUser(null);
    localStorage.removeItem('auth_user');
    AuthService.signOut();
  };

  const getCurrentUser = async () => {
    try {
      const storedUser = localStorage.getItem('auth_user');
      if (storedUser) {
        const parsedUser = JSON.parse(storedUser);
        setUser(parsedUser);
        return { data: parsedUser, error: null };
      }
      return { data: null, error: 'No user found' };
    } catch (error) {
      console.error('Get current user error:', error);
      return { data: null, error: 'Failed to get current user' };
    }
  };

  return {
    user,
    isLoading,
    isAuthenticated,
    isInitialized,
    signIn,
    signUp,
    createUser,
    signOut,
    getCurrentUser
  };
};
