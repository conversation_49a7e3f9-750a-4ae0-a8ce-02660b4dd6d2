
import React, { useState } from "react";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { CreditCard, Building2, User, CircleDollarSign } from "lucide-react";
import { useBalance } from "@/hooks/use-balance";

interface WithdrawalRequestFormProps {
  onSubmit: (data: any) => void;
  isLoading?: boolean;
}

export function WithdrawalRequestForm({ 
  onSubmit, 
  isLoading = false
}: WithdrawalRequestFormProps) {
  const { balance } = useBalance();
  const [formData, setFormData] = useState({
    amount: "",
    bankCode: "",
    accountNumber: "",
    accountName: "",
  });
  const [banks, setBanks] = useState([]);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isVerified, setIsVerified] = useState(false);

  // Fetch Paystack banks list on mount
  useEffect(() => {
    fetch(`${import.meta.env.VITE_BACKEND_URL}/api/paystack/banks`)
      .then(res => res.json())
      .then(data => setBanks(data.data || []));
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (name === "accountNumber") {
      setIsVerified(false);
      setFormData(prev => ({ ...prev, accountName: "" }));
    }
  };

  const handleVerifyAccount = async () => {
    if (!formData.bankCode || !formData.accountNumber) {
      toast.error("Select a bank and enter account number");
      return;
    }
    setIsVerifying(true);
    try {
      const res = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/paystack/resolve-account`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          bank_code: formData.bankCode,
          account_number: formData.accountNumber
        })
      });
      const data = await res.json();
      if (data.status && data.data) {
        setFormData(prev => ({ ...prev, accountName: data.data.account_name }));
        setIsVerified(true);
        toast.success("Account verified: " + data.data.account_name);
      } else {
        setIsVerified(false);
        setFormData(prev => ({ ...prev, accountName: "" }));
        toast.error(data.message || "Account verification failed");
      }
    } catch (err) {
      setIsVerified(false);
      setFormData(prev => ({ ...prev, accountName: "" }));
      toast.error("Error verifying account");
    }
    setIsVerifying(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const amount = parseFloat(formData.amount);
    if (amount <= 0) {
      toast.error("Please enter a valid withdrawal amount");
      return;
    }
    if (amount > balance) {
      toast.error("Your withdrawal amount exceeds your available balance");
      return;
    }
    if (!formData.bankCode || !formData.accountNumber || !formData.accountName || !isVerified) {
      toast.error("Please verify your bank details");
      return;
    }
    // Call backend to initiate Paystack transfer
    onSubmit({
      amount: formData.amount,
      bankCode: formData.bankCode,
      accountNumber: formData.accountNumber,
      accountName: formData.accountName
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="bg-brand-blue text-brand-yellow p-4 rounded-md mb-4 border border-brand-yellow/30 shadow-yellow">
        <div className="flex items-center gap-2">
          <CircleDollarSign className="h-5 w-5" />
          <p className="font-medium">Available Balance: ₦{balance.toLocaleString()}</p>
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="amount" className="flex items-center gap-1">
          <CircleDollarSign className="h-4 w-4 text-muted-foreground" />
          Withdrawal Amount (₦)
        </Label>
        <Input
          id="amount"
          name="amount"
          type="number"
          value={formData.amount}
          onChange={handleChange}
          placeholder="Enter amount to withdraw"
          className="border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30"
          required
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="bankCode" className="flex items-center gap-1">
          <Building2 className="h-4 w-4 text-muted-foreground" />
          Bank
        </Label>
        <select
          id="bankCode"
          name="bankCode"
          value={formData.bankCode}
          onChange={handleChange}
          className="border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30 w-full rounded-md p-2"
          required
        >
          <option value="">Select bank</option>
          {[...new Map(banks.map((bank: any) => [bank.code, bank])).values()].map((bank: any) => (
            <option key={bank.code} value={bank.code}>{bank.name}</option>
          ))}
        </select>
      </div>
      <div className="space-y-2">
        <Label htmlFor="accountNumber" className="flex items-center gap-1">
          <CreditCard className="h-4 w-4 text-muted-foreground" />
          Account Number
        </Label>
        <div className="flex gap-2">
          <Input
            id="accountNumber"
            name="accountNumber"
            value={formData.accountNumber}
            onChange={handleChange}
            placeholder="Enter account number"
            className="border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30"
            required
            maxLength={10}
          />
          <Button type="button" onClick={handleVerifyAccount} disabled={isVerifying || !formData.accountNumber || !formData.bankCode} variant="outline">
            {isVerifying ? "Verifying..." : isVerified ? "Verified" : "Verify"}
          </Button>
        </div>
      </div>
      {formData.accountName && (
        <div className="space-y-2">
          <Label htmlFor="accountName" className="flex items-center gap-1">
            <User className="h-4 w-4 text-muted-foreground" />
            Account Name
          </Label>
          <Input
            id="accountName"
            name="accountName"
            value={formData.accountName}
            readOnly
            className="border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30 bg-gray-100"
          />
        </div>
      )}
      <Button 
        type="submit" 
        disabled={isLoading || !isVerified} 
        variant="brand"
        className="w-full shadow-yellow"
      >
        {isLoading ? "Processing..." : "Withdraw to Bank Account"}
      </Button>
    </form>
  );
}
