
import { useState, useCallback } from 'react';
import { toast } from 'sonner';

// Types
export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone: string | null;
  avatar_url: string | null;
  status: string | null;
  kyc_status: string | null;
  created_at: string;
  isAdmin?: boolean;
  roles?: any[];
}

export interface UpdateUserData {
  firstName?: string;
  lastName?: string;
  phone?: string;
  status?: 'active' | 'suspended' | 'blocked';
  isAdmin?: boolean;
}

// Mock users data
const MOCK_USERS: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    first_name: 'Admin',
    last_name: 'User',
    phone: '+2349063978612',
    avatar_url: null,
    status: 'active',
    kyc_status: 'verified',
    created_at: '2024-01-01T00:00:00Z',
    isAdmin: true,
    roles: ['admin']
  },
  {
    id: '2',
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    phone: '+2348012345678',
    avatar_url: null,
    status: 'active',
    kyc_status: 'pending',
    created_at: '2024-01-02T00:00:00Z',
    isAdmin: false,
    roles: ['user']
  },
  {
    id: '3',
    email: '<EMAIL>',
    first_name: 'Jane',
    last_name: 'Smith',
    phone: '+2348087654321',
    avatar_url: null,
    status: 'active',
    kyc_status: 'verified',
    created_at: '2024-01-03T00:00:00Z',
    isAdmin: false,
    roles: ['user']
  }
];

export function useUsersApi() {
  const [users, setUsers] = useState<User[]>(MOCK_USERS);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Get all users
  const getAllUsers = useCallback(async () => {
    setIsLoading(true);
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setUsers(MOCK_USERS);
      return { data: MOCK_USERS, error: null };
    } catch (error) {
      console.error('Error fetching users:', error);
      const message = error instanceof Error ? error.message : 'Failed to fetch users';
      return { data: null, error: message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Get user by ID
  const getUserById = useCallback(async (userId: string) => {
    setIsLoading(true);
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const user = MOCK_USERS.find(u => u.id === userId);
      if (!user) {
        throw new Error('User not found');
      }
      
      setSelectedUser(user);
      return { data: user, error: null };
    } catch (error) {
      console.error('Error fetching user:', error);
      const message = error instanceof Error ? error.message : 'Failed to fetch user';
      return { data: null, error: message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update user
  const updateUser = useCallback(async (userId: string, userData: UpdateUserData) => {
    setIsSaving(true);
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const userIndex = MOCK_USERS.findIndex(u => u.id === userId);
      if (userIndex === -1) {
        throw new Error('User not found');
      }
      
      const updatedUser = {
        ...MOCK_USERS[userIndex],
        first_name: userData.firstName || MOCK_USERS[userIndex].first_name,
        last_name: userData.lastName || MOCK_USERS[userIndex].last_name,
        phone: userData.phone || MOCK_USERS[userIndex].phone,
        status: userData.status || MOCK_USERS[userIndex].status,
        isAdmin: userData.isAdmin !== undefined ? userData.isAdmin : MOCK_USERS[userIndex].isAdmin
      };
      
      MOCK_USERS[userIndex] = updatedUser;
      
      // Update local state
      setSelectedUser(updatedUser);
      setUsers(prev => 
        prev.map(user => 
          user.id === userId ? updatedUser : user
        )
      );
      
      toast.success('User updated successfully');
      return { data: updatedUser, error: null };
    } catch (error) {
      console.error('Error updating user:', error);
      const message = error instanceof Error ? error.message : 'Failed to update user';
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsSaving(false);
    }
  }, []);

  // Delete user
  const deleteUser = useCallback(async (userId: string) => {
    setIsSaving(true);
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const userIndex = MOCK_USERS.findIndex(u => u.id === userId);
      if (userIndex === -1) {
        throw new Error('User not found');
      }
      
      MOCK_USERS.splice(userIndex, 1);
      
      // Update local state
      setUsers(prev => prev.filter(user => user.id !== userId));
      if (selectedUser?.id === userId) {
        setSelectedUser(null);
      }
      
      toast.success('User deleted successfully');
      return { success: true, error: null };
    } catch (error) {
      console.error('Error deleting user:', error);
      const message = error instanceof Error ? error.message : 'Failed to delete user';
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setIsSaving(false);
    }
  }, [selectedUser]);

  // Get current user profile
  const getProfile = useCallback(async () => {
    setIsLoading(true);
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Return first user as current user for demo
      const currentUser = MOCK_USERS[0];
      return { data: currentUser, error: null };
    } catch (error) {
      console.error('Error fetching profile:', error);
      const message = error instanceof Error ? error.message : 'Failed to fetch profile';
      return { data: null, error: message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    users,
    selectedUser,
    isLoading,
    isSaving,
    getAllUsers,
    getUserById,
    updateUser,
    deleteUser,
    getProfile
  };
}
