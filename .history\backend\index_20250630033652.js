require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const authRoutes = require('./routes/userAuth');
const savingsRoutes = require('./routes/savingsRoutes');
const kycRoutes = require('./routes/kycRoutes');
const transactionRoutes = require('./routes/transactionRoutes');
const profitRoutes = require('./routes/profitRoutes');
const accountsRoutes = require('./routes/accountsRoutes'); // Import accounts routes
const withdrawAccountsRoutes = require('./routes/withdrawAccountsRoutes'); // Import withdraw accounts routes
const depositRoutes = require('./routes/depositRoutes'); // Import deposit routes
const userInfoRoutes = require('./routes/userInfo');
const withdrawRoutes = require('./routes/withdrawRoutes'); // Import userInfo routes
const cors = require('cors');
const notificationRoutes = require('./routes/notificationRoutes'); // Import notification routes
const statsRoutes = require('./routes/statsRoutes'); // Import stats routes
const errorHandler = require('./middleware/errorHandler'); // Import error handler middleware

// Require the savings scheduler to enable automated savings plan deductions
require('./scheduler/savingsScheduler');

const app = express();

// CORS configuration
const corsOptions = {
  origin: [
    'http://localhost:8010',
    process.env.FRONTEND_URL, // fallback for safety
  ],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'], // Add PATCH here
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
};
console.log('CORS allowed origins:', corsOptions.origin);

app.use(cors(corsOptions));
app.use(express.json());

app.use('/api/auth', authRoutes);
app.use('/api/users', authRoutes); // <-- Add this line to expose /api/users/recent
app.use('/api/savings', savingsRoutes);
app.use('/api/kyc', kycRoutes);
app.use('/api/transactions', transactionRoutes);
app.use('/api/profits', profitRoutes);
app.use('/api/accounts', accountsRoutes); // Add accounts routes
app.use('/api/withdraw-accounts', withdrawAccountsRoutes); // Add withdraw accounts routes
app.use('/api/deposits', depositRoutes); // Add deposit routes
app.use('/api/userinfo', userInfoRoutes); // Add userInfo routes
app.use('/api/withdraw', withdrawRoutes); // Add withdraw routes
app.use('/api/notifications', notificationRoutes); // Add notification routes
app.use('/api', statsRoutes); // Add stats routes
app.use(errorHandler); // Use the error handler middleware

mongoose.connect(process.env.MONGO_URI)
  .then(() => {
    console.log('MongoDB connected');
    app.listen(8080, () => console.log('Server running on http://localhost:8080'));
  })
  .catch(err => console.log(err));
