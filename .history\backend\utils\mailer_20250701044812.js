
const nodemailer = require('nodemailer');

// Hardcoded transporter for testing
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: '<EMAIL>',
    pass: 'iludrwqrzagirofu',
  },
});

/**
 * Send a group invite email
 * @param {string} to - Recipient email
 * @param {string} groupName - Name of the group
 * @param {string} inviteLink - Link for the user to join the group
 * @returns {Promise}
 */
async function sendGroupInvite(to, groupName, inviteLink) {
  const mailOptions = {
    from: process.env.EMAIL_FROM || process.env.EMAIL_USER,
    to,
    subject: `You're invited to join the group savings plan: ${groupName}`,
    html: `
      <p>Hello,</p>
      <p>You have been invited to join the group savings plan <b>${groupName}</b>.</p>
      <p>Click <a href="${inviteLink}">here</a> to join, or copy and paste this link into your browser:</p>
      <p>${inviteLink}</p>
      <p>If you did not expect this invitation, you can ignore this email.</p>
      <p>Best regards,<br/>Asusu Team</p>
    `,
  };
  return transporter.sendMail(mailOptions);
}

module.exports = {
  sendGroupInvite,
};
