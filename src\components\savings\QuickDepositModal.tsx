
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PlusCircle, CreditCard, Smartphone, Building2 } from 'lucide-react';
import { toast } from 'sonner';

interface QuickDepositModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function QuickDepositModal({ open, onOpenChange }: QuickDepositModalProps) {
  const [amount, setAmount] = useState('');
  const [method, setMethod] = useState('');
  const [savingsPlan, setSavingsPlan] = useState('');

  const quickAmounts = [1000, 2500, 5000, 10000, 25000, 50000];
  
  const paymentMethods = [
    { value: 'card', label: 'Debit Card', icon: <CreditCard className="h-4 w-4" /> },
    { value: 'transfer', label: 'Bank Transfer', icon: <Building2 className="h-4 w-4" /> },
    { value: 'ussd', label: 'USSD', icon: <Smartphone className="h-4 w-4" /> }
  ];

  const savingsPlans = [
    { value: 'daily-saver', label: 'Daily Saver' },
    { value: 'emergency-fund', label: 'Emergency Fund' },
    { value: 'new-car', label: 'New Car Fund' },
    { value: 'vacation', label: 'Vacation Fund' }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!amount || !method || !savingsPlan) {
      toast.error('Please fill in all fields');
      return;
    }

    console.log('Processing deposit:', { amount, method, savingsPlan });
    toast.success(`Deposit of ₦${Number(amount).toLocaleString()} initiated successfully!`);
    onOpenChange(false);
    
    // Reset form
    setAmount('');
    setMethod('');
    setSavingsPlan('');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <PlusCircle className="h-5 w-5 text-brand-blue" />
            Quick Deposit
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-3">
            <Label>Quick Amount Selection</Label>
            <div className="grid grid-cols-3 gap-2">
              {quickAmounts.map((quickAmount) => (
                <Button
                  key={quickAmount}
                  type="button"
                  variant={amount === quickAmount.toString() ? "default" : "outline"}
                  className="h-12"
                  onClick={() => setAmount(quickAmount.toString())}
                >
                  ₦{quickAmount.toLocaleString()}
                </Button>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="amount">Custom Amount (₦)</Label>
            <Input
              id="amount"
              type="number"
              placeholder="Enter amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label>Select Savings Plan</Label>
            <Select value={savingsPlan} onValueChange={setSavingsPlan}>
              <SelectTrigger>
                <SelectValue placeholder="Choose savings plan" />
              </SelectTrigger>
              <SelectContent>
                {savingsPlans.map((plan) => (
                  <SelectItem key={plan.value} value={plan.value}>
                    {plan.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <Label>Payment Method</Label>
            <div className="grid gap-2">
              {paymentMethods.map((paymentMethod) => (
                <Card
                  key={paymentMethod.value}
                  className={`cursor-pointer transition-colors ${
                    method === paymentMethod.value
                      ? 'border-brand-blue bg-brand-blue/5'
                      : 'hover:border-brand-blue/50'
                  }`}
                  onClick={() => setMethod(paymentMethod.value)}
                >
                  <CardContent className="flex items-center gap-3 p-4">
                    {paymentMethod.icon}
                    <span className="font-medium">{paymentMethod.label}</span>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {amount && (
            <Card className="bg-brand-yellow/10 border-brand-yellow/20">
              <CardHeader>
                <CardTitle className="text-sm">Transaction Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span>Amount:</span>
                  <span className="font-bold">₦{Number(amount).toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Fee:</span>
                  <span>₦0</span>
                </div>
                <hr />
                <div className="flex justify-between font-bold">
                  <span>Total:</span>
                  <span>₦{Number(amount).toLocaleString()}</span>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="flex gap-3 pt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} className="flex-1">
              Cancel
            </Button>
            <Button type="submit" className="flex-1 bg-brand-blue hover:bg-brand-blue/90">
              Continue to Payment
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
