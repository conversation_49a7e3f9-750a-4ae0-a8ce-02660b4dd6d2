const BACKEND_URL = import.meta.env.VITE_BACKEND_URL;

export const StatsService = {
  getDashboardStats: async () => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/transactions/stats`);
      if (!res.ok) throw new Error('Failed to fetch dashboard stats');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      return { data: null, error };
    }
  },
};
