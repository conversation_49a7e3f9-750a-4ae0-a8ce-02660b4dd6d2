const mongoose = require('mongoose');

const WithdrawSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  amount: {
    type: Number,
    required: true,
  },
  status: {
    type: String,
    enum: ['pending', 'success', 'failed'],
    default: 'pending',
  },
  paystackTransferCode: {
    type: String,
    default: '',
  },
  paystackStatus: {
    type: String,
    enum: ['pending', 'success', 'failed'],
    default: 'pending',
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  processedAt: {
    type: Date,
    default: null,
  },
  notes: {
    type: String,
    default: '',
  },
  bankAccountId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'WithdrawAccount',
    required: true,
  },
});

module.exports = mongoose.model('Withdraw', WithdrawSchema);
