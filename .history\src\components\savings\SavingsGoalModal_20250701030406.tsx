
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
// import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CalendarIcon, Target, Users } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

// Connect to backend route for savings plan creation
const API_URL = import.meta.env.VITE_BACKEND_URL || '';

interface SavingsGoalModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function SavingsGoalModal({ open, onOpenChange }: SavingsGoalModalProps) {

  const [formData, setFormData] = useState({
    name: '',
    target: '',
    frequency: '',
    amount: '',
    isGroup: false
  });

  // Calculate deadline as a Date object
  const calculateDeadlineDate = () => {
    const { target, amount, frequency } = formData;
    const targetNum = parseFloat(target);
    const amountNum = parseFloat(amount);
    if (!targetNum || !amountNum || !frequency) return null;
    const contributionsNeeded = Math.ceil(targetNum / amountNum);
    let daysPerContribution = 1;
    switch (frequency) {
      case 'daily': daysPerContribution = 1; break;
      case 'weekly': daysPerContribution = 7; break;
      case 'bi-weekly': daysPerContribution = 14; break;
      case 'monthly': daysPerContribution = 30; break;
      default: daysPerContribution = 1;
    }
    const totalDays = contributionsNeeded * daysPerContribution;
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + totalDays);
    return endDate;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name || !formData.target || !formData.frequency || !formData.amount) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Prepare request body for backend
    const deadlineDate = calculateDeadlineDate();
    const token = localStorage.getItem('access_token');
    const payload = {
      title: formData.name,
      depositFrequency: formData.frequency,
      depositAmount: Number(formData.amount),
      targetDate: deadlineDate ? deadlineDate.toISOString().slice(0, 10) : undefined,
      targetAmount: Number(formData.target)
    };
    try {
      const res = await fetch(`${API_URL}/api/savings/plan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { 'Authorization': `Bearer ${token}` } : {})
        },
        body: JSON.stringify(payload)
      });
      const data = await res.json();
      console.log('[SavingsGoalModal] Backend response:', data);
      if (!res.ok) throw new Error(data.error || 'Failed to create savings plan');
      toast.success('Savings goal created successfully!');
      onOpenChange(false);
      setFormData({
        name: '',
        target: '',
        frequency: '',
        amount: '',
        isGroup: false
      });
    } catch (err: any) {
      console.error('[SavingsGoalModal] Error creating savings plan:', err);
      toast.error(err.message || 'Failed to create savings plan');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-brand-blue" />
            Create Savings Goal
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Goal Name *</Label>
            <Input
              id="name"
              placeholder="e.g., New Car, Emergency Fund"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="target">Target Amount (₦) *</Label>
            <Input
              id="target"
              type="number"
              placeholder="1000000"
              value={formData.target}
              onChange={(e) => setFormData(prev => ({ ...prev, target: e.target.value }))}
            />
          </div>



          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="frequency">Frequency *</Label>
              <Select value={formData.frequency} onValueChange={(value) => setFormData(prev => ({ ...prev, frequency: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="bi-weekly">Bi-weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Amount per Contribution (₦) *</Label>
              <Input
                id="amount"
                type="number"
                placeholder="5000"
                value={formData.amount}
                onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label>Target Deadline (auto-calculated)</Label>
            <Input
              value={(() => { const d = calculateDeadlineDate(); return d ? format(d, 'PPP') : ''; })()}
              readOnly
              className="bg-muted-foreground/10 cursor-not-allowed"
              placeholder="Deadline will be calculated"
            />
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isGroup"
              checked={formData.isGroup}
              onChange={(e) => setFormData(prev => ({ ...prev, isGroup: e.target.checked }))}
              className="rounded"
            />
            <Label htmlFor="isGroup" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Make this a group savings goal
            </Label>
          </div>

          <div className="flex gap-3 pt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} className="flex-1">
              Cancel
            </Button>
            <Button type="submit" className="flex-1 bg-brand-blue hover:bg-brand-blue/90">
              Create Goal
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
