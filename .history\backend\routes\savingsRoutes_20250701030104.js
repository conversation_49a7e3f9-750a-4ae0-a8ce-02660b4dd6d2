const express = require('express');
const SavingsPlan = require('../models/savingsPlan');
const Transaction = require('../models/transaction');
const mongoose = require('mongoose');
const authMiddleware = require('../middleware/authMiddleware');
const router = express.Router();

// Create a new savings plan
router.post('/plan', authMiddleware, async (req, res) => {
  try {
    const { title, depositFrequency, depositAmount, targetDate, targetAmount } = req.body;
    const userId = req.user && req.user.id;
    if (!userId) {
      return res.status(401).json({ error: 'User authentication failed. Please log in again.' });
    }
    if (!title || !depositFrequency || !depositAmount || !targetDate || !targetAmount) {
      return res.status(400).json({ error: 'All fields are required.' });
    }
    if (typeof depositAmount !== 'number' || depositAmount <= 0) {
      return res.status(400).json({ error: 'Deposit amount must be a positive number.' });
    }
    if (typeof targetAmount !== 'number' || targetAmount <= 0) {
      return res.status(400).json({ error: 'Target amount must be a positive number.' });
    }
    // Optionally: validate date format
    if (isNaN(Date.parse(targetDate))) {
      return res.status(400).json({ error: 'Target date is invalid.' });
    }
    const newPlan = new SavingsPlan({
      title,
      depositFrequency,
      depositAmount,
      targetDate,
      targetAmount,
      userId,
    });
    const savedPlan = await newPlan.save();
    res.status(201).json(savedPlan);
  } catch (error) {
    console.error('Error creating savings plan:', error); // Log the detailed error
    res.status(500).json({ error: 'Failed to create savings plan', details: error.message });
  }
});

// Get all savings plans for the logged-in user, plus summary
router.get('/my', authMiddleware, async (req, res) => {
  try {
    const userId = req.user && req.user.id;
    console.log('[SavingsRoute] /my called for user:', userId);
    if (!userId) {
      console.error('[SavingsRoute] No userId found in request.');
      return res.status(401).json({ error: 'User authentication failed. Please log in again.' });
    }

    // Fetch plans
    let plans = [];
    try {
      plans = await SavingsPlan.find({ userId }).sort({ createdAt: -1 });
      console.log('[SavingsRoute] Plans found:', plans.length, plans);
    } catch (err) {
      console.error('[SavingsRoute] Error fetching plans:', err);
      return res.status(500).json({ error: 'Failed to fetch savings plans', details: err.message });
    }

    // Calculate summary statistics
    const totalPlans = plans.length;
    const now = new Date();
    const activePlans = plans.filter(plan => {
      try {
        return new Date(plan.targetDate) > now;
      } catch (e) {
        console.error('[SavingsRoute] Error parsing plan.targetDate:', plan.targetDate, e);
        return false;
      }
    }).length;
    const completedPlans = totalPlans - activePlans;

    // Fetch transactions
    let transactions = [];
    try {
      transactions = await Transaction.find({ 
        userId, 
        type: 'deposit',
        status: 'completed'
      });
      console.log('[SavingsRoute] Transactions found:', transactions.length, transactions);
    } catch (err) {
      console.error('[SavingsRoute] Error fetching transactions:', err);
      return res.status(500).json({ error: 'Failed to fetch transactions', details: err.message });
    }

    const totalSaved = transactions.reduce((sum, transaction) => sum + (transaction.amount || 0), 0);
    const totalTargetAmount = plans.reduce((sum, plan) => sum + (plan.targetAmount || 0), 0);
    const savingsProgress = totalTargetAmount > 0 ? (totalSaved / totalTargetAmount) * 100 : 0;

    console.log('[SavingsRoute] Summary:', {
      totalPlans,
      activePlans,
      completedPlans,
      totalSaved,
      totalTargetAmount,
      savingsProgress
    });

    // Ensure all plans have startDate and endDate (targetDate) fields in ISO string format
    const plansWithDates = plans.map(plan => {
      // If using Mongoose, plan is a document, so convert to plain object
      const plain = plan.toObject ? plan.toObject() : { ...plan };
      // Use createdAt as startDate, targetDate as endDate
      return {
        ...plain,
        startDate: plain.createdAt ? new Date(plain.createdAt).toISOString() : null,
        endDate: plain.targetDate ? new Date(plain.targetDate).toISOString() : null,
      };
    });

    res.status(200).json({
      plans: plansWithDates,
      summary: {
        totalPlans,
        activePlans,
        completedPlans,
        totalSaved,
        totalTargetAmount,
        savingsProgress
      }
    });
  } catch (error) {
    console.error('[SavingsRoute] Error in /my route:', error);
    res.status(500).json({ error: 'Failed to fetch savings plans', details: error.message });
  }
});

// Get savings plans with pagination and filtering
router.get('/plans', async (req, res) => {
  try {
    const { status, limit = 10, page = 1 } = req.query;
    // Try to get userId from auth if available, else allow public fetch (for all plans)
    let userId = undefined;
    if (req.user && req.user.id) {
      userId = req.user.id;
    }
    // Build query
    const query = {};
    if (userId) query.userId = userId;
    if (status) query.status = status;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Fetch plans with pagination
    const plans = await SavingsPlan.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const totalCount = await SavingsPlan.countDocuments(query);

    res.status(200).json({
      plans,
      pagination: {
        total: totalCount,
        page: parseInt(page),
        pages: Math.ceil(totalCount / parseInt(limit)),
      },
    });
  } catch (error) {
    console.error('Error fetching savings plans:', error);
    res.status(500).json({ error: 'Failed to fetch savings plans', details: error.message });
  }
});

// Get savings summary for the logged-in user
router.get('/summary', authMiddleware, async (req, res) => {
  try {
    const userId = req.user.id;
    // Get all plans for the user
    const plans = await SavingsPlan.find({ userId });
    // Calculate summary statistics
    const totalPlans = plans.length;
    const activePlans = plans.filter(plan => new Date(plan.targetDate) > new Date()).length;
    const completedPlans = totalPlans - activePlans;
    // Calculate total saved amount from transactions
    const transactions = await Transaction.find({ 
      userId, 
      type: 'deposit',
      status: 'completed'
    });
    const totalSaved = transactions.reduce((sum, transaction) => sum + transaction.amount, 0);
    const totalTargetAmount = plans.reduce((sum, plan) => sum + plan.targetAmount, 0);
    res.status(200).json({
      totalPlans,
      activePlans,
      completedPlans,
      totalSaved,
      totalTargetAmount,
      savingsProgress: totalTargetAmount > 0 ? (totalSaved / totalTargetAmount) * 100 : 0,
    });
  } catch (error) {
    console.error('Error fetching savings summary:', error);
    res.status(500).json({ error: 'Failed to fetch savings summary', details: error.message });
  }
});

// Update a savings plan
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updatedPlan = await SavingsPlan.findByIdAndUpdate(id, req.body, { new: true });
    res.status(200).json(updatedPlan);
  } catch (error) {
    res.status(500).json({ error: 'Failed to update savings plan', details: error.message });
  }
});

// Delete a savings plan
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    await SavingsPlan.findByIdAndDelete(id);
    res.status(200).json({ message: 'Savings plan deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to delete savings plan', details: error.message });
  }
});

module.exports = router;