import {
  BarChart3,
  CreditCard,
  DollarSign,
  FileText,
  TrendingUp,
  UserPlus,
  Users,
  Shield,
  UserCog,
  Settings,
  LockKeyhole,
  MessageSquare,
  Award,
  Bell,
  BookOpen,
  Target,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  Wallet,
  Clipboard,
} from "lucide-react";
import { StatCard } from "@/components/ui/stat-card";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { RevenueCard } from "@/components/admin/RevenueCard";

const pendingRequestsData = [
  {
    id: "r1",
    user: { name: "<PERSON><PERSON>", initials: "CO" },
    type: "Withdrawal",
    amount: 50000,
    date: "2023-05-25",
  },
  {
    id: "r2",
    user: { name: "Emeka Eze", initials: "EE" },
    type: "Account Update",
    amount: null,
    date: "2023-05-24",
  },
  {
    id: "r3",
    user: { name: "Funke Adebayo", initials: "FA" },
    type: "Withdrawal",
    amount: 20000,
    date: "2023-05-24",
  },
  {
    id: "r4",
    user: { name: "Damilola Ogunleye", initials: "DO" },
    type: "Account Update",
    amount: null,
    date: "2023-05-23",
  },
];

const recentUsersData = [
  {
    id: "u1",
    name: "Grace Nwabueze",
    initials: "GN",
    email: "<EMAIL>",
    phone: "+234 ************",
    registeredOn: "2023-05-25",
  },
  {
    id: "u2",
    name: "Ibrahim Mohammed",
    initials: "IM",
    email: "<EMAIL>",
    phone: "+234 ************",
    registeredOn: "2023-05-24",
  },
  {
    id: "u3",
    name: "Joshua Taiwo",
    initials: "JT",
    email: "<EMAIL>",
    phone: "+234 ************",
    registeredOn: "2023-05-23",
  },
];

const complianceData = [
  {
    id: "c1",
    category: "KYC Verification",
    status: "Critical",
    completionRate: 78,
    lastUpdated: "2023-05-24",
  },
  {
    id: "c2",
    category: "AML Checks",
    status: "Good",
    completionRate: 92,
    lastUpdated: "2023-05-25",
  },
  {
    id: "c3",
    category: "Data Privacy",
    status: "Warning",
    completionRate: 85,
    lastUpdated: "2023-05-23",
  },
];

const rewardProgramsData = [
  {
    id: "r1",
    name: "Savings Challenge",
    participants: 145,
    progress: 68,
    reward: "₦5,000 bonus",
  },
  {
    id: "r2",
    name: "Referral Program",
    participants: 87,
    progress: 42,
    reward: "₦2,000 cashback",
  },
  {
    id: "r3",
    name: "Consistent Saver",
    participants: 253,
    progress: 91,
    reward: "3% interest bonus",
  },
];

const adminRoles = [
  {
    title: "User Manager",
    description: "Manage user accounts",
    icon: <UserCog className="h-12 w-12 text-brand-blue" />,
    link: "/admin/users"
  },
  {
    title: "Security",
    description: "Manage system security",
    icon: <Shield className="h-12 w-12 text-brand-blue" />,
    link: "/admin/settings"
  },
  {
    title: "Support",
    description: "Handle customer inquiries",
    icon: <MessageSquare className="h-12 w-12 text-brand-blue" />,
    link: "/admin/requests"
  },
  {
    title: "Staff Roles",
    description: "Manage staff permissions",
    icon: <UserCog className="h-12 w-12 text-brand-blue" />,
    link: "/admin/staff-roles"
  }
];

const AdminDashboard = () => {
  const navigate = useNavigate();

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Admin Dashboard</h2>
        <p className="text-muted-foreground">
          System operations overview
        </p>
      </div>

      {/* System Activity - Moved to top */}
      <div className="grid gap-4 grid-cols-1 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>System Activity</CardTitle>
            <CardDescription>
              Usage statistics and system overview
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex items-center justify-center bg-muted/50 rounded-md">
              <p className="text-muted-foreground">Activity chart will be displayed here</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 grid-cols-2 sm:grid-cols-2 md:grid-cols-3">
        {adminRoles.map((role, index) => (
          <Card 
            key={index} 
            className="transition-all duration-300 hover:shadow-yellow hover:-translate-y-1 cursor-pointer border-brand-yellow/20 hover:border-brand-yellow"
            onClick={() => navigate(role.link)}
          >
            <CardContent className="pt-6 flex flex-col items-center text-center p-4">
              <div className="p-3 rounded-full bg-brand-yellow/10 mb-4">
                {role.icon}
              </div>
              <h3 className="font-semibold text-lg">{role.title}</h3>
              <p className="text-sm text-muted-foreground">{role.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <StatCard
          title="Total Users"
          value="2,534"
          icon={<Users className="h-4 w-4" />}
          trend={{ value: 8, isPositive: true }}
        />
        <StatCard
          title="New Users"
          value="245"
          icon={<UserPlus className="h-4 w-4" />}
          trend={{ value: 12, isPositive: true }}
        />
        <StatCard
          title="Total Savings"
          value="₦125.75M"
          icon={<Wallet className="h-4 w-4" />}
          trend={{ value: 6, isPositive: true }}
        />
        <StatCard
          title="Pending Requests"
          value="17"
          icon={<FileText className="h-4 w-4" />}
          trend={{ value: 3, isPositive: false }}
        />
        <RevenueCard
          title="Revenue"
          value="₦4.25M"
          period="Current Month"
          trend={{ value: 14, isPositive: true }}
          className="col-span-1"
        />
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div>
            <CardTitle>Security & Compliance</CardTitle>
            <CardDescription>
              System security status and compliance metrics
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={() => navigate('/admin/settings')}>
            Security Settings
          </Button>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Category</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Completion Rate</TableHead>
                <TableHead>Last Updated</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {complianceData.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.category}</TableCell>
                  <TableCell>
                    <Badge 
                      variant={
                        item.status === "Critical" ? "destructive" :
                        item.status === "Warning" ? "outline" : 
                        "success"
                      }
                      className="flex items-center gap-1"
                    >
                      {item.status === "Critical" ? <AlertTriangle className="h-3 w-3" /> :
                       item.status === "Warning" ? <AlertTriangle className="h-3 w-3" /> : 
                       <CheckCircle2 className="h-3 w-3" />}
                      {item.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="w-full">
                      <div className="flex justify-between mb-1 text-xs">
                        <span>{item.completionRate}%</span>
                      </div>
                      <Progress value={item.completionRate} />
                    </div>
                  </TableCell>
                  <TableCell>{item.lastUpdated}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div>
              <CardTitle>Pending Requests</CardTitle>
              <CardDescription>
                Awaiting approval
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={() => navigate('/admin/requests')}>
              View All
            </Button>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {pendingRequestsData.map((request) => (
                  <TableRow key={request.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className="text-xs">
                            {request.user.initials}
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-medium">{request.user.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        request.type === "Withdrawal" 
                          ? "bg-yellow-100 text-yellow-800" 
                          : "bg-blue-100 text-blue-800"
                      }`}>
                        {request.type}
                      </span>
                    </TableCell>
                    <TableCell>
                      {request.amount 
                        ? `₦${request.amount.toLocaleString()}`
                        : "-"}
                    </TableCell>
                    <TableCell>{request.date}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" size="sm" className="h-8 px-2">
                          Review
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div>
              <CardTitle>Recent Users</CardTitle>
              <CardDescription>
                New registrations
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={() => navigate('/admin/users')}>
              View All
            </Button>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Registered</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentUsersData.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className="text-xs">
                            {user.initials}
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-medium">{user.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="text-xs">{user.email}</span>
                        <span className="text-xs text-muted-foreground">{user.phone}</span>
                      </div>
                    </TableCell>
                    <TableCell>{user.registeredOn}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" size="sm" className="h-8 px-2">
                          View
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div>
            <CardTitle>Goal & Reward Programs</CardTitle>
            <CardDescription>
              Track active savings challenges and rewards
            </CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => navigate('/admin/group-savings')}
            className="flex items-center gap-1"
          >
            <Clipboard className="h-4 w-4" />
            Update Plans
          </Button>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Program</TableHead>
                <TableHead>Participants</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Reward</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {rewardProgramsData.map((program) => (
                <TableRow key={program.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <Award className="h-4 w-4 text-brand-blue" />
                      {program.name}
                    </div>
                  </TableCell>
                  <TableCell>{program.participants}</TableCell>
                  <TableCell>
                    <div className="w-full">
                      <div className="flex justify-between mb-1 text-xs">
                        <span>{program.progress}%</span>
                      </div>
                      <Progress value={program.progress} />
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      {program.reward}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" size="sm" className="h-8 px-2">
                        Details
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

    </div>
  );
};

export default AdminDashboard;

