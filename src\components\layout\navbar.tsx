
import React from 'react';
import { Link } from 'react-router-dom';
import { UserNav } from '@/components/user-nav';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { useAuth } from '@/hooks/use-auth';

interface NavbarProps {
  isAdmin?: boolean;
}

export const Navbar = ({ isAdmin = false }: NavbarProps) => {
  const { user } = useAuth();

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center justify-between px-4">
        <div className="flex items-center space-x-4">
          <Link to={isAdmin ? "/admin/dashboard" : "/user/dashboard"} className="flex items-center space-x-2">
            <span className="font-unica-one text-xl font-bold text-brand-blue">
              ASUSU
            </span>
          </Link>
        </div>

        <div className="flex items-center space-x-4">
          <ThemeToggle />
          {user && <UserNav isAdmin={isAdmin} />}
        </div>
      </div>
    </header>
  );
};

export default Navbar;
