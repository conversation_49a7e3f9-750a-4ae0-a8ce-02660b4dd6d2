
import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { UserAvatar } from "@/components/ui/user-avatar";
import { PhoneInput } from "@/components/profile/PhoneInput";
import { ChangePasswordForm } from "@/components/profile/ChangePasswordForm";
import { ChangePinForm } from "@/components/profile/ChangePinForm";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useTheme } from "@/hooks/use-theme";
import { 
  Shield, Lock, CreditCard, UserRound, 
  Mail, Phone, Computer, Smartphone, Palette, 
  Languages, Coins, Moon, Sun
} from "lucide-react";

export default function Settings() {
  const { toast } = useToast();
  const { theme, toggleTheme } = useTheme();
  const [activeTab, setActiveTab] = useState("general");
  const [isMobileView, setIsMobileView] = useState(window.innerWidth < 768);
  const [user, setUser] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    image: null,
  });

  useEffect(() => {
    // Fetch user profile from new backend endpoint on mount
    const fetchProfile = async () => {
      const token = localStorage.getItem('access_token');
      console.log('[Settings] access_token from localStorage:', token);
      if (!token) {
        console.warn('[Settings] No access_token found in localStorage');
        return;
      }
      try {
        const res = await fetch('http://localhost:8080/api/user/profile', {
          headers: { 'Authorization': `Bearer ${token}` },
        });
        console.log('[Settings] GET /api/user/profile status:', res.status);
        if (!res.ok) {
          const errorText = await res.text();
          console.error('[Settings] Failed to fetch profile:', res.status, errorText);
          return;
        }
        const data = await res.json();
        console.log('[Settings] Profile data received:', data);
        setUser({
          firstName: data.profile?.first_name || data.firstName || data.first_name || "",
          lastName: data.profile?.last_name || data.lastName || data.last_name || "",
          email: data.email || "",
          phone: data.profile?.phone || data.phoneNumber || data.phone || "",
          image: data.profile?.image || data.image || null,
        });
      } catch (e) {
        console.error('[Settings] Error fetching profile:', e);
        setUser({
          firstName: "",
          lastName: "",
          email: "",
          phone: "",
          image: null,
        });
      }
    };
    fetchProfile();
  }, []);
  
  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('access_token');
      console.log('[Settings] access_token for update:', token);
      if (token) {
        const updateBody = {
          firstName: user.firstName,
          lastName: user.lastName,
          phoneNumber: user.phone, // must be phoneNumber for backend
          email: user.email,
        };
        console.log('[Settings] Sending PUT /api/user/profile/update with body:', updateBody);
        const updateRes = await fetch('http://localhost:8080/api/user/profile/update', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(updateBody),
        });
        console.log('[Settings] PUT /api/user/profile/update status:', updateRes.status);
        if (!updateRes.ok) {
          const errorText = await updateRes.text();
          console.error('[Settings] Profile update failed:', updateRes.status, errorText);
          toast({
            title: "Update failed",
            description: errorText || "Could not update profile. Please try again.",
            variant: "destructive",
          });
          return;
        }
        const updateData = await updateRes.json();
        console.log('[Settings] Profile update response:', updateData);
      }
      // Refetch user profile after update
      const res = await fetch('http://localhost:8080/api/user/profile', {
        headers: { 'Authorization': `Bearer ${token}` },
      });
      console.log('[Settings] Refetch profile after update, status:', res.status);
      if (res.ok) {
        const data = await res.json();
        console.log('[Settings] Refetched profile data:', data);
        setUser({
          firstName: data.profile?.first_name || data.firstName || data.first_name || "",
          lastName: data.profile?.last_name || data.lastName || data.last_name || "",
          email: data.email || "",
          phone: data.profile?.phone || data.phoneNumber || data.phone || "",
          image: data.profile?.image || data.image || null,
        });
      } else {
        const errorText = await res.text();
        console.error('[Settings] Failed to refetch profile after update:', res.status, errorText);
      }
      toast({
        title: "Profile updated",
        description: "Your profile information has been updated successfully",
      });
    } catch (err) {
      console.error('[Settings] Error in handleUpdateProfile:', err);
      toast({
        title: "Update failed",
        description: "Could not update profile. Please try again.",
        variant: "destructive",
      });
    }
  };

  const toggleDeviceView = () => {
    setIsMobileView(!isMobileView);
  };

  return (
    <div className={`space-y-6 ${isMobileView ? 'max-w-md mx-auto px-4' : ''}`}>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Account Settings</h1>
          <p className="text-muted-foreground">
            Manage your profile and preferences
          </p>
        </div>

        <div className="flex items-center gap-3">
          <div className="flex items-center space-x-2">
            <Smartphone className="h-4 w-4 text-muted-foreground" />
            <Label htmlFor="mobile-view" className="text-sm">Mobile View</Label>
            <Switch id="mobile-view" checked={isMobileView} onCheckedChange={toggleDeviceView} />
            <Computer className="h-4 w-4 text-muted-foreground" />
          </div>
          <Badge variant="outline" className="bg-primary/10 text-primary">
            <Shield className="h-3 w-3 mr-1" />
            Verified
          </Badge>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-6">
        {/* User Profile Card */}
        <Card className={`w-full ${!isMobileView ? 'md:w-80' : ''} h-fit animate-fade-in transition-all duration-300 hover:shadow-lg dark:border-gray-700 dark:bg-gray-800`}>
          <div className="h-2 bg-brand-yellow w-full"></div>
          <CardContent className="pt-6 flex flex-col items-center text-center">
            <UserAvatar 
              user={user} 
              className="h-24 w-24 mb-4 bg-brand-blue/10 ring-2 ring-offset-2 ring-brand-blue/20 dark:bg-brand-blue/30 dark:ring-brand-yellow/20"
            />
            <h3 className="font-semibold text-xl">{user.firstName} {user.lastName}</h3>
            <p className="text-sm text-muted-foreground mb-1">{user.email}</p>
            <p className="text-sm text-muted-foreground">
              {user.phone}
              {user.phone && (
                <span className="bg-brand-yellow/20 text-xs px-2 py-0.5 rounded ml-2 text-brand-blue dark:bg-brand-yellow/30">
                  #{user.phone.slice(-10)}
                </span>
              )}
            </p>
            
            <div className="mt-6 flex flex-col w-full gap-2">
              <Button variant="outline" size="sm" className="w-full hover:bg-brand-blue/5 dark:hover:bg-brand-yellow/10 dark:border-gray-600">
                Change Avatar
              </Button>
              <Button variant="outline" size="sm" className="w-full hover:bg-brand-blue/5 dark:hover:bg-brand-yellow/10 dark:border-gray-600">
                View Profile
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Settings Content */}
        <div className="flex-1">
          <Tabs defaultValue="general" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 mb-4 dark:bg-gray-800">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
              <TabsTrigger value="payment" className="hidden md:block">Payment</TabsTrigger>
              <TabsTrigger value="verification" className="hidden md:block">Verification</TabsTrigger>
              <TabsTrigger value="preferences">Settings</TabsTrigger>
            </TabsList>
            
            <TabsContent value="general" className="space-y-4">
              <Card className="transition-all duration-300 hover:shadow-lg dark:border-gray-700 dark:bg-gray-800">
                <div className="h-1 bg-brand-blue w-full dark:bg-brand-yellow"></div>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                  <CardDescription>
                    Update your details
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleUpdateProfile} className="space-y-4">
                    <div className="flex gap-2">
                      <div className="flex-1">
                        <Label htmlFor="firstName" className="flex items-center">
                          <UserRound className="h-4 w-4 mr-2 text-brand-blue dark:text-brand-yellow" />
                          First Name
                        </Label>
                        <Input
                          id="firstName"
                          value={user.firstName}
                          onChange={(e) => setUser({ ...user, firstName: e.target.value })}
                          className="border-brand-blue/20 focus-visible:ring-brand-blue/30 dark:border-gray-700 dark:bg-gray-900"
                        />
                      </div>
                      <div className="flex-1">
                        <Label htmlFor="lastName" className="flex items-center">
                          <UserRound className="h-4 w-4 mr-2 text-brand-blue dark:text-brand-yellow" />
                          Last Name
                        </Label>
                        <Input
                          id="lastName"
                          value={user.lastName}
                          onChange={(e) => setUser({ ...user, lastName: e.target.value })}
                          className="border-brand-blue/20 focus-visible:ring-brand-blue/30 dark:border-gray-700 dark:bg-gray-900"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="email" className="flex items-center">
                        <Mail className="h-4 w-4 mr-2 text-brand-blue dark:text-brand-yellow" />
                        Email Address
                      </Label>
                      <div className="flex items-center gap-2">
                        <Input 
                          id="email" 
                          type="email"
                          value={user.email}
                          onChange={(e) => setUser({...user, email: e.target.value})}
                          className="border-brand-blue/20 focus-visible:ring-brand-blue/30 dark:border-gray-700 dark:bg-gray-900"
                        />
                        <Badge variant="outline" className="shrink-0 bg-green-50 text-green-600 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800">Verified</Badge>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="flex items-center">
                        <Phone className="h-4 w-4 mr-2 text-brand-blue dark:text-brand-yellow" />
                        Phone Number
                      </Label>
                      <PhoneInput 
                        value={user.phone}
                        onChange={(value) => setUser({ ...user, phone: value })}
                      />
                    </div>
                    
                    <Button type="submit" className="bg-brand-blue hover:bg-brand-blue/90 text-white mt-2 dark:bg-brand-yellow dark:text-gray-900 dark:hover:bg-brand-yellow/90">
                      Save Changes
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="security" className="space-y-4">
              <ChangePasswordForm />
              <ChangePinForm />
            </TabsContent>
            
            <TabsContent value="payment">
              <Card className="transition-all duration-300 hover:shadow-lg dark:border-gray-700 dark:bg-gray-800">
                <div className="h-1 bg-brand-yellow w-full"></div>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CreditCard className="h-5 w-5 mr-2 text-brand-blue dark:text-brand-yellow" />
                    Payment Methods
                  </CardTitle>
                  <CardDescription>
                    Add or manage your payment cards
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* This content will be implemented in the PaymentMethodCard component */}
                  <div className="flex justify-end">
                    <Button 
                      className="bg-brand-blue hover:bg-brand-blue/90 text-white dark:bg-brand-yellow dark:text-gray-900 dark:hover:bg-brand-yellow/90"
                      onClick={() => window.location.href = '/add-card'}
                    >
                      <CreditCard className="mr-2 h-4 w-4" />
                      Add New Card
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="verification">
              <Card className="transition-all duration-300 hover:shadow-lg dark:border-gray-700 dark:bg-gray-800">
                <div className="h-1 bg-brand-blue w-full dark:bg-brand-yellow"></div>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Shield className="h-5 w-5 mr-2 text-brand-blue dark:text-brand-yellow" />
                    Identity Verification
                  </CardTitle>
                  <CardDescription>
                    Verify your identity to access all features
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col gap-4">
                    <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/5 transition-all duration-200 hover:bg-muted/10 dark:border-gray-700 dark:hover:bg-gray-800/50">
                      <div className="flex items-center gap-3">
                        <UserRound className="h-8 w-8 text-brand-blue p-1.5 bg-brand-blue/10 rounded-full dark:text-brand-yellow dark:bg-brand-blue/20" />
                        <div>
                          <h3 className="font-medium">Basic Information</h3>
                          <p className="text-sm text-muted-foreground">Personal details verification</p>
                        </div>
                      </div>
                      <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800">Completed</Badge>
                    </div>
                    
                    <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/5 transition-all duration-200 hover:bg-muted/10 dark:border-gray-700 dark:hover:bg-gray-800/50">
                      <div className="flex items-center gap-3">
                        <Mail className="h-8 w-8 text-brand-blue p-1.5 bg-brand-blue/10 rounded-full dark:text-brand-yellow dark:bg-brand-blue/20" />
                        <div>
                          <h3 className="font-medium">Email Verification</h3>
                          <p className="text-sm text-muted-foreground">Verify your email address</p>
                        </div>
                      </div>
                      <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800">Completed</Badge>
                    </div>
                    
                    <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/5 transition-all duration-200 hover:bg-muted/10 dark:border-gray-700 dark:hover:bg-gray-800/50">
                      <div className="flex items-center gap-3">
                        <Phone className="h-8 w-8 text-brand-blue p-1.5 bg-brand-blue/10 rounded-full dark:text-brand-yellow dark:bg-brand-blue/20" />
                        <div>
                          <h3 className="font-medium">Phone Verification</h3>
                          <p className="text-sm text-muted-foreground">Verify your phone number</p>
                        </div>
                      </div>
                      <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800">Completed</Badge>
                    </div>
                    
                    <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/5 transition-all duration-200 hover:bg-muted/10 dark:border-gray-700 dark:hover:bg-gray-800/50">
                      <div className="flex items-center gap-3">
                        <Shield className="h-8 w-8 text-brand-blue p-1.5 bg-brand-blue/10 rounded-full dark:text-brand-yellow dark:bg-brand-blue/20" />
                        <div>
                          <h3 className="font-medium">ID Verification</h3>
                          <p className="text-sm text-muted-foreground">Submit government-issued ID</p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" onClick={() => window.location.href = '/kyc'} 
                        className="hover:bg-brand-blue/5 hover:text-brand-blue dark:border-gray-700 dark:hover:bg-brand-yellow/10 dark:hover:text-brand-yellow">
                        Complete
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="preferences">
              <Card className="transition-all duration-300 hover:shadow-lg dark:border-gray-700 dark:bg-gray-800">
                <div className="h-1 bg-brand-yellow w-full"></div>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Palette className="h-5 w-5 mr-2 text-brand-blue dark:text-brand-yellow" />
                    System Preferences
                  </CardTitle>
                  <CardDescription>
                    Customize your account settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/5 transition-all duration-200 hover:bg-muted/10 dark:border-gray-700 dark:hover:bg-gray-800/50">
                    <div className="space-y-0.5">
                      <Label className="text-base flex items-center">
                        {theme === 'light' ? (
                          <Sun className="h-4 w-4 mr-2 text-brand-blue dark:text-brand-yellow" />
                        ) : (
                          <Moon className="h-4 w-4 mr-2 text-brand-blue dark:text-brand-yellow" />
                        )}
                        Dark Mode
                      </Label>
                      <p className="text-sm text-muted-foreground">Switch between light and dark themes</p>
                    </div>
                    <Switch checked={theme === 'dark'} onCheckedChange={toggleTheme} />
                  </div>
                  
                  <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/5 transition-all duration-200 hover:bg-muted/10 dark:border-gray-700 dark:hover:bg-gray-800/50">
                    <div className="space-y-0.5">
                      <Label className="text-base flex items-center">
                        <Languages className="h-4 w-4 mr-2 text-brand-blue dark:text-brand-yellow" />
                        Language
                      </Label>
                      <p className="text-sm text-muted-foreground">Choose your preferred language</p>
                    </div>
                    <select className="p-2 border rounded-md bg-background dark:bg-gray-900 dark:border-gray-700">
                      <option value="en">English</option>
                      <option value="yo">Yoruba</option>
                      <option value="ig">Igbo</option>
                      <option value="ha">Hausa</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/5 transition-all duration-200 hover:bg-muted/10 dark:border-gray-700 dark:hover:bg-gray-800/50">
                    <div className="space-y-0.5">
                      <Label className="text-base flex items-center">
                        <Coins className="h-4 w-4 mr-2 text-brand-blue dark:text-brand-yellow" />
                        Currency
                      </Label>
                      <p className="text-sm text-muted-foreground">Set your preferred currency</p>
                    </div>
                    <select className="p-2 border rounded-md bg-background dark:bg-gray-900 dark:border-gray-700">
                      <option value="NGN">Nigerian Naira (₦)</option>
                      <option value="USD">US Dollar ($)</option>
                    </select>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
