import { supabase } from './client';
import { toast } from 'sonner';
import { isPostgrestError } from '@/utils/supabase-types';
import { User } from '@supabase/supabase-js';

// Type for the user creation form
export interface AdminCreateUserData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  isAdmin?: boolean;
}

// Function for admins to create new users
export async function adminCreateUser(userData: AdminCreateUserData) {
  try {
    // First, check if the current user is an admin
    const { data: currentUserData } = await supabase.auth.getUser();
    const { data: isAdmin, error: adminCheckError } = await supabase
      .rpc('is_admin', { user_id: currentUserData.user?.id });
      
    if (adminCheckError) throw adminCheckError;
    if (!isAdmin) throw new Error('Only admins can create users');
    
    // Create the user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      email_confirm: true, // Auto-confirm email to avoid verification steps
      user_metadata: {
        first_name: userData.firstName,
        last_name: userData.lastName,
        phone: userData.phone || '',
      },
    });
    
    if (authError) throw authError;
    
    const newUserId = authData.user.id;
    
    // Check if phone is empty and handle it appropriately
    const phoneValue = userData.phone && userData.phone.trim() !== "" 
      ? userData.phone 
      : null;
    
    // Update the profile to add phone number manually (if provided)
    // This allows us to handle unique constraint violations more gracefully
    if (phoneValue) {
      try {
        const { error: phoneUpdateError } = await supabase
          .from('profiles')
          .update({ phone: phoneValue })
          .eq('id', newUserId);
          
        if (phoneUpdateError && phoneUpdateError.code === '23505') {
          // Phone number is a duplicate, update to null instead
          await supabase
            .from('profiles')
            .update({ phone: null })
            .eq('id', newUserId);
            
          console.warn("Duplicate phone number detected, setting to null");
        } else if (phoneUpdateError) {
          console.error("Error updating phone:", phoneUpdateError);
        }
      } catch (phoneError) {
        console.error("Phone update error:", phoneError);
        // Continue anyway - the user is created
      }
    }
    
    // If user creation was successful but we need to make them an admin
    if (userData.isAdmin) {
      const { error: roleError } = await supabase
        .from('user_roles')
        .upsert({
          user_id: newUserId,
          role: 'admin'
        });
        
      if (roleError) throw roleError;
    }
    
    // Get the created profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', newUserId)
      .single();
      
    if (profileError && profileError.code !== 'PGRST116') throw profileError;
    
    // Update the profile to add admin as creator
    await supabase
      .from('profiles')
      .update({
        created_by_admin_id: currentUserData.user?.id
      })
      .eq('id', newUserId);
    
    return { 
      data: { 
        user: authData.user,
        profile 
      }, 
      error: null 
    };
  } catch (error) {
    console.error('Error creating user by admin:', error);
    
    // Check for specific error messages and provide more user-friendly responses
    let errorMessage = error instanceof Error ? error.message : 'An error occurred';
    
    if (errorMessage.includes('duplicate key') && errorMessage.includes('profiles_phone_key')) {
      errorMessage = 'Phone number is already in use. Please use a different phone number.';
    } else if (errorMessage.includes('duplicate key') && errorMessage.includes('profiles_email_key')) {
      errorMessage = 'Email address is already in use. Please use a different email.';
    }
    
    return { 
      data: null, 
      error: isPostgrestError(error) ? error : { 
        message: errorMessage,
        code: 'ADMIN_CREATE_USER_ERROR' 
      } 
    };
  }
}

// Get all users with their profiles and roles
export async function getAllUsers() {
  try {
    // First, check if the current user is an admin
    const { data: currentUserData } = await supabase.auth.getUser();
    const { data: isAdmin, error: adminCheckError } = await supabase
      .rpc('is_admin', { user_id: currentUserData.user?.id });
      
    if (adminCheckError) throw adminCheckError;
    if (!isAdmin) throw new Error('Only admins can list all users');
    
    // Get all profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false });
      
    if (profilesError) throw profilesError;
    
    // Get roles for all users
    const { data: roles, error: rolesError } = await supabase
      .from('user_roles')
      .select('*');
      
    if (rolesError) throw rolesError;
    
    // Combine the data
    const users = profiles.map(profile => {
      const userRoles = roles.filter(role => role.user_id === profile.id);
      const isAdmin = userRoles.some(role => role.role === 'admin');
      
      return {
        ...profile,
        isAdmin,
        roles: userRoles
      };
    });
    
    return { data: users, error: null };
  } catch (error) {
    console.error('Error getting all users:', error);
    return { 
      data: null, 
      error: isPostgrestError(error) ? error : { 
        message: error instanceof Error ? error.message : 'An error occurred',
        code: 'GET_ALL_USERS_ERROR' 
      } 
    };
  }
}

// Function to update a user's profile by an admin
export async function updateUserByAdmin(userId: string, userData: {
  firstName?: string;
  lastName?: string;
  phone?: string;
  status?: 'active' | 'suspended' | 'blocked';
  isAdmin?: boolean;
}) {
  try {
    // Check if current user is admin
    const { data: currentUserData } = await supabase.auth.getUser();
    const { data: isAdmin, error: adminCheckError } = await supabase
      .rpc('is_admin', { user_id: currentUserData.user?.id });
      
    if (adminCheckError) throw adminCheckError;
    if (!isAdmin) throw new Error('Only admins can update user profiles');

    // Prepare profile update data
    const updateData: Record<string, any> = {};
    if (userData.firstName !== undefined) updateData.first_name = userData.firstName;
    if (userData.lastName !== undefined) updateData.last_name = userData.lastName;
    if (userData.phone !== undefined) updateData.phone = userData.phone;
    if (userData.status !== undefined) updateData.status = userData.status;

    // Update profile if there's data to update
    if (Object.keys(updateData).length > 0) {
      const { error: profileError } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId);
        
      if (profileError) throw profileError;
    }

    // Handle admin role update
    if (userData.isAdmin !== undefined) {
      if (userData.isAdmin) {
        // Add admin role
        const { error: roleError } = await supabase
          .from('user_roles')
          .upsert({
            user_id: userId,
            role: 'admin'
          }, { onConflict: 'user_id,role' });
        
        if (roleError) throw roleError;
      } else {
        // Remove admin role
        const { error: roleError } = await supabase
          .from('user_roles')
          .delete()
          .eq('user_id', userId)
          .eq('role', 'admin');
          
        if (roleError) throw roleError;
      }
    }

    // Get updated user data
    const { data: profile, error: getProfileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();
      
    if (getProfileError) throw getProfileError;
    
    // Get updated role data
    const { data: roles, error: getRolesError } = await supabase
      .from('user_roles')
      .select('*')
      .eq('user_id', userId);
      
    if (getRolesError) throw getRolesError;
    
    // Check if profile exists
    if (!profile) {
      throw new Error('Profile not found');
    }
    
    return { 
      data: { 
        ...profile, 
        isAdmin: roles.some(r => r.role === 'admin'),
        roles 
      }, 
      error: null 
    };
  } catch (error) {
    console.error('Error updating user by admin:', error);
    return { 
      data: null, 
      error: isPostgrestError(error) ? error : { 
        message: error instanceof Error ? error.message : 'An error occurred',
        code: 'UPDATE_USER_BY_ADMIN_ERROR' 
      } 
    };
  }
}

// Interface for Supabase admin users to fix the typing issue
interface AdminUser {
  id: string;
  email?: string;
  phone?: string;
  created_at?: string;
  last_sign_in_at?: string;
  user_metadata?: Record<string, any>;
}

// Now let's update the function to create the initial admin user
export async function createInitialAdminUser() {
  try {
    const response = await supabase.functions.invoke('auth-user', {
      body: {
        action: 'create-admin',
        email: '<EMAIL>',
        password: 'Germany123456789@',
        first_name: 'Admin',
        last_name: 'User',
        phone: '+2349063978612'
      }
    });
    
    if (response.error) throw response.error;
    
    return { data: response.data, error: null };
  } catch (error) {
    console.error('Error creating initial admin user:', error);
    return { 
      data: null, 
      error: isPostgrestError(error) ? error : { 
        message: error instanceof Error ? error.message : 'An error occurred',
        code: 'CREATE_INITIAL_ADMIN_ERROR' 
      } 
    };
  }
}
