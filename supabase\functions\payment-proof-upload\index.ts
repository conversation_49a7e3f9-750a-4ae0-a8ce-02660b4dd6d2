
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'
import { v4 as uuidv4 } from 'https://esm.sh/uuid@9.0.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL') as string
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') as string
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Get form data from request
    const formData = await req.formData()
    const file = formData.get('file') as File
    const userId = formData.get('userId') as string
    const amount = parseFloat(formData.get('amount') as string)
    const paymentMethod = formData.get('paymentMethod') as string
    const transactionId = formData.get('transactionId') as string
    const description = formData.get('description') as string

    console.log(`Processing payment proof upload. User ID: ${userId}, Amount: ${amount}`)

    if (!file || !userId || isNaN(amount)) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Upload file to Supabase Storage
    const fileExt = file.name.split('.').pop()
    const fileName = `${userId}/${Date.now()}_payment_proof.${fileExt}`
    
    const { data: uploadData, error: uploadError } = await supabase
      .storage
      .from('payment_proofs')
      .upload(fileName, file, {
        upsert: false,
        contentType: file.type
      })

    if (uploadError) {
      console.error('Error uploading payment proof:', uploadError)
      throw uploadError
    }

    // Get public URL for the uploaded file
    const { data: urlData } = supabase
      .storage
      .from('payment_proofs')
      .getPublicUrl(fileName)

    const documentUrl = urlData.publicUrl

    // Generate reference for the transaction if not provided
    const reference = transactionId || `PMT-${uuidv4().substring(0, 8)}-${Date.now()}`

    // Create transaction record if transactionId is not provided
    let transaction = null
    if (!transactionId) {
      const { data: transactionData, error: transactionError } = await supabase
        .from('transactions')
        .insert({
          user_id: userId,
          amount,
          type: 'deposit',
          payment_method: paymentMethod,
          reference,
          description: description || 'Payment with proof',
          status: 'pending_verification',
          metadata: {
            payment_proof_url: documentUrl
          }
        })
        .select()
        .single()

      if (transactionError) {
        console.error('Error creating transaction record:', transactionError)
        throw transactionError
      }
      
      transaction = transactionData
    } else {
      // Update existing transaction with proof
      const { data: transactionData, error: transactionError } = await supabase
        .from('transactions')
        .update({
          status: 'pending_verification',
          metadata: {
            payment_proof_url: documentUrl
          }
        })
        .eq('id', transactionId)
        .select()
        .single()

      if (transactionError) {
        console.error('Error updating transaction record:', transactionError)
        throw transactionError
      }
      
      transaction = transactionData
    }

    // Create notification for user
    await supabase
      .from('notifications')
      .insert({
        user_id: userId,
        title: 'Payment Proof Received',
        message: `Your payment proof for ₦${amount.toLocaleString()} has been received and is pending verification.`,
        type: 'info',
        channel: 'in-app',
        priority: 'medium'
      })

    // Create notification for admins
    const { data: adminUsers, error: adminError } = await supabase
      .rpc('get_admin_users')

    if (!adminError && adminUsers) {
      for (const admin of adminUsers) {
        await supabase
          .from('notifications')
          .insert({
            user_id: admin.id,
            title: 'New Payment Proof For Review',
            message: `A payment proof of ₦${amount.toLocaleString()} has been uploaded by a user and requires verification.`,
            type: 'info',
            channel: 'in-app',
            priority: 'high',
            action_url: '/admin/payment-verification'
          })
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        data: {
          transaction,
          payment_proof_url: documentUrl
        }
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Error processing payment proof:', error.message)
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
