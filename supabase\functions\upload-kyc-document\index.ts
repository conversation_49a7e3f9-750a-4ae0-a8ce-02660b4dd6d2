
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL') as string
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') as string
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Get form data from request
    const formData = await req.formData()
    const file = formData.get('file') as File
    const documentType = formData.get('documentType') as string
    const userId = formData.get('userId') as string

    console.log(`Processing KYC document upload. User ID: ${userId}, Type: ${documentType}`)

    if (!file || !documentType || !userId) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Upload file to Supabase Storage
    const fileExt = file.name.split('.').pop()
    const fileName = `${userId}/${Date.now()}_${documentType}.${fileExt}`
    
    const { data: uploadData, error: uploadError } = await supabase
      .storage
      .from('kyc_documents')
      .upload(fileName, file, {
        upsert: false,
        contentType: file.type
      })

    if (uploadError) {
      console.error('Error uploading document:', uploadError)
      throw uploadError
    }

    // Get public URL for the uploaded file
    const { data: urlData } = supabase
      .storage
      .from('kyc_documents')
      .getPublicUrl(fileName)

    const documentUrl = urlData.publicUrl

    // Save document record in database
    const { data: documentData, error: documentError } = await supabase
      .from('kyc_documents')
      .insert({
        user_id: userId,
        document_type: documentType,
        document_url: documentUrl,
        verification_status: 'pending',
        metadata: {
          original_filename: file.name,
          file_size: file.size,
          content_type: file.type
        }
      })
      .select()
      .single()

    if (documentError) {
      console.error('Error creating document record:', documentError)
      throw documentError
    }

    // Create notification for user
    await supabase
      .from('notifications')
      .insert({
        user_id: userId,
        title: 'KYC Document Uploaded',
        message: `Your ${documentType} document has been uploaded and is pending verification.`,
        type: 'info',
        channel: 'in-app',
        priority: 'medium'
      })

    // Create notification for admins (alert them of new documents to verify)
    const { data: adminUsers, error: adminError } = await supabase
      .rpc('get_admin_users')

    if (!adminError && adminUsers) {
      for (const admin of adminUsers) {
        await supabase
          .from('notifications')
          .insert({
            user_id: admin.id,
            title: 'New KYC Document For Review',
            message: `A new ${documentType} document has been uploaded by a user and requires verification.`,
            type: 'info',
            channel: 'in-app',
            priority: 'high',
            action_url: '/admin/verification'
          })
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        data: documentData 
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Error processing KYC document:', error.message)
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
