const express = require('express');
const axios = require('axios');
const router = express.Router();

const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY;
const PAYSTACK_BASE_URL = process.env.PAYSTACK_BASE_URL || 'https://api.paystack.co';

// Get list of banks
router.get('/banks', async (req, res) => {
  try {
    const response = await axios.get(`${PAYSTACK_BASE_URL}/bank`, {
      headers: {
        Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
      },
    });
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ status: false, message: 'Failed to fetch banks', error: error.message });
  }
});

// Resolve account number
router.post('/resolve-account', async (req, res) => {
  const { bank_code, account_number } = req.body;
  if (!bank_code || !account_number) {
    return res.status(400).json({ status: false, message: 'bank_code and account_number are required' });
  }
  try {
    const response = await axios.get(`${PAYSTACK_BASE_URL}/bank/resolve`, {
      params: { bank_code, account_number },
      headers: {
        Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
      },
    });
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ status: false, message: 'Failed to resolve account', error: error.message });
  }
});

// Initiate transfer (withdrawal)
router.post('/transfer', async (req, res) => {
  const { amount, bank_code, account_number, account_name, user_id } = req.body;
  if (!amount || !bank_code || !account_number || !account_name) {
    return res.status(400).json({ status: false, message: 'Missing required fields' });
  }
  try {
    // Step 1: Create transfer recipient
    const recipientRes = await axios.post(`${PAYSTACK_BASE_URL}/transferrecipient`, {
      type: 'nuban',
      name: account_name,
      account_number,
      bank_code,
      currency: 'NGN',
    }, {
      headers: {
        Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
      },
    });
    const recipient = recipientRes.data.data;
    // Step 2: Initiate transfer
    const transferRes = await axios.post(`${PAYSTACK_BASE_URL}/transfer`, {
      source: 'balance',
      amount: Math.round(Number(amount)),
      recipient: recipient.recipient_code,
      reason: `Withdrawal${user_id ? ' for user ' + user_id : ''}`,
    }, {
      headers: {
        Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
      },
    });
    res.json(transferRes.data);
  } catch (error) {
    res.status(500).json({ status: false, message: 'Failed to initiate transfer', error: error.message });
  }
});

module.exports = router;
