
// Mock API service without Firebase dependency
export const api = {
  baseURL: 'https://api.kojapay.io',
  
  // Mock request function
  request: async (endpoint: string, options: any = {}) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock response
    return {
      data: { success: true, mock: true },
      status: 200
    };
  }
};

export default api;
