
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Users, 
  Calendar, 
  DollarSign, 
  ShoppingCart, 
  Car, 
  Smartphone, 
  ShoppingBag,
  UserPlus,
  Eye,
  Share2
} from 'lucide-react';
import { GroupSavingsPlan } from '@/types/group-savings';

interface GroupSavingsCardProps {
  plan: GroupSavingsPlan;
  onJoin?: (planId: string) => void;
  onViewDetails?: (plan: GroupSavingsPlan) => void;
  onShare?: (plan: GroupSavingsPlan) => void;
  canJoin?: boolean;
}

export default function GroupSavingsCard({ 
  plan, 
  onJoin, 
  onViewDetails, 
  onShare,
  canJoin = true 
}: GroupSavingsCardProps) {
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'electronics':
        return <ShoppingCart className="h-5 w-5 text-blue-500" />;
      case 'car':
        return <Car className="h-5 w-5 text-green-500" />;
      case 'phone':
        return <Smartphone className="h-5 w-5 text-purple-500" />;
      case 'grocery':
        return <ShoppingBag className="h-5 w-5 text-yellow-500" />;
      default:
        return <ShoppingCart className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500 text-white">Active</Badge>;
      case 'completed':
        return <Badge className="bg-blue-500 text-white">Completed</Badge>;
      case 'recruiting':
        return <Badge className="bg-yellow-500 text-white">Recruiting</Badge>;
      case 'pending':
        return <Badge className="bg-gray-500 text-white">Pending</Badge>;
      default:
        return <Badge className="bg-gray-500 text-white">{status}</Badge>;
    }
  };

  const progress = (plan.current_members / plan.max_members) * 100;
  const canJoinGroup = canJoin && plan.status === 'recruiting' && plan.current_members < plan.max_members;

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            {getCategoryIcon(plan.category)}
            <CardTitle className="text-lg">{plan.name}</CardTitle>
          </div>
          {getStatusBadge(plan.status)}
        </div>
        <CardDescription className="text-sm">{plan.description}</CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="font-medium">₦{plan.target_amount.toLocaleString()}</p>
              <p className="text-xs text-muted-foreground">Target</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="font-medium">₦{plan.contribution_amount.toLocaleString()}</p>
              <p className="text-xs text-muted-foreground">Contribution</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="font-medium">{plan.current_members}/{plan.max_members}</p>
              <p className="text-xs text-muted-foreground">Members</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="font-medium">{new Date(plan.end_date).toLocaleDateString()}</p>
              <p className="text-xs text-muted-foreground">End Date</p>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Member Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {plan.status === 'recruiting' && (
          <div className="bg-blue-50 p-3 rounded-lg">
            <p className="text-sm text-blue-800">
              🚀 Looking for {plan.max_members - plan.current_members} more members to start!
            </p>
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-2 flex justify-between gap-2">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => onViewDetails?.(plan)}
          className="flex-1"
        >
          <Eye className="h-4 w-4 mr-1" />
          View Details
        </Button>
        
        {canJoinGroup ? (
          <Button 
            size="sm" 
            onClick={() => onJoin?.(plan.id)}
            className="flex-1"
          >
            <UserPlus className="h-4 w-4 mr-1" />
            Join Group
          </Button>
        ) : (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => onShare?.(plan)}
            className="flex-1"
          >
            <Share2 className="h-4 w-4 mr-1" />
            Share
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
