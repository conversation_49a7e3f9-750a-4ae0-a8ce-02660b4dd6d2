
import { supabase } from './client';

interface SignUpData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
}

interface SignInData {
  email: string;
  password: string;
}

interface JwtSignInData {
  token: string;
}

interface PasswordResetData {
  email: string;
}

interface UpdatePasswordData {
  access_token: string;
  new_password: string;
}

/**
 * Sign up a new user through the edge function
 */
export async function signUpViaFunction(data: SignUpData) {
  try {
    const { data: responseData, error } = await supabase.functions.invoke('auth-user/signup', {
      body: data,
    });
    
    if (error) throw error;
    
    return { data: responseData, error: null };
  } catch (error) {
    console.error('Error signing up via function:', error);
    return { data: null, error };
  }
}

/**
 * Sign in a user through the edge function
 */
export async function signInViaFunction(data: SignInData) {
  try {
    const { data: responseData, error } = await supabase.functions.invoke('auth-user/signin', {
      body: data,
    });
    
    if (error) throw error;
    
    return { data: responseData, error: null };
  } catch (error) {
    console.error('Error signing in via function:', error);
    return { data: null, error };
  }
}

/**
 * Sign in with JWT through the edge function
 */
export async function signInWithJWTViaFunction(token: string) {
  try {
    const { data: responseData, error } = await supabase.functions.invoke('auth-user/jwt-signin', {
      body: { token },
    });
    
    if (error) throw error;
    
    return { data: responseData, error: null };
  } catch (error) {
    console.error('Error signing in with JWT via function:', error);
    return { data: null, error };
  }
}

/**
 * Request password reset through the edge function
 */
export async function resetPasswordViaFunction(email: string) {
  try {
    const { data: responseData, error } = await supabase.functions.invoke('auth-user/reset-password', {
      body: { email },
    });
    
    if (error) throw error;
    
    return { data: responseData, error: null };
  } catch (error) {
    console.error('Error requesting password reset via function:', error);
    return { data: null, error };
  }
}

/**
 * Update user password through the edge function
 */
export async function updatePasswordViaFunction(access_token: string, new_password: string) {
  try {
    const { data: responseData, error } = await supabase.functions.invoke('auth-user/update-password', {
      body: { access_token, new_password },
    });
    
    if (error) throw error;
    
    return { data: responseData, error: null };
  } catch (error) {
    console.error('Error updating password via function:', error);
    return { data: null, error };
  }
}
