
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuthApi } from '@/hooks/use-auth-api';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

export default function AdminLogin() {
  const navigate = useNavigate();
  const [credentials, setCredentials] = useState({
    email: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCredentials(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const res = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials)
      });
      const data = await res.json();
      console.log('[AdminLogin] Login response:', data);
      if (!res.ok) {
        toast.error(data?.error || 'Login failed');
        setIsLoading(false);
        return;
      }
      // Log user object and role for debugging
      if (data?.user) {
        console.log('[AdminLogin] User object:', data.user);
        console.log('[AdminLogin] User role:', data.user.role, 'isAdmin:', data.user.isAdmin);
      } else {
        console.warn('[AdminLogin] No user object in response');
      }
      // Check for admin role (case-insensitive)
      const isAdmin = data?.user && (
        (typeof data.user.role === 'string' && data.user.role.toLowerCase() === 'admin') ||
        data.user.isAdmin === true
      );
      if (isAdmin) {
        toast.success('Admin login successful');
        localStorage.setItem('auth_user', JSON.stringify(data.user));
        if (data.token) localStorage.setItem('auth_token', data.token);
        navigate('/admin/dashboard');
      } else {
        toast.error('Access denied. Admin privileges required.');
        console.warn('[AdminLogin] Access denied. User role:', data?.user?.role, 'isAdmin:', data?.user?.isAdmin);
      }
    } catch (err) {
      toast.error('Network error. Please try again.');
      console.error('[AdminLogin] Network or unexpected error:', err);
    }
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">Admin Login</CardTitle>
          <CardDescription className="text-center">
            Access the admin dashboard
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={credentials.email}
                onChange={handleInputChange}
                placeholder="<EMAIL>"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                name="password"
                type="password"
                value={credentials.password}
                onChange={handleInputChange}
                placeholder="Enter your password"
                required
              />
            </div>
            
            <Button type="submit" disabled={isLoading} className="w-full">
              {isLoading ? 'Signing in...' : 'Sign In'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
