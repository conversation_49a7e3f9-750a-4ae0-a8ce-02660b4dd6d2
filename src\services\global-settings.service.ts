// Global Settings Service for ASUSU by Koja
// API service for global settings management

import {
  GlobalSettings,
  PublicGlobalSettings,
  GlobalSettingsResponse,
  PublicSettingsResponse,
  AppConfigUpdate,
  ColorThemeUpdate,
  BrandingUpdate,
  APIKeysUpdate,
  ContentUpdate,
  SystemConfigUpdate,
  ContactUpdate,
  SettingsHistoryResponse
} from '@/types/global-settings';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

class GlobalSettingsService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // Public settings (no authentication required)
  async getPublicSettings(): Promise<PublicGlobalSettings> {
    const response = await fetch(`${API_BASE_URL}/settings`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const result = await this.handleResponse<PublicSettingsResponse>(response);
    return result.data;
  }

  // Admin settings (authentication required)
  async getAdminSettings(): Promise<GlobalSettings> {
    const response = await fetch(`${API_BASE_URL}/settings/admin`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    const result = await this.handleResponse<GlobalSettingsResponse>(response);
    return result.data;
  }

  // Update app configuration
  async updateAppConfig(data: AppConfigUpdate): Promise<GlobalSettings> {
    const response = await fetch(`${API_BASE_URL}/settings/app-config`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    const result = await this.handleResponse<GlobalSettingsResponse>(response);
    return result.data;
  }

  // Update color theme
  async updateColors(data: ColorThemeUpdate): Promise<GlobalSettings> {
    const response = await fetch(`${API_BASE_URL}/settings/colors`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    const result = await this.handleResponse<GlobalSettingsResponse>(response);
    return result.data;
  }

  // Update branding (logo, favicon)
  async updateBranding(data: BrandingUpdate): Promise<GlobalSettings> {
    const response = await fetch(`${API_BASE_URL}/settings/branding`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    const result = await this.handleResponse<GlobalSettingsResponse>(response);
    return result.data;
  }

  // Update API keys
  async updateAPIKeys(data: APIKeysUpdate): Promise<GlobalSettings> {
    const response = await fetch(`${API_BASE_URL}/settings/api-keys`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    const result = await this.handleResponse<GlobalSettingsResponse>(response);
    return result.data;
  }

  // Update content management
  async updateContent(data: ContentUpdate): Promise<GlobalSettings> {
    const response = await fetch(`${API_BASE_URL}/settings/content`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    const result = await this.handleResponse<GlobalSettingsResponse>(response);
    return result.data;
  }

  // Update system configuration
  async updateSystem(data: SystemConfigUpdate): Promise<GlobalSettings> {
    const response = await fetch(`${API_BASE_URL}/settings/system`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    const result = await this.handleResponse<GlobalSettingsResponse>(response);
    return result.data;
  }

  // Update contact information
  async updateContact(data: ContactUpdate): Promise<GlobalSettings> {
    const response = await fetch(`${API_BASE_URL}/settings/contact`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    const result = await this.handleResponse<GlobalSettingsResponse>(response);
    return result.data;
  }

  // Get settings history
  async getSettingsHistory(): Promise<SettingsHistoryResponse['data']> {
    const response = await fetch(`${API_BASE_URL}/settings/history`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    const result = await this.handleResponse<SettingsHistoryResponse>(response);
    return result.data;
  }

  // Upload file helper (for logos, images, etc.)
  async uploadFile(file: File, type: 'logo' | 'favicon' | 'image'): Promise<{ url: string; filename: string }> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    const token = localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/upload`, {
      method: 'POST',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` })
      },
      body: formData
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || 'Upload failed');
    }

    const result = await response.json();
    return result.data;
  }

  // Validate color hex format
  validateHexColor(color: string): boolean {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
  }

  // Apply theme colors to CSS variables
  applyThemeColors(colors: ColorThemeUpdate): void {
    const root = document.documentElement;
    
    if (colors.primary) {
      root.style.setProperty('--color-primary', colors.primary);
      root.style.setProperty('--color-brand-blue', colors.primary);
    }
    
    if (colors.secondary) {
      root.style.setProperty('--color-secondary', colors.secondary);
      root.style.setProperty('--color-brand-green', colors.secondary);
    }
    
    if (colors.accent) {
      root.style.setProperty('--color-accent', colors.accent);
      root.style.setProperty('--color-brand-yellow', colors.accent);
    }
    
    if (colors.background) {
      root.style.setProperty('--color-background', colors.background);
    }
  }

  // Reset theme colors to default
  resetThemeColors(): void {
    const root = document.documentElement;
    root.style.removeProperty('--color-primary');
    root.style.removeProperty('--color-secondary');
    root.style.removeProperty('--color-accent');
    root.style.removeProperty('--color-background');
    root.style.removeProperty('--color-brand-blue');
    root.style.removeProperty('--color-brand-green');
    root.style.removeProperty('--color-brand-yellow');
  }
}

// Export singleton instance
export const globalSettingsService = new GlobalSettingsService();
export default globalSettingsService;
