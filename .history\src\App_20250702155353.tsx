
import JoinRotationalGroupPage from './pages/user/JoinRotationalGroup';

import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from "@/components/ui/sonner"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { Toaster as HotToaster } from "@/components/ui/toaster"
import ErrorBoundary from './components/ErrorBoundary';
import { AuthProvider } from './hooks/use-auth';
import AOS from 'aos';
import 'aos/dist/aos.css';

// Import pages
import Index from './pages/Index';
import Login from './pages/Login';
import Signup from './pages/Signup';
import ReferralSignup from './pages/ReferralSignup';
import AdminLogin from './pages/AdminLogin';
import NotFound from './pages/NotFound';
import HelpCenter from './pages/HelpCenter';
import Savings from './pages/Savings';

// User pages
import UserDashboard from './pages/user/Dashboard';
import UserSavingsPlans from './pages/user/SavingsPlans';
import RotationalSavings from './pages/user/RotationalSavings';
import UserTransactions from './pages/user/Transactions';
import UserPayments from './pages/user/Payments';
import UserAnalytics from './pages/user/Analytics';
import UserSettings from './pages/user/Settings';
import UserProfile from './pages/user/UserProfile';
import KycVerification from './pages/user/KycVerification';
import AddCard from './pages/user/AddCard';

// Admin pages
import AdminDashboard from './pages/admin/Dashboard';
import UserManagement from './pages/admin/UserManagement';
import SavingsPlansManagement from './pages/admin/SavingsPlansManagement';
import GroupSavingsPlans from './pages/admin/GroupSavingsPlans';
import PaymentManagement from './pages/admin/PaymentManagement';
import AdminAnalytics from './pages/admin/Analytics';
import StaffManagement from './pages/admin/StaffManagement';
import NotificationManagement from './pages/admin/NotificationManagement';
import OtpVerificationManagement from './pages/admin/OtpVerificationManagement';
import AdminRolesManagement from './pages/admin/AdminRolesManagement';
import StaffRoleManagement from './pages/admin/StaffRoleManagement';
import UserProfileDetails from './pages/admin/UserProfileDetails';
import AssignPlanToUser from './pages/admin/AssignPlanToUser';
import Requests from './pages/admin/Requests';

import { PrivateRoute } from './components/auth/PrivateRoute';
import { UserLayout } from './components/layout/user-layout';

import './App.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      staleTime: 5 * 60 * 1000,
    },
  },
});

function App() {
  // ...existing code...
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <Router>
          <AuthProvider>
            <div className="min-h-screen bg-background">
              <Routes>
                {/* Public routes */}
                <Route path="/" element={<Index />} />
                <Route path="/login" element={<Login />} />
                <Route path="/signup" element={<Signup />} />
                <Route path="/referral" element={<ReferralSignup />} />
                <Route path="/admin/login" element={<AdminLogin />} />
                <Route path="/help" element={<HelpCenter />} />
                <Route path="/savings" element={<Savings />} />
                <Route path="/join/group/:groupId" element={<JoinRotationalGroupPage />} />
  useEffect(() => {
    AOS.init({
      duration: 800,
      once: true,
      offset: 100,
      easing: 'ease-out-cubic'
    });
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <Router>
          <AuthProvider>
            <div className="min-h-screen bg-background">
              <Routes>
                {/* Public routes */}
                <Route path="/" element={<Index />} />
                <Route path="/login" element={<Login />} />
                <Route path="/signup" element={<Signup />} />
                <Route path="/referral" element={<ReferralSignup />} />
                <Route path="/admin/login" element={<AdminLogin />} />
                <Route path="/help" element={<HelpCenter />} />
                <Route path="/savings" element={<Savings />} />

                {/* User routes */}
                <Route path="/user/*" element={
                  <PrivateRoute>
                    <UserLayout>
                      <Routes>
                        <Route path="dashboard" element={<UserDashboard />} />
                        <Route path="savings-plans" element={<UserSavingsPlans />} />
                        <Route path="rotational-savings" element={<RotationalSavings />} />
                        <Route path="transactions" element={<UserTransactions />} />
                        <Route path="payments" element={<UserPayments />} />
                        <Route path="analytics" element={<UserAnalytics />} />
                        <Route path="settings" element={<UserSettings />} />
                        <Route path="profile" element={<UserProfile />} />
                        <Route path="kyc-verification" element={<KycVerification />} />
                        <Route path="add-card" element={<AddCard />} />
                      </Routes>
                    </UserLayout>
                  </PrivateRoute>
                } />

                {/* Dashboard route redirect */}
                <Route path="/dashboard" element={
                  <PrivateRoute>
                    <UserLayout>
                      <UserDashboard />
                    </UserLayout>
                  </PrivateRoute>
                } />

                {/* Legacy route redirects */}
                <Route path="/transactions" element={
                  <PrivateRoute>
                    <UserLayout>
                      <UserTransactions />
                    </UserLayout>
                  </PrivateRoute>
                } />
                <Route path="/payments" element={
                  <PrivateRoute>
                    <UserLayout>
                      <UserPayments />
                    </UserLayout>
                  </PrivateRoute>
                } />
                <Route path="/analytics" element={
                  <PrivateRoute>
                    <UserLayout>
                      <UserAnalytics />
                    </UserLayout>
                  </PrivateRoute>
                } />
                <Route path="/profile" element={
                  <PrivateRoute>
                    <UserLayout>
                      <UserProfile />
                    </UserLayout>
                  </PrivateRoute>
                } />
                <Route path="/settings" element={
                  <PrivateRoute>
                    <UserLayout>
                      <UserSettings />
                    </UserLayout>
                  </PrivateRoute>
                } />

                {/* Admin routes */}
                <Route path="/admin/dashboard" element={
                  <PrivateRoute>
                    <AdminDashboard />
                  </PrivateRoute>
                } />
                <Route path="/admin/users" element={
                  <PrivateRoute>
                    <UserManagement />
                  </PrivateRoute>
                } />
                <Route path="/admin/savings-plans" element={
                  <PrivateRoute>
                    <SavingsPlansManagement />
                  </PrivateRoute>
                } />
                <Route path="/admin/group-savings" element={
                  <PrivateRoute>
                    <GroupSavingsPlans />
                  </PrivateRoute>
                } />
                <Route path="/admin/payments" element={
                  <PrivateRoute>
                    <PaymentManagement />
                  </PrivateRoute>
                } />
                <Route path="/admin/analytics" element={
                  <PrivateRoute>
                    <AdminAnalytics />
                  </PrivateRoute>
                } />
                <Route path="/admin/staff" element={
                  <PrivateRoute>
                    <StaffManagement />
                  </PrivateRoute>
                } />
                <Route path="/admin/notifications" element={
                  <PrivateRoute>
                    <NotificationManagement />
                  </PrivateRoute>
                } />
                <Route path="/admin/otp-verification" element={
                  <PrivateRoute>
                    <OtpVerificationManagement />
                  </PrivateRoute>
                } />
                <Route path="/admin/roles" element={
                  <PrivateRoute>
                    <AdminRolesManagement />
                  </PrivateRoute>
                } />
                <Route path="/admin/staff-roles" element={
                  <PrivateRoute>
                    <StaffRoleManagement />
                  </PrivateRoute>
                } />
                <Route path="/admin/user/:userId" element={
                  <PrivateRoute>
                    <UserProfileDetails />
                  </PrivateRoute>
                } />
                <Route path="/admin/assign-plan" element={
                  <PrivateRoute>
                    <AssignPlanToUser />
                  </PrivateRoute>
                } />
                <Route path="/admin/requests" element={
                  <PrivateRoute>
                    <Requests />
                  </PrivateRoute>
                } />

                {/* Catch all route */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </div>
            <Toaster />
            <HotToaster />
          </AuthProvider>
        </Router>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
