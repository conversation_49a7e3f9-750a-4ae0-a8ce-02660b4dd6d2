
// Add at the top of the file
import { supabase } from '@/integrations/supabase/client';
import { isPostgrestError, hasData, jsonToRecord } from '@/utils/supabase-types';
import { Json } from '@/integrations/supabase/types';

// Fix the metadata issue in the function (line ~240)
export const createTransaction = async (transactionData: {
  user_id: string;
  amount: number;
  savings_plan_id: string;
  status: "pending" | "completed" | "failed";
  type: "deposit" | "withdrawal" | "transfer";
  description?: string;
  payment_method?: string;
  metadata?: Record<string, any> | null;
}) => {
  // Ensure transactionData has a type property
  const { data, error } = await supabase
    .from('transactions')
    .insert({
      user_id: transactionData.user_id,
      amount: transactionData.amount,
      savings_plan_id: transactionData.savings_plan_id,
      status: transactionData.status,
      type: transactionData.type,
      description: transactionData.description,
      payment_method: transactionData.payment_method,
      metadata: transactionData.metadata as Json
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating transaction:', error);
    return { data: null, error };
  }

  return { data, error: null };
};

// Fix the metadata issue in this function too (line ~380)
export const getUserTransactions = async (userId: string) => {
  const { data, error } = await supabase
    .from('transactions')
    .select(`
      *,
      savings_plan: savings_plan_id(
        id,
        name,
        type
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching user transactions:', error);
    return { data: null, error };
  }

  return { data, error: null };
};

// Fix the function with missing type property (line ~442)
export const updateTransactionStatus = async (
  transactionId: string,
  status: "pending" | "completed" | "failed",
  metadata?: Record<string, any>
) => {
  const updateData: {
    status: "pending" | "completed" | "failed";
    processed_at?: string;
    metadata?: Json;
  } = {
    status
  };
  
  if (status === 'completed') {
    updateData.processed_at = new Date().toISOString();
  }
  
  if (metadata) {
    updateData.metadata = metadata as Json;
  }
  
  const { data, error } = await supabase
    .from('transactions')
    .update(updateData)
    .eq('id', transactionId)
    .select()
    .single();
  
  if (error) {
    console.error('Error updating transaction status:', error);
    return { data: null, error };
  }
  
  return { data, error: null };
};
