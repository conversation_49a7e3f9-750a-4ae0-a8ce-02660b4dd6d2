
import { supabase } from './client';

export type SavingsPlanType = 'personal' | 'group' | 'target' | 'fixed';
export type SavingsPlanStatus = 'active' | 'paused' | 'completed';

export interface SavingsPlan {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  type: SavingsPlanType;
  target_amount?: number;
  current_amount: number;
  interest_rate: number;
  status: SavingsPlanStatus;
  frequency?: string;
  automatic_deduction: boolean;
  deduction_amount?: number;
  start_date: string;
  target_date?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface CreateSavingsPlanInput {
  name: string;
  description?: string;
  type: SavingsPlanType;
  target_amount?: number;
  interest_rate?: number;
  frequency?: string;
  automatic_deduction?: boolean;
  deduction_amount?: number;
  target_date?: string;
  metadata?: Record<string, any>;
}

export async function createSavingsPlan(data: CreateSavingsPlanInput) {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user found');

    const { data: plan, error } = await supabase
      .from('savings_plans')
      .insert({
        user_id: user.id,
        name: data.name,
        description: data.description,
        type: data.type,
        target_amount: data.target_amount,
        interest_rate: data.interest_rate || 0,
        frequency: data.frequency,
        automatic_deduction: data.automatic_deduction || false,
        deduction_amount: data.deduction_amount,
        target_date: data.target_date,
        metadata: data.metadata
      })
      .select()
      .single();

    if (error) throw error;

    // Create notification
    await supabase.functions.invoke('handle-notification', {
      body: {
        userId: user.id,
        title: 'Savings Plan Created',
        message: `Your new savings plan "${data.name}" has been created successfully.`,
        type: 'success',
        channel: 'in-app',
        priority: 'medium'
      }
    });

    return { data: plan, error: null };
  } catch (error) {
    console.error('Error creating savings plan:', error);
    return { data: null, error };
  }
}

export async function getUserSavingsPlans() {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user found');

    const { data, error } = await supabase
      .from('savings_plans')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error getting user savings plans:', error);
    return { data: null, error };
  }
}

export async function getSavingsPlanDetails(planId: string) {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user found');

    const { data, error } = await supabase
      .from('savings_plans')
      .select(`
        *,
        transactions:transactions (*)
      `)
      .eq('id', planId)
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error getting savings plan details:', error);
    return { data: null, error };
  }
}

export async function updateSavingsPlanStatus(planId: string, status: SavingsPlanStatus) {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user found');

    const { data, error } = await supabase
      .from('savings_plans')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', planId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) throw error;

    // Create notification
    await supabase.functions.invoke('handle-notification', {
      body: {
        userId: user.id,
        title: `Savings Plan ${status.charAt(0).toUpperCase() + status.slice(1)}`,
        message: `Your savings plan "${data.name}" has been ${status}.`,
        type: 'info',
        channel: 'in-app',
        priority: 'medium'
      }
    });

    return { data, error: null };
  } catch (error) {
    console.error('Error updating savings plan status:', error);
    return { data: null, error };
  }
}

export async function updateSavingsPlan(planId: string, updates: Partial<CreateSavingsPlanInput>) {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user found');

    const { data, error } = await supabase
      .from('savings_plans')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', planId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating savings plan:', error);
    return { data: null, error };
  }
}

export async function deleteSavingsPlan(planId: string) {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user found');

    const { data, error } = await supabase
      .from('savings_plans')
      .delete()
      .eq('id', planId)
      .eq('user_id', user.id);

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error deleting savings plan:', error);
    return { data: null, error };
  }
}

export async function createGroupSavingsPlan(data: CreateSavingsPlanInput, members: string[]) {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user found');

    // Start a transaction
    const { data: plan, error: planError } = await supabase
      .from('savings_plans')
      .insert({
        user_id: user.id,
        name: data.name,
        description: data.description,
        type: 'group',
        target_amount: data.target_amount,
        interest_rate: data.interest_rate || 0,
        frequency: data.frequency,
        automatic_deduction: data.automatic_deduction || false,
        deduction_amount: data.deduction_amount,
        target_date: data.target_date,
        metadata: {
          ...data.metadata,
          member_count: members.length + 1 // Including the creator
        }
      })
      .select()
      .single();

    if (planError) throw planError;

    // Add creator as member with 'owner' role
    const { error: ownerError } = await supabase
      .from('group_savings_members')
      .insert({
        savings_plan_id: plan.id,
        user_id: user.id,
        role: 'owner',
        contribution_amount: data.deduction_amount || 0
      });

    if (ownerError) throw ownerError;

    // Add other members
    if (members.length > 0) {
      const memberEntries = members.map(memberId => ({
        savings_plan_id: plan.id,
        user_id: memberId,
        role: 'member',
        contribution_amount: data.deduction_amount || 0
      }));

      const { error: membersError } = await supabase
        .from('group_savings_members')
        .insert(memberEntries);

      if (membersError) throw membersError;

      // Notify all members
      for (const memberId of members) {
        await supabase.functions.invoke('handle-notification', {
          body: {
            userId: memberId,
            title: 'Added to Group Savings',
            message: `You have been added to "${data.name}" group savings plan by ${user.email}.`,
            type: 'info',
            channel: 'all',
            priority: 'high'
          }
        });
      }
    }

    // Notify creator
    await supabase.functions.invoke('handle-notification', {
      body: {
        userId: user.id,
        title: 'Group Savings Created',
        message: `Your group savings plan "${data.name}" has been created successfully.`,
        type: 'success',
        channel: 'in-app',
        priority: 'medium'
      }
    });

    return { data: plan, error: null };
  } catch (error) {
    console.error('Error creating group savings plan:', error);
    return { data: null, error };
  }
}

export async function getGroupSavingsMembers(planId: string) {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user found');

    const { data, error } = await supabase
      .from('group_savings_members')
      .select(`
        *,
        profiles:user_id (
          id,
          first_name,
          last_name,
          email,
          avatar_url
        )
      `)
      .eq('savings_plan_id', planId);

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error getting group savings members:', error);
    return { data: null, error };
  }
}
