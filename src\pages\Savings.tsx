import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { PiggyBank, TrendingUp, Users, Plus, Calendar, Target, Trophy, Zap } from 'lucide-react';
import { Link } from 'react-router-dom';
import { SavingsGoalModal } from '@/components/savings/SavingsGoalModal';
import { QuickDepositModal } from '@/components/savings/QuickDepositModal';

const Savings = () => {
  const [showSavingsGoalModal, setShowSavingsGoalModal] = useState(false);
  const [showQuickDepositModal, setShowQuickDepositModal] = useState(false);

  const savingsStats = [
    { title: 'Total Savings', value: '₦245,000', change: '+12%', icon: <PiggyBank className="h-5 w-5" /> },
    { title: 'Active Plans', value: '3', change: '+1', icon: <Target className="h-5 w-5" /> },
    { title: 'Monthly Growth', value: '₦18,500', change: '+8%', icon: <TrendingUp className="h-5 w-5" /> },
    { title: 'Group Savings', value: '₦65,000', change: '+15%', icon: <Users className="h-5 w-5" /> }
  ];

  const personalPlans = [
    {
      id: 1,
      name: 'Emergency Fund',
      target: 500000,
      current: 245000,
      duration: '12 months',
      frequency: 'Daily',
      status: 'active'
    },
    {
      id: 2,
      name: 'New Car Fund',
      target: 2000000,
      current: 450000,
      duration: '24 months',
      frequency: 'Weekly',
      status: 'active'
    },
    {
      id: 3,
      name: 'Vacation Fund',
      target: 300000,
      current: 120000,
      duration: '8 months',
      frequency: 'Bi-weekly',
      status: 'active'
    }
  ];

  const groupPlans = [
    {
      name: 'Family Christmas Fund',
      members: 8,
      target: 1200000,
      current: 850000,
      nextTurn: 'Your turn next!',
      status: 'active'
    },
    {
      name: 'Office Investment Club',
      members: 12,
      target: 5000000,
      current: 2800000,
      nextTurn: 'Turn 5 of 12',
      status: 'active'
    }
  ];

  const recentActivity = [
    { type: 'deposit', amount: 5000, plan: 'Emergency Fund', date: '2 hours ago' },
    { type: 'payout', amount: 150000, plan: 'Family Christmas Fund', date: '1 day ago' },
    { type: 'deposit', amount: 25000, plan: 'New Car Fund', date: '3 days ago' },
    { type: 'milestone', plan: 'Vacation Fund', milestone: '40% complete', date: '1 week ago' }
  ];

  const savingsTips = [
    { title: 'Start Small', description: 'Begin with ₦500 daily contributions to build the habit' },
    { title: 'Automate Savings', description: 'Set up automatic transfers to never miss a contribution' },
    { title: 'Track Progress', description: 'Regular monitoring keeps you motivated and on track' },
    { title: 'Join Groups', description: 'Group savings provide accountability and faster goal achievement' }
  ];

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      <SavingsGoalModal 
        open={showSavingsGoalModal} 
        onOpenChange={setShowSavingsGoalModal} 
      />

      <QuickDepositModal 
        open={showQuickDepositModal} 
        onOpenChange={setShowQuickDepositModal} 
      />

      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8" data-aos="fade-up">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold mb-2">Your Savings Journey</h1>
          <p className="text-muted-foreground">Track your progress and achieve your financial goals</p>
        </div>
        <div className="flex flex-col xs:flex-row gap-2 w-full sm:w-auto">
          <Button
            className="bg-brand-blue hover:bg-brand-blue/90 w-full xs:w-auto"
            onClick={() => setShowSavingsGoalModal(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden xs:inline">New Plan</span>
            <span className="xs:hidden">New</span>
          </Button>
          <Link to="/user/rotational-savings" className="w-full xs:w-auto">
            <Button variant="outline" className="w-full">
              <Users className="h-4 w-4 mr-2" />
              <span className="hidden xs:inline">Join Group</span>
              <span className="xs:hidden">Group</span>
            </Button>
          </Link>
          <Button
            variant="outline"
            onClick={() => setShowQuickDepositModal(true)}
            className="w-full xs:w-auto"
          >
            <PiggyBank className="h-4 w-4 mr-2" />
            <span className="hidden xs:inline">Quick Deposit</span>
            <span className="xs:hidden">Deposit</span>
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 mb-8">
        {savingsStats.map((stat, index) => (
          <Card key={index} className="stat-card" data-aos="fade-up" data-aos-delay={index * 100}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-brand-blue/10 rounded-lg">
                  {stat.icon}
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                </div>
              </div>
              <Badge variant="secondary" className="text-green-600 bg-green-50">
                {stat.change}
              </Badge>
            </div>
          </Card>
        ))}
      </div>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Personal Savings Plans */}
        <div className="lg:col-span-2 space-y-6">
          <Card data-aos="fade-right">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Personal Savings Plans
                </CardTitle>
                <Badge>{personalPlans.length} Active</Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {personalPlans.map((plan, index) => (
                <div key={plan.id} className="p-4 border rounded-lg space-y-3" data-aos="fade-up" data-aos-delay={index * 100}>
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold">{plan.name}</h3>
                    <div className="text-right">
                      <p className="text-sm text-muted-foreground">{plan.frequency}</p>
                      <Badge variant="outline">{plan.status}</Badge>
                    </div>
                  </div>
                  <Progress value={(plan.current / plan.target) * 100} className="h-2" />
                  <div className="flex justify-between text-sm">
                    <span>₦{plan.current.toLocaleString()} / ₦{plan.target.toLocaleString()}</span>
                    <span className="text-muted-foreground">{plan.duration}</span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Group Savings */}
          <Card data-aos="fade-right" data-aos-delay="200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Group Savings Plans
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {groupPlans.map((group, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-3" data-aos="fade-up" data-aos-delay={index * 100}>
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold">{group.name}</h3>
                    <Badge variant="secondary">{group.members} members</Badge>
                  </div>
                  <Progress value={(group.current / group.target) * 100} className="h-2" />
                  <div className="flex justify-between text-sm">
                    <span>₦{group.current.toLocaleString()} / ₦{group.target.toLocaleString()}</span>
                    <span className="text-brand-blue font-medium">{group.nextTurn}</span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Recent Activity */}
          <Card data-aos="fade-left">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {recentActivity.map((activity, index) => (
                <div key={index} className="flex items-start gap-3 p-2 rounded-lg hover:bg-muted/50" data-aos="fade-up" data-aos-delay={index * 50}>
                  <div className={`p-1 rounded-full ${
                    activity.type === 'deposit' ? 'bg-green-100 text-green-600' :
                    activity.type === 'payout' ? 'bg-blue-100 text-blue-600' :
                    'bg-yellow-100 text-yellow-600'
                  }`}>
                    {activity.type === 'deposit' ? <TrendingUp className="h-3 w-3" /> :
                     activity.type === 'payout' ? <PiggyBank className="h-3 w-3" /> :
                     <Trophy className="h-3 w-3" />}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">
                      {activity.type === 'deposit' && `+₦${activity.amount?.toLocaleString()}`}
                      {activity.type === 'payout' && `Received ₦${activity.amount?.toLocaleString()}`}
                      {activity.type === 'milestone' && activity.milestone}
                    </p>
                    <p className="text-xs text-muted-foreground">{activity.plan}</p>
                    <p className="text-xs text-muted-foreground">{activity.date}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Savings Tips */}
          <Card data-aos="fade-left" data-aos-delay="200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Trophy className="h-5 w-5" />
                Savings Tips
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {savingsTips.map((tip, index) => (
                <div key={index} className="space-y-1" data-aos="fade-up" data-aos-delay={index * 100}>
                  <h4 className="font-medium text-sm">{tip.title}</h4>
                  <p className="text-xs text-muted-foreground">{tip.description}</p>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Savings;
