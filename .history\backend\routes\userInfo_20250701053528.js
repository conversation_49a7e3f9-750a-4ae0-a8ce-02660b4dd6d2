const express = require('express');
const router = express.Router();
const User = require('../models/user');

// Get user balance by userId
router.get('/balance/:userId', async (req, res) => {
  const userId = req.params.userId;
  console.log(`[UserInfo] GET /balance/${userId}`);
  try {
    const user = await User.findById(userId);
    if (!user) {
      console.warn(`[UserInfo] User not found for id: ${userId}`);
      return res.status(404).json({ error: 'User not found' });
    }
    console.log(`[UserInfo] User found:`, { id: user._id, balance: user.balance });
    res.json({ balance: user.balance });
  } catch (err) {
    console.error(`[UserInfo] Error fetching balance for ${userId}:`, err);
    res.status(500).json({ error: err.message });
  }
});

module.exports = router;