const express = require('express');
const router = express.Router();
const Withdraw = require('../models/withdraw');
const User = require('../models/user');
const Deposit = require('../models/deposit');
const Transaction = require('../models/transaction');
const Notification = require('../models/notification');
const authMiddleware = require('../middleware/authMiddleware');

const axios = require('axios');

// Initiate Paystack withdrawal (payout)
router.post('/initiate', authMiddleware, async (req, res) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'User authentication failed. Please log in again.' });
    }
    const userId = req.user.id;
    const { amount, notes, bankAccountId } = req.body;
    if (!amount || !bankAccountId) {
      return res.status(400).json({ error: 'Amount and bank account are required.' });
    }
    if (typeof amount !== 'number' || amount <= 0) {
      return res.status(400).json({ error: 'Amount must be a positive number.' });
    }
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    if (user.balance < amount) {
      return res.status(400).json({ error: 'Insufficient balance for withdrawal.' });
    }
    // Get bank account details
    const WithdrawAccount = require('../models/withdrawAccounts');
    const account = await WithdrawAccount.findById(bankAccountId);
    if (!account) {
      return res.status(404).json({ error: 'Withdrawal account not found' });
    }
    // Step 1: Resolve account number (optional, for extra validation)
    // Step 2: Create transfer recipient on Paystack
    const recipientRes = await axios.post(
      `${process.env.PAYSTACK_BASE_URL}/transferrecipient`,
      {
        type: 'nuban',
        name: account.accountName,
        account_number: account.accountNumber,
        bank_code: account.bankCode || '058', // Default to GTBank if not provided
        currency: 'NGN',
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );
    const recipientCode = recipientRes.data.data.recipient_code;
    // Step 3: Initiate transfer
    const transferRes = await axios.post(
      `${process.env.PAYSTACK_BASE_URL}/transfer`,
      {
        source: 'balance',
        amount: Math.round(amount * 100),
        recipient: recipientCode,
        reason: notes || 'Withdrawal',
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );
    const transferCode = transferRes.data.data.transfer_code;
    // Create withdrawal record (pending)
    const withdraw = new Withdraw({
      userId,
      amount,
      notes,
      bankAccountId,
      paystackTransferCode: transferCode,
      paystackStatus: 'pending',
      status: 'pending',
    });
    await withdraw.save();
    // Notify user
    await Notification.create({
      userId,
      type: 'withdrawal_requested',
      title: 'Withdrawal Requested',
      message: `Your withdrawal request of ₦${amount} is being processed.`,
    });
    res.status(201).json({ withdraw, transferCode });
  } catch (err) {
    console.error('Error in /initiate:', err.response?.data || err.message);
    res.status(500).json({ error: 'Failed to initiate withdrawal', details: err.message });
  }
});
// Paystack webhook for transfer status
router.post('/webhook', async (req, res) => {
  try {
    const event = req.body;
    if (event.event === 'transfer.success') {
      const transferCode = event.data.transfer_code;
      const withdraw = await Withdraw.findOne({ paystackTransferCode: transferCode });
      if (withdraw && withdraw.status === 'pending') {
        withdraw.status = 'success';
        withdraw.paystackStatus = 'success';
        withdraw.processedAt = new Date();
        await withdraw.save();
        // Deduct user balance now
        const user = await User.findById(withdraw.userId);
        if (user) {
          user.balance -= withdraw.amount;
          await user.save();
        }
        // Create transaction record
        const Transaction = require('../models/transaction');
        await Transaction.create({
          userId: withdraw.userId,
          type: 'withdrawal',
          amount: withdraw.amount,
          description: withdraw.notes || 'Withdrawal',
          balanceAfter: user ? user.balance : 0,
          reference: transferCode,
        });
        // Notify user
        await Notification.create({
          userId: withdraw.userId,
          type: 'withdrawal_approved',
          title: 'Withdrawal Successful',
          message: `Your withdrawal of ₦${withdraw.amount} was successful.`,
        });
      }
    } else if (event.event === 'transfer.failed') {
      const transferCode = event.data.transfer_code;
      const withdraw = await Withdraw.findOne({ paystackTransferCode: transferCode });
      if (withdraw && withdraw.status === 'pending') {
        withdraw.status = 'failed';
        withdraw.paystackStatus = 'failed';
        withdraw.processedAt = new Date();
        await withdraw.save();
        // Notify user
        await Notification.create({
          userId: withdraw.userId,
          type: 'withdrawal_rejected',
          title: 'Withdrawal Failed',
          message: `Your withdrawal of ₦${withdraw.amount} failed. Please try again.`,
        });
      }
    }
    res.sendStatus(200);
  } catch (error) {
    console.error('Paystack withdrawal webhook error:', error.message);
    res.sendStatus(500);
  }
});

// Get all withdrawals for a user
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const withdrawals = await Withdraw.find({ userId })
      .sort({ createdAt: -1 })
      .populate('bankAccountId');
    res.json(withdrawals);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch withdrawals' });
  }
});

// Get all withdrawals (admin or for all users)
router.get('/all', async (req, res) => {
  try {
    const withdrawals = await Withdraw.find().sort({ createdAt: -1 }).populate('bankAccountId');
    res.json(withdrawals);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch all withdrawals' });
  }
});

// (Optional) Admin: Approve or reject a withdrawal
router.patch('/:id', async (req, res) => {
  try {
    const { status } = req.body;
    const withdraw = await Withdraw.findById(req.params.id);
    if (!withdraw) return res.status(404).json({ error: 'Withdrawal not found' });
    withdraw.status = status;
    if (status === 'approved') {
      withdraw.processedAt = new Date();
      // Deduct balance here if not already done
      const user = await User.findById(withdraw.userId);
      if (user && user.balance >= withdraw.amount) {
        user.balance -= withdraw.amount;
        await user.save();
      }
      await Notification.create({
        userId: withdraw.userId,
        type: 'withdrawal_approved',
        title: 'Withdrawal Approved',
        message: `Your withdrawal of ₦${withdraw.amount} has been approved.`,
      });
    } else if (status === 'rejected') {
      withdraw.processedAt = new Date();
      // Refund the amount to the user's balance
      const user = await User.findById(withdraw.userId);
      if (user) {
        user.balance += withdraw.amount;
        await user.save();
        // Create a Transaction record for the refund
        await Transaction.create({
          userId: user._id,
          type: 'withdrawal_refund',
          amount: withdraw.amount,
          description: 'Refund for rejected withdrawal',
          balanceAfter: user.balance,
          reference: `WD-REFUND-${Date.now()}`
        });
      }
      await Notification.create({
        userId: withdraw.userId,
        type: 'withdrawal_rejected',
        title: 'Withdrawal Rejected',
        message: `Your withdrawal of ₦${withdraw.amount} was rejected.`,
      });
    }
    await withdraw.save();
    res.json(withdraw);
  } catch (err) {
    res.status(500).json({ error: 'Failed to update withdrawal status' });
  }
});

// PUT: Update withdrawal status (admin)
router.put('/:id/status', async (req, res) => {
  try {
    const { status, notes } = req.body;
    const withdraw = await Withdraw.findById(req.params.id);
    if (!withdraw) return res.status(404).json({ error: 'Withdrawal not found' });
    withdraw.status = status;
    if (notes) withdraw.notes = notes;
    if (status === 'approved') {
      withdraw.processedAt = new Date();
      // Deduct balance here if not already done
      const user = await User.findById(withdraw.userId);
      if (user && user.balance >= withdraw.amount) {
        user.balance -= withdraw.amount;
        await user.save();
      }
      await Notification.create({
        userId: withdraw.userId,
        type: 'withdrawal_approved',
        title: 'Withdrawal Approved',
        message: `Your withdrawal of ₦${withdraw.amount} has been approved.`,
      });
    } else if (status === 'rejected') {
      withdraw.processedAt = new Date();
      // Refund the amount to the user's balance
      const user = await User.findById(withdraw.userId);
      if (user) {
        user.balance += withdraw.amount;
        await user.save();
        // Create a Transaction record for the refund
        await Transaction.create({
          userId: user._id,
          type: 'withdrawal_refund',
          amount: withdraw.amount,
          description: 'Refund for rejected withdrawal',
          balanceAfter: user.balance,
          reference: `WD-REFUND-${Date.now()}`
        });
      }
      await Notification.create({
        userId: withdraw.userId,
        type: 'withdrawal_rejected',
        title: 'Withdrawal Rejected',
        message: `Your withdrawal of ₦${withdraw.amount} was rejected.`,
      });
    }
    await withdraw.save();
    res.json(withdraw);
  } catch (err) {
    res.status(500).json({ error: 'Failed to update withdrawal status' });
  }
});

// Get pending withdrawals count for a user
router.get('/user/:userId/pending-count', async (req, res) => {
  try {
    const { userId } = req.params;
    const count = await Withdraw.countDocuments({ userId, status: 'pending' });
    res.json({ count });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch pending withdrawals count' });
  }
});

// Admin: Get count of all pending withdrawals
router.get('/pending-count', async (req, res) => {
  try {
    const count = await Withdraw.countDocuments({ status: 'pending' });
    res.json({ count });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch pending withdrawals count' });
  }
});

module.exports = router;
