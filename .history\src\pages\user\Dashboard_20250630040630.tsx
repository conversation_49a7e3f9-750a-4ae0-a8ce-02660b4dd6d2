
import {
  BarChart3,
  Credit<PERSON>ard,
  DollarSign,
  PiggyBank,
  PlusCircle,
  Send,
  TrendingUp,
  Clock,
  Target,
  CalendarDays,
  ArrowUpRight,
  Percent,
  ArrowRight,
  Wallet,
  Gift,
  Sparkles,
  Users,
  Eye,
  EyeOff,
  Upload,
  UserPlus,
  Share2,
} from "lucide-react";
import { StatCard } from "@/components/ui/stat-card";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { AvatarGroup } from "@/components/ui/avatar-group";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, Di<PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { PaymentProofUpload } from "@/components/payments/PaymentProofUpload";
import { useBalance } from "@/hooks/use-balance";
import { PayoutModeDialog } from "@/components/payments/PayoutModeDialog";
import { QuickPayoutDialog } from "@/components/payments/QuickPayoutDialog";
import { SavingsGoalModal } from "@/components/savings/SavingsGoalModal";
import { QuickDepositModal } from "@/components/savings/QuickDepositModal";

const recentTransactions = [
  {
    id: "t1",
    description: "Daily Deposit",
    amount: 5000,
    date: "Today",
    type: "deposit",
  },
  {
    id: "t2",
    description: "Withdrawal",
    amount: -2000,
    date: "Yesterday",
    type: "withdrawal",
  },
  {
    id: "t3",
    description: "Bonus Credit",
    amount: 1000,
    date: "May 21, 2023",
    type: "deposit",
  },
  {
    id: "t4",
    description: "Daily Deposit",
    amount: 5000,
    date: "May 20, 2023",
    type: "deposit",
  },
];

const savingsGoals = [
  {
    id: "g1",
    name: "New Car",
    target: 2500000,
    current: 750000,
    deadline: "Dec 2023",
    contributors: [
      { fallback: "AJ" },
      { fallback: "TM" },
      { fallback: "NK" },
    ],
  },
  {
    id: "g2",
    name: "House Down Payment",
    target: 5000000,
    current: 1250000,
    deadline: "Jun 2024",
    contributors: [
      { fallback: "AJ" },
      { fallback: "OS" },
    ],
  },
];

const savingsPlans = [
  {
    id: "p1",
    name: "Daily Saver",
    description: "Save ₦5,000 daily",
    icon: <Clock className="h-5 w-5 text-brand-blue" />,
    stats: "30 day streak",
    route: "/user/savings-plans"
  },
  {
    id: "p2",
    name: "Group Savings",
    description: "With 6 friends",
    icon: <Users className="h-5 w-5 text-brand-blue" />,
    stats: "₦420,000 collected",
    route: "/user/rotational-savings"
  },
  {
    id: "p3",
    name: "Special Offer",
    description: "10% interest rate",
    icon: <Gift className="h-5 w-5 text-brand-blue" />,
    stats: "Limited time",
    route: "/user/savings-plans"
  }
];


const stored = localStorage.getItem('auth_user');
const user = stored ? JSON.parse(stored) : null;

const UserDashboard = () => {
  const navigate = useNavigate();
  const [showBalance, setShowBalance] = useState(true);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showPayoutModeModal, setShowPayoutModeModal] = useState(false);
  const [showPayoutModal, setShowPayoutModal] = useState(false);
  const [showSavingsGoalModal, setShowSavingsGoalModal] = useState(false);
  const [showQuickDepositModal, setShowQuickDepositModal] = useState(false);
  const { balance, isLoading: balanceLoading } = useBalance();

  const [transactions, setTransactions] = useState(recentTransactions);
  
  useEffect(() => {
    const savedTransactions = localStorage.getItem('user_transactions');
    if (savedTransactions) {
      const parsedTransactions = JSON.parse(savedTransactions);
      const combinedTransactions = [...parsedTransactions, ...recentTransactions]
        .slice(0, 4);
      setTransactions(combinedTransactions);
    }
  }, []);

  const handleQuickDeposit = () => {
    setShowQuickDepositModal(true);
  };

  const toggleBalanceVisibility = () => {
    setShowBalance(!showBalance);
  };

  const handlePaymentProofSubmit = (formData) => {
    console.log("Payment proof submitted:", formData);
    toast.success("Payment proof submitted successfully! We'll verify your payment soon.");
    setShowPaymentModal(false);
  };

  const handleConfigurePayoutSettings = () => {
    setShowPayoutModal(false);
    setShowPayoutModeModal(true);
  };

  const handleInviteFriends = () => {
    // Create a shareable link for referrals
    const referralLink = `${window.location.origin}/signup?ref=${user?.profile?.phone || user?.phone || ''}`;
    
    if (navigator.share) {
      navigator.share({
        title: 'Join ASUSU by Koja',
        text: 'Start saving with me on ASUSU! Get ₦1,000 bonus when you sign up.',
        url: referralLink,
      }).then(() => {
        toast.success("Invite sent successfully!");
      }).catch((error) => {
        console.log('Error sharing:', error);
        // Fallback to clipboard
        navigator.clipboard.writeText(referralLink);
        toast.success("Referral link copied to clipboard!");
      });
    } else {
      // Fallback for browsers that don't support Web Share API
      navigator.clipboard.writeText(referralLink);
      toast.success("Referral link copied to clipboard!");
    }
  };

  const handleCreateNewGoal = () => {
    setShowSavingsGoalModal(true);
  };

  const handleCreateGroupSavings = () => {
    navigate('/user/rotational-savings');
  };

  const handlePlanClick = (plan) => {
    navigate(plan.route);
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight text-brand-blue">Welcome, {user?.profile?.first_name || user?.name || "User"}</h2>
          <p className="text-muted-foreground">
            <span className="text-brand-blue font-semibold">ASUSU</span> dashboard
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            onClick={handleQuickDeposit}
            className="h-10 gap-2 bg-brand-yellow hover:bg-brand-yellow/90 text-foreground animate-yellow-pulse"
          >
            <PlusCircle className="h-4 w-4" />
            Deposit
          </Button>
          <Button 
            variant="outline" 
            className="h-10 gap-2 hover:border-brand-blue/50 hover:bg-brand-blue/10"
            onClick={() => setShowPayoutModal(true)}
          >
            <Send className="h-4 w-4" />
            Payout
          </Button>
        </div>
      </div>

      <Dialog open={showPaymentModal} onOpenChange={setShowPaymentModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Submit Payment Proof</DialogTitle>
          </DialogHeader>
          <PaymentProofUpload 
            onSubmit={handlePaymentProofSubmit}
            isLoading={false}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={showPayoutModeModal} onOpenChange={setShowPayoutModeModal}>
        <PayoutModeDialog onClose={() => setShowPayoutModeModal(false)} />
      </Dialog>

      <Dialog open={showPayoutModal} onOpenChange={setShowPayoutModal}>
        <QuickPayoutDialog 
          onClose={() => setShowPayoutModal(false)} 
          onConfigureSettings={handleConfigurePayoutSettings}
        />
      </Dialog>

      <SavingsGoalModal 
        open={showSavingsGoalModal} 
        onOpenChange={setShowSavingsGoalModal} 
      />

      <QuickDepositModal 
        open={showQuickDepositModal} 
        onOpenChange={setShowQuickDepositModal} 
      />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="col-span-full p-6 bg-brand-blue text-white overflow-hidden relative shadow-glass-strong hover:shadow-yellow border-2 border-brand-yellow/20 transition-all duration-300 hover:-translate-y-1">
          <div className="absolute right-0 bottom-0 opacity-10">
            <PiggyBank className="h-40 w-40" />
          </div>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <div className="mb-4">
                <p className="text-white/80 text-sm">Account ID: #{userData.phone}</p>
              </div>
              <div className="flex items-center gap-2">
                <p className="text-3xl font-bold">
                  {showBalance ? `₦${balanceLoading ? '...' : balance.toLocaleString()}` : "₦••••••••"}
                </p>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-8 w-8 text-white/80 hover:text-white hover:bg-white/10"
                  onClick={toggleBalanceVisibility}
                >
                  {showBalance ? 
                    <EyeOff className="h-4 w-4" /> : 
                    <Eye className="h-4 w-4" />
                  }
                </Button>
              </div>
              <p className="text-white/80 text-sm">Total Savings Balance</p>
            </div>
            <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 gap-3 w-full sm:w-auto">
              <div className="bg-white/10 p-3 rounded-lg hover:bg-white/20 transition-colors cursor-pointer">
                <div className="flex items-center gap-2">
                  <div className="p-2 rounded-full bg-white/20">
                    <Target className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <p className="text-sm text-white/80">Goal Progress</p>
                    <p className="font-bold">45%</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/10 p-3 rounded-lg hover:bg-white/20 transition-colors cursor-pointer">
                <div className="flex items-center gap-2">
                  <div className="p-2 rounded-full bg-white/20">
                    <CalendarDays className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <p className="text-sm text-white/80">Next Savings</p>
                    <p className="font-bold">Today</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/10 p-3 rounded-lg hover:bg-white/20 transition-colors cursor-pointer">
                <div className="flex items-center gap-2">
                  <div className="p-2 rounded-full bg-white/20">
                    <ArrowUpRight className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <p className="text-sm text-white/80">Monthly Growth</p>
                    <p className="font-bold">+12.3%</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/10 p-3 rounded-lg hover:bg-white/20 transition-colors cursor-pointer">
                <div className="flex items-center gap-2">
                  <div className="p-2 rounded-full bg-white/20">
                    <Percent className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <p className="text-sm text-white/80">Interest Rate</p>
                    <p className="font-bold">5.2%</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <div className="grid gap-4 grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 gap-4">
        {savingsPlans.map((plan) => (
          <Card 
            key={plan.id} 
            className="hover:border-brand-yellow/50 hover:shadow-yellow transition-all duration-300 group cursor-pointer"
            onClick={() => handlePlanClick(plan)}
          >
            <CardContent className="p-4 flex items-center gap-4">
              <div className="p-3 rounded-full bg-brand-lightBlue/50 group-hover:bg-brand-yellow/20 transition-colors">
                {plan.icon}
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">{plan.name}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">{plan.description}</p>
                <p className="text-xs text-brand-blue mt-1 font-medium">{plan.stats}</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-4 grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Savings"
          value="₦1,250,000"
          icon={<PiggyBank className="h-4 w-4" />}
          trend={{ value: 12, isPositive: true }}
          className="animate-scale-in hover:border-brand-yellow/50 hover:shadow-yellow"
        />
        <StatCard
          title="Monthly Deposits"
          value="₦150,000"
          icon={<DollarSign className="h-4 w-4" />}
          trend={{ value: 8, isPositive: true }}
          className="animate-scale-in [animation-delay:100ms] hover:border-brand-yellow/50 hover:shadow-yellow"
        />
        <StatCard
          title="Daily Average"
          value="₦5,000"
          icon={<BarChart3 className="h-4 w-4" />}
          trend={{ value: 3, isPositive: true }}
          className="animate-scale-in [animation-delay:200ms] hover:border-brand-yellow/50 hover:shadow-yellow"
        />
        <StatCard
          title="Withdrawals"
          value="₦20,000"
          icon={<CreditCard className="h-4 w-4" />}
          trend={{ value: 5, isPositive: false }}
          className="animate-scale-in [animation-delay:300ms] hover:border-brand-yellow/50 hover:shadow-yellow"
        />
      </div>

      <div className="grid gap-6 grid-cols-1 md:grid-cols-3">
        <Card className="md:col-span-2 border-brand-yellow/20 hover:border-brand-yellow shadow-kola hover:shadow-glass-strong">
          <CardHeader>
            <CardTitle>Recent Transactions</CardTitle>
            <CardDescription>
              Your latest financial activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {transactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-brand-yellow/5 transition-colors hover:border-brand-yellow/30"
                >
                  <div className="flex items-center gap-4">
                    <div className={`p-2 rounded-full ${
                      transaction.type === 'deposit' 
                        ? 'bg-green-100 text-green-600' 
                        : 'bg-red-100 text-red-600'
                    }`}>
                      {transaction.type === 'deposit' ? (
                        <TrendingUp className="h-4 w-4" />
                      ) : (
                        <CreditCard className="h-4 w-4" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{transaction.description}</p>
                      <p className="text-sm text-muted-foreground">
                        {transaction.date}
                      </p>
                    </div>
                  </div>
                  <p className={`font-medium ${
                    transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {transaction.amount > 0 ? '+' : ''}
                    ₦{Math.abs(transaction.amount).toLocaleString()}
                  </p>
                </div>
              ))}
              <Button
                variant="outline"
                className="w-full hover:bg-brand-yellow/10 hover:border-brand-yellow/50 group"
                onClick={() => navigate('/user/transactions')}
              >
                View All Transactions
                <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="lg:row-span-2 border-brand-yellow/20 hover:border-brand-yellow shadow-kola hover:shadow-glass-strong">
          <CardHeader>
            <CardTitle>Savings Goals</CardTitle>
            <CardDescription>Track your progress</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-5">
              {savingsGoals.map((goal) => (
                <div key={goal.id} className="space-y-3 p-4 rounded-lg border bg-gradient-to-br from-white to-brand-yellow/5 hover:shadow-yellow transition-all duration-300">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium">{goal.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        Target: ₦{goal.target.toLocaleString()} • {goal.deadline}
                      </p>
                    </div>
                    <AvatarGroup items={goal.contributors} />
                  </div>
                  
                  <Progress value={(goal.current / goal.target) * 100} className="h-2 bg-muted" indicatorColor="bg-brand-yellow" />
                  
                  <div className="flex justify-between text-sm">
                    <p>₦{goal.current.toLocaleString()}</p>
                    <p className="text-muted-foreground">
                      {Math.round((goal.current / goal.target) * 100)}%
                    </p>
                  </div>
                </div>
              ))}
              
              <Button
                variant="outline"
                className="w-full mt-4 hover:bg-brand-yellow/10 hover:border-brand-yellow/50 group"
                onClick={handleCreateNewGoal}
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Create New Goal
              </Button>
            </div>
          </CardContent>
          <CardFooter className="border-t pt-6">
            <div className="w-full bg-brand-yellow/15 rounded-lg p-4 flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-brand-blue">Group Savings</p>
                <p className="text-xs text-muted-foreground">Save with friends & family</p>
              </div>
              <Button 
                size="sm" 
                variant="accent" 
                onClick={handleCreateGroupSavings} 
                className="bg-brand-yellow text-foreground hover:bg-brand-yellow/90"
              >
                Create
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>

      <Card className="overflow-hidden border-brand-yellow/20">
        <div className="bg-gradient-to-r from-brand-yellow/20 to-brand-lightBlue/20 p-6">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-brand-yellow p-3 rounded-full">
                <Sparkles className="h-6 w-6 text-foreground" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">Refer Friends & Earn</h3>
                <p className="text-sm text-muted-foreground">Get ₦1,000 for each friend who joins</p>
              </div>
            </div>
            <Button 
              className="bg-brand-blue hover:bg-brand-blue/90 gap-2"
              onClick={handleInviteFriends}
            >
              <UserPlus className="h-4 w-4" />
              Invite Friends
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default UserDashboard;
