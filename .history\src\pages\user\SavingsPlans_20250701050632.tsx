// Group plans where user is a member (from /api/group-savings/group-plans/my)
// ...existing code...
  const [myGroupPlans, setMyGroupPlans] = useState<any[]>([]);
  const [myGroupPlansLoading, setMyGroupPlansLoading] = useState(true);
  const [myGroupPlansError, setMyGroupPlansError] = useState<string|null>(null);

  // Fetch group plans where user is a member
  const fetchMyGroupPlans = async () => {
    setMyGroupPlansLoading(true);
    setMyGroupPlansError(null);
    try {
      const token = localStorage.getItem('access_token');
      const res = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/group-savings/group-plans/my`, {
        headers: token ? { 'Authorization': `Bearer ${token}` } : {}
      });
      const data = await res.json();
      console.log('[SavingsPlans] /api/group-savings/group-plans/my response:', data);
      setMyGroupPlans(Array.isArray(data) ? data : []);
    } catch (err) {
      console.error('[SavingsPlans] Error fetching /api/group-savings/group-plans/my:', err);
      setMyGroupPlansError('Failed to load your group plans');
      setMyGroupPlans([]);
    }
    setMyGroupPlansLoading(false);
  };

  React.useEffect(() => {
    fetchMyGroupPlans();
  }, []);
import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  PiggyBank, 
  TrendingUp, 
  CalendarClock, 
  Users, 
  Target, 
  Wallet, 
  DollarSign,
  Clock,
  Sparkles,
  UserRound,
  Plus,
  Mail,
  Calendar,
  UserPlus,
  Bell,
  X
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { StatCard } from "@/components/ui/stat-card";
import { SavingsGoalModal } from "@/components/savings/SavingsGoalModal";

export default function SavingsPlans() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [savingAmount, setSavingAmount] = useState("");
  const [selectedPlan, setSelectedPlan] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<string>("individual");
  const [createGroupDialogOpen, setCreateGroupDialogOpen] = useState(false);
  const [showSavingsGoalModal, setShowSavingsGoalModal] = useState(false);
  const [invitedMembers, setInvitedMembers] = useState<string[]>([]);
  const [memberEmail, setMemberEmail] = useState("");
  
  // Form state for creating a new group
  const [newGroup, setNewGroup] = useState({
    name: "",
    targetAmount: "",
    frequency: "monthly", // new: frequency of contribution
    amountPerContribution: "",
    deadline: "", // new: calculated end date
    contributionType: "equal",
    isPublic: false
  });
  
  // Plans state
  const [individualPlans, setIndividualPlans] = useState<any[]>([]);
  const [groupPlans, setGroupPlans] = useState<any[]>([]);
  const [targetPlans, setTargetPlans] = useState<any[]>([]);
  const [plansLoading, setPlansLoading] = useState(true);
  const [plansError, setPlansError] = useState<string|null>(null);

  React.useEffect(() => {
    async function fetchPlans() {
      setPlansLoading(true);
      setPlansError(null);
      try {
        const token = localStorage.getItem('access_token');
        const res = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/savings/plans`, {
          headers: token ? { 'Authorization': `Bearer ${token}` } : {}
        });
        const data = await res.json();
        console.log('[SavingsPlans] /api/savings/plans response:', data);
        setIndividualPlans(Array.isArray(data.individual) ? data.individual : []);
        setGroupPlans(Array.isArray(data.group) ? data.group : []);
        setTargetPlans(Array.isArray(data.target) ? data.target : []);
      } catch (err) {
        console.error('[SavingsPlans] Error fetching /api/savings/plans:', err);
        setPlansError('Failed to load plans');
        setIndividualPlans([]);
        setGroupPlans([]);
        setTargetPlans([]);
      }
      setPlansLoading(false);
    }
    fetchPlans();
  }, []);

  const handleDialogOpen = (plan: any) => {
    setSelectedPlan(plan);
    setSavingAmount(plan.minimumAmount);
  };
  
  const handleJoinPlan = () => {
    if (!selectedPlan) return;
    
    const amount = parseFloat(savingAmount);
    const minAmount = parseFloat(selectedPlan.minimumAmount);
    
    if (isNaN(amount) || amount < minAmount) {
      toast({
        title: "Invalid amount",
        description: `Minimum amount is ₦${minAmount.toLocaleString()}`,
        variant: "destructive"
      });
      return;
    }
    
    setIsLoading(true);
    
    // Mock API call
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "Success",
        description: `You have successfully joined the ${selectedPlan.name} plan`,
      });
      setSelectedPlan(null);
    }, 1500);
  };
  
  // User's active plans from backend
  const [userPlans, setUserPlans] = useState<any[]>([]);
  const [userPlansLoading, setUserPlansLoading] = useState(true);
  const [userPlansError, setUserPlansError] = useState<string|null>(null);

  React.useEffect(() => {
    async function fetchUserPlans() {
      setUserPlansLoading(true);
      setUserPlansError(null);
      try {
        const token = localStorage.getItem('access_token');
        const res = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/savings/my`, {
          headers: token ? { 'Authorization': `Bearer ${token}` } : {}
        });
        const data = await res.json();
        console.log('[SavingsPlans] /api/savings/my response:', data);
        // Map backend startDate/endDate to frontend camelCase for consistency
        const plans = Array.isArray(data) ? data : (Array.isArray(data.plans) ? data.plans : []);
        const mapped = plans.map((plan: any) => ({
          ...plan,
          startDate: plan.startDate || plan.createdAt || '',
          endDate: plan.endDate || plan.targetDate || '',
        }));
        setUserPlans(mapped);
      } catch (err) {
        console.error('[SavingsPlans] Error fetching /api/savings/my:', err);
        setUserPlansError('Failed to load your plans');
        setUserPlans([]);
      }
      setUserPlansLoading(false);
    }
    fetchUserPlans();
  }, []);

  // Add member to the invited list
  const addMember = () => {
    if (!memberEmail) return;
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(memberEmail)) {
      toast({
        title: "Invalid email",
        description: "Please enter a valid email address",
        variant: "destructive"
      });
      return;
    }

    if (invitedMembers.includes(memberEmail)) {
      toast({
        title: "Duplicate email",
        description: "This member is already invited",
        variant: "destructive"
      });
      return;
    }

    setInvitedMembers([...invitedMembers, memberEmail]);
    setMemberEmail("");
  };

  // Remove member from the invited list
  const removeMember = (email: string) => {
    setInvitedMembers(invitedMembers.filter(e => e !== email));
  };

  // Helper to calculate deadline based on target, frequency, and amount per contribution
  const calculateDeadline = (targetAmount: string, frequency: string, amountPerContribution: string) => {
    const target = parseFloat(targetAmount);
    const amount = parseFloat(amountPerContribution);
    if (!target || !amount || amount <= 0) return "";
    let periods = Math.ceil(target / amount);
    let now = new Date();
    let end = new Date(now);
    switch (frequency) {
      case "daily":
        end.setDate(now.getDate() + periods);
        break;
      case "weekly":
        end.setDate(now.getDate() + periods * 7);
        break;
      case "monthly":
      default:
        end.setMonth(now.getMonth() + periods);
        break;
    }
    return end.toISOString().slice(0, 10); // yyyy-mm-dd
  };

  // Handle form change
  const handleGroupFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewGroup(prev => {
      const updated = { ...prev, [name]: value };
      // If relevant fields change, recalculate deadline
      if (["targetAmount", "frequency", "amountPerContribution"].includes(name)) {
        updated.deadline = calculateDeadline(
          name === "targetAmount" ? value : updated.targetAmount,
          name === "frequency" ? value : updated.frequency,
          name === "amountPerContribution" ? value : updated.amountPerContribution
        );
      }
      return updated;
    });
  };

  // Handle checkbox change
  const handleCheckboxChange = (checked: boolean) => {
    setNewGroup(prev => ({
      ...prev,
      isPublic: checked
    }));
  };

  // Create new group savings plan
  const handleCreateGroup = async () => {
    // Validate form
    if (!newGroup.name) {
      toast({
        title: "Group name required",
        description: "Please enter a name for your savings group",
        variant: "destructive"
      });
      return;
    }

    if (!newGroup.targetAmount || parseFloat(newGroup.targetAmount) <= 0) {
      toast({
        title: "Invalid target amount",
        description: "Please enter a valid target amount",
        variant: "destructive"
      });
      return;
    }

    if (invitedMembers.length === 0) {
      toast({
        title: "No members invited",
        description: "Please invite at least one member to your group",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    try {
      const token = localStorage.getItem('access_token');
      const res = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/group-savings/group-plan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { 'Authorization': `Bearer ${token}` } : {})
        },
        body: JSON.stringify({
          title: newGroup.name,
          depositFrequency: newGroup.frequency,
          depositAmount: parseFloat(newGroup.amountPerContribution),
          targetDate: newGroup.deadline,
          targetAmount: parseFloat(newGroup.targetAmount),
          isPublic: newGroup.isPublic,
          pendingInvites: invitedMembers
        })
      });
      const data = await res.json();
      setIsLoading(false);
      if (!res.ok) {
        toast({
          title: "Error",
          description: data.error || 'Failed to create group',
          variant: "destructive"
        });
        return;
      }
      toast({
        title: "Group created successfully!",
        description: `Your group "${newGroup.name}" has been created and invitations sent`,
      });
      // Reset form
      setNewGroup({
        name: "",
        targetAmount: "",
        frequency: "monthly",
        amountPerContribution: "",
        deadline: "",
        contributionType: "equal",
        isPublic: false
      });
      setInvitedMembers([]);
      setCreateGroupDialogOpen(false);
      setActiveTab("group");
      // Optionally, refresh group plans here
      // Auto-refresh group plans and user plans after group creation
      try {
        setPlansLoading(true);
        setUserPlansLoading(true);
        // Refresh available group plans
        const token = localStorage.getItem('access_token');
        const plansRes = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/savings/plans`, {
          headers: token ? { 'Authorization': `Bearer ${token}` } : {}
        });
        const plansData = await plansRes.json();
        setIndividualPlans(Array.isArray(plansData.individual) ? plansData.individual : []);
        setGroupPlans(Array.isArray(plansData.group) ? plansData.group : []);
        setTargetPlans(Array.isArray(plansData.target) ? plansData.target : []);
        setPlansLoading(false);
        // Refresh user's active plans
        const userPlansRes = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/savings/my`, {
          headers: token ? { 'Authorization': `Bearer ${token}` } : {}
        });
        const userPlansData = await userPlansRes.json();
        const plans = Array.isArray(userPlansData) ? userPlansData : (Array.isArray(userPlansData.plans) ? userPlansData.plans : []);
        const mapped = plans.map((plan) => ({
          ...plan,
          startDate: plan.startDate || plan.createdAt || '',
          endDate: plan.endDate || plan.targetDate || '',
        }));
        setUserPlans(mapped);
        setUserPlansLoading(false);
        // Refresh group plans where user is a member
        await fetchMyGroupPlans();
      } catch (refreshErr) {
        // Silently fail refresh, user can manually reload if needed
        setPlansLoading(false);
        setUserPlansLoading(false);
      }
    } catch (err) {
      setIsLoading(false);
      toast({
        title: "Error",
        description: 'Failed to create group',
        variant: "destructive"
      });
    }
  };


  // For Active Plans section: merge individual and group plans
  const allActivePlans = [...userPlans, ...myGroupPlans];

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold mb-6">Savings Plans</h1>
      
      {/* Savings Overview Stats - dynamic from allActivePlans */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {/* Total Savings */}
        <StatCard
          title="Total Savings"
          value={(userPlansLoading || myGroupPlansLoading) ? '...' : `₦${allActivePlans.reduce((sum, p) => sum + (parseFloat(p.currentAmount || p.current || 0)), 0).toLocaleString()}`}
          description="Across all plans"
          icon={<PiggyBank className="h-5 w-5" />}
        />
        {/* Interest Earned */}
        <StatCard
          title="Interest Earned"
          value={(userPlansLoading || myGroupPlansLoading) ? '...' : `₦${allActivePlans.reduce((sum, p) => sum + (parseFloat(p.interestEarned || p.interest || 0)), 0).toLocaleString()}`}
          description="Lifetime earnings"
          icon={<TrendingUp className="h-5 w-5" />}
        />
        {/* Active Plans */}
        <StatCard
          title="Active Plans"
          value={(userPlansLoading || myGroupPlansLoading) ? '...' : allActivePlans.length}
          description="Currently saving"
          icon={<CalendarClock className="h-5 w-5" />}
        />
        {/* Group Savings */}
        <StatCard
          title="Group Savings"
          value={(userPlansLoading || myGroupPlansLoading) ? '...' : `₦${myGroupPlans.reduce((sum, p) => sum + (parseFloat(p.currentAmount || p.current || 0)), 0).toLocaleString()}`}
          description={myGroupPlansLoading ? '' : `With ${myGroupPlans.reduce((sum, p) => sum + (p.participants || 0), 0)} participants`}
          icon={<Users className="h-5 w-5" />}
        />
      </div>

      {userPlansLoading ? (
        <div className="mb-8 text-center py-8">Loading your active plans…</div>
      ) : userPlansError ? (
        <div className="mb-8 text-center py-8 text-red-500">{userPlansError}</div>
      ) : userPlans.length > 0 ? (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Your Active Plans</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
            {userPlans.map((plan) => (
              <Card key={plan.id || plan._id} className="kola-card border-primary/20 hover:border-primary/50 shadow-kola dark:from-background dark:to-accent/50 dark:hover:shadow-dark-glass">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-primary dark:text-primary-foreground">
                      {plan.planName || plan.name || plan.title}
                      {'participants' in plan && (
                        <span className="ml-2 text-xs bg-secondary/20 text-secondary-foreground px-2 py-1 rounded-full">
                          {plan.participants} Members
                        </span>
                      )}
                    </CardTitle>
                    <div className="bg-secondary/20 text-secondary-foreground text-xs px-3 py-1 rounded-full font-medium">
                      Active
                    </div>
                  </div>
                  <CardDescription>
                    Target: ₦{parseInt(plan.targetAmount || plan.target || '0').toLocaleString()}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="bg-accent/30 p-3 rounded-md grid grid-cols-2 gap-2">
                      <div>
                        <p className="text-sm text-muted-foreground">Current Savings</p>
                        <p className="font-medium text-secondary dark:text-secondary-foreground">₦{parseInt(plan.currentAmount || plan.current || '0').toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Progress</p>
                        <p className="font-medium">{plan.progress ?? (plan.targetAmount || plan.target ? Math.round(((plan.currentAmount || plan.current || 0) / (plan.targetAmount || plan.target || 1)) * 100) : 0)}%</p>
                      </div>
                    </div>
                    <div className="w-full bg-accent/50 rounded-full h-2.5 overflow-hidden dark:bg-accent/30">
                      <div 
                        className="bg-secondary h-2.5 rounded-full animate-pulse-light" 
                        style={{ width: `${plan.progress ?? (plan.targetAmount || plan.target ? Math.round(((plan.currentAmount || plan.current || 0) / (plan.targetAmount || plan.target || 1)) * 100) : 0)}%` }}
                      ></div>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-muted-foreground">Start Date:</span> 
                        <span className="text-foreground ml-1">{plan.startDate ? new Date(plan.startDate).toLocaleDateString() : '--'}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">End Date:</span> 
                        <span className="text-foreground ml-1">{plan.endDate ? new Date(plan.endDate).toLocaleDateString() : '--'}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex gap-2">
                  <Button variant="yellow" className="flex-1 group transition-all duration-300 hover:scale-105">
                    <span className="group-hover:scale-110 transition-transform duration-300">
                      <DollarSign className="mr-2 h-4 w-4" /> Top Up
                    </span>
                  </Button>
                  <Button variant="outline" className="flex-1 border-primary/30 hover:bg-accent/50 group transition-all duration-300">
                    <span className="group-hover:scale-110 transition-transform duration-300">
                      <Wallet className="mr-2 h-4 w-4" /> Details
                    </span>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      ) : null}
      
      <div className="mb-6">
        <Tabs 
          defaultValue="individual" 
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Available Plans</h2>
            <div className="flex gap-2 items-center">
              {activeTab === "group" && (
                <Button 
                  variant="yellow" 
                  className="flex items-center gap-2 mr-4" 
                  onClick={() => setCreateGroupDialogOpen(true)}
                >
                  <Plus className="h-4 w-4" /> Create Group
                </Button>
              )}
              {activeTab === "individual" && (
                <Button
                  variant="yellow"
                  className="flex items-center gap-2 mr-4"
                  onClick={() => setShowSavingsGoalModal(true)}
                >
                  <Plus className="h-4 w-4" /> Create Individual
                </Button>
              )}
              <TabsList>
                <TabsTrigger value="individual" className="flex items-center gap-1">
                  <UserRound className="h-4 w-4" /> Individual
                </TabsTrigger>
                <TabsTrigger value="group" className="flex items-center gap-1">
                  <Users className="h-4 w-4" /> Group
                </TabsTrigger>
                <TabsTrigger value="target" className="flex items-center gap-1">
                  <Target className="h-4 w-4" /> Target
                </TabsTrigger>
              </TabsList>
            </div>
          </div>
          
          <TabsContent value="individual" className="mt-0">
            {userPlansLoading ? (
              <div className="text-center py-8">Loading plans…</div>
            ) : userPlansError ? (
              <div className="text-center py-8 text-red-500">{userPlansError}</div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {userPlans.map((plan) => (
                  <PlanCard 
                    key={plan.id || plan._id} 
                    plan={plan} 
                    onJoin={() => handleDialogOpen(plan)} 
                  />
                ))}
              </div>
            )}
          </TabsContent>
          <TabsContent value="group" className="mt-0">
            {myGroupPlansLoading ? (
              <div className="text-center py-8">Loading plans…</div>
            ) : myGroupPlansError ? (
              <div className="text-center py-8 text-red-500">{myGroupPlansError}</div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {myGroupPlans.map((plan) => (
                  <PlanCard 
                    key={plan.id || plan._id} 
                    plan={plan} 
                    onJoin={() => handleDialogOpen(plan)} 
                  />
                ))}
              </div>
            )}
          </TabsContent>
          <TabsContent value="target" className="mt-0">
            {plansLoading ? (
              <div className="text-center py-8">Loading plans…</div>
            ) : plansError ? (
              <div className="text-center py-8 text-red-500">{plansError}</div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {targetPlans.map((plan) => (
                  <PlanCard 
                    key={plan.id || plan._id} 
                    plan={plan} 
                    onJoin={() => handleDialogOpen(plan)} 
                  />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
      
      {/* Join Plan Dialog */}
      <Dialog open={!!selectedPlan} onOpenChange={(open) => !open && setSelectedPlan(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Join {selectedPlan?.name}</DialogTitle>
            <DialogDescription>
              Start your savings journey with our {selectedPlan?.name} plan.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="saving-amount">How much would you like to save?</Label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                <Input
                  id="saving-amount"
                  type="number"
                  value={savingAmount}
                  onChange={(e) => setSavingAmount(e.target.value)}
                  placeholder={`Minimum ₦${selectedPlan?.minimumAmount}`}
                  className="pl-10"
                />
              </div>
              <p className="text-sm text-muted-foreground">
                Minimum amount: ₦{parseInt(selectedPlan?.minimumAmount || "0").toLocaleString()}
              </p>
            </div>
            
            <div className="bg-muted/50 p-4 rounded-md space-y-3">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <p className="text-sm">
                  Duration: <span className="font-medium">{selectedPlan?.durationDays} days</span>
                </p>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                <p className="text-sm">
                  Interest Rate: <span className="font-medium">{selectedPlan?.interestRate}%</span>
                </p>
              </div>
              {selectedPlan?.participants && (
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <p className="text-sm">
                    Participants: <span className="font-medium">{selectedPlan?.participants}</span>
                  </p>
                </div>
              )}
            </div>
          </div>
          <Button 
            onClick={handleJoinPlan} 
            disabled={isLoading} 
            variant="default"
            className="w-full transition-all duration-300 hover:scale-105"
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <span className="animate-spin mr-2">⟳</span> Processing...
              </span>
            ) : (
              <span className="flex items-center gap-2">
                <Sparkles className="h-4 w-4" /> Confirm and Join
              </span>
            )}
          </Button>
        </DialogContent>
      </Dialog>

      {/* Create Individual Savings Modal (reuses dashboard modal) */}
      <SavingsGoalModal 
        open={showSavingsGoalModal}
        onOpenChange={setShowSavingsGoalModal}
      />
      {/* Create Group Savings Dialog */}
      <Dialog open={createGroupDialogOpen} onOpenChange={setCreateGroupDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl flex items-center gap-2">
              <Users className="h-5 w-5 text-brand-blue" /> Create Group Savings
            </DialogTitle>
            <DialogDescription>
              Create a savings group and invite your family & friends to save together.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6 py-4">
            {/* Group Name */}
            <div className="space-y-2">
              <Label htmlFor="group-name">Group Name</Label>
              <Input
                id="group-name"
                name="name"
                value={newGroup.name}
                onChange={handleGroupFormChange}
                placeholder="Family Savings, Weekend Getaway, etc."
              />
            </div>
            {/* Target Amount, Frequency, Amount per Contribution, Deadline */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="target-amount">Target Amount (₦)</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                  <Input
                    id="target-amount"
                    name="targetAmount"
                    type="number"
                    value={newGroup.targetAmount}
                    onChange={handleGroupFormChange}
                    placeholder="100000"
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="frequency">Frequency</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                  <select
                    id="frequency"
                    name="frequency"
                    value={newGroup.frequency}
                    onChange={handleGroupFormChange}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 pl-10 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="amount-per-contribution">Amount per Contribution (₦)</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                  <Input
                    id="amount-per-contribution"
                    name="amountPerContribution"
                    type="number"
                    value={newGroup.amountPerContribution}
                    onChange={handleGroupFormChange}
                    placeholder="5000"
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="deadline">Deadline (calculated)</Label>
                <Input
                  id="deadline"
                  name="deadline"
                  type="date"
                  value={newGroup.deadline}
                  readOnly
                  className="bg-muted/50 cursor-not-allowed"
                />
              </div>
            </div>
            
            {/* Contribution Type */}
            <div className="space-y-2">
              <Label htmlFor="contribution-type">Contribution Type</Label>
              <div className="relative">
                <Users className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                <select 
                  id="contribution-type" 
                  name="contributionType"
                  value={newGroup.contributionType}
                  onChange={handleGroupFormChange}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 pl-10 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                >
                  <option value="equal">Equal Contributions</option>
                  <option value="flexible">Flexible Contributions</option>
                </select>
              </div>
              <p className="text-xs text-muted-foreground">
                {newGroup.contributionType === "equal" 
                  ? "All members contribute the same amount on a regular basis." 
                  : "Members can contribute different amounts based on their ability."}
              </p>
            </div>
            
            {/* Group Visibility */}
            <div className="flex items-start space-x-2">
              <Checkbox 
                id="is-public" 
                checked={newGroup.isPublic}
                onCheckedChange={handleCheckboxChange}
              />
              <div className="grid gap-1.5 leading-none">
                <Label htmlFor="is-public" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Make this group public
                </Label>
                <p className="text-xs text-muted-foreground">
                  Public groups can be discovered by other users on the platform.
                </p>
              </div>
            </div>
            
            {/* Invite Members */}
            <div className="space-y-4 bg-muted/40 p-4 rounded-md">
              <h3 className="font-medium text-base flex items-center gap-2">
                <UserPlus className="h-4 w-4" /> Invite Members
              </h3>
              
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Mail className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                  <Input
                    placeholder="<EMAIL>"
                    className="pl-10"
                    value={memberEmail}
                    onChange={(e) => setMemberEmail(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && addMember()}
                  />
                </div>
                <Button type="button" onClick={addMember} variant="outline">Add</Button>
              </div>
              
              {invitedMembers.length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium">Invited Members ({invitedMembers.length})</p>
                  <div className="bg-background p-2 rounded-md max-h-24 overflow-y-auto">
                    {invitedMembers.map(email => (
                      <div key={email} className="flex justify-between items-center py-1">
                        <span className="text-sm flex items-center gap-2">
                          <UserRound className="h-3 w-3 text-muted-foreground" /> {email}
                        </span>
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={() => removeMember(email)}>
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="flex items-start space-x-2 pt-2">
                <Bell className="h-4 w-4 text-muted-foreground mt-0.5" />
                <p className="text-xs text-muted-foreground">
                  Members will receive an email invitation to join your savings group.
                </p>
              </div>
            </div>
          </div>
          
          <Button 
            onClick={handleCreateGroup} 
            disabled={isLoading} 
            className="w-full transition-all duration-300 hover:scale-105"
            variant="yellow"
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <span className="animate-spin mr-2">⟳</span> Creating Group...
              </span>
            ) : (
              <span className="flex items-center gap-2">
                <Users className="h-4 w-4" /> Create Group Savings
              </span>
            )}
          </Button>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Plan Card Component for displaying individual plans
function PlanCard({ plan, onJoin }: { plan: any, onJoin: () => void }) {
  return (
    <Card 
      key={plan.id} 
      className="h-full flex flex-col blue-card hover:transform hover:scale-[1.03] transition-all duration-300"
    >
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle className="text-white">{plan.name}</CardTitle>
          {plan.tag && (
            <div className="bg-brand-yellow/20 text-yellow-300 text-xs px-3 py-1 rounded-full font-medium animate-pulse-slow">
              {plan.tag}
            </div>
          )}
        </div>
        <CardDescription className="text-white/80">{plan.description}</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="space-y-4">
          <div className="space-y-1">
            <p className="text-sm font-medium text-white">Minimum Amount</p>
            <p className="text-2xl font-bold text-yellow-300">₦{parseInt(plan.minimumAmount).toLocaleString()}</p>
          </div>
          
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <p className="text-white/70">Duration</p>
              <p className="text-white">{plan.durationDays} days</p>
            </div>
            <div>
              <p className="text-white/70">Interest Rate</p>
              <p className="text-white">{plan.interestRate}%</p>
            </div>
            {plan.participants && (
              <div className="col-span-2">
                <p className="text-white/70">Participants</p>
                <p className="text-white">{plan.participants}</p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          variant="yellow" 
          className="w-full group transition-all duration-300 hover:scale-105"
          onClick={onJoin}
        >
          <span className="group-hover:translate-x-1 transition-transform duration-300 flex items-center gap-2">
            <PiggyBank className="h-4 w-4" /> Join Plan
          </span>
        </Button>
      </CardFooter>
    </Card>
  );
}
