// backend/middleware/authMiddleware.js
const jwt = require('jsonwebtoken');

// Replace with your JWT secret or use process.env.JWT_SECRET
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret';

function authMiddleware(req, res, next) {
  const authHeader = req.headers['authorization'];
  console.log('[authMiddleware] Authorization header:', authHeader);
  const token = authHeader && authHeader.split(' ')[1];
  if (!token) {
    console.error('[authMiddleware] No token provided');
    return res.status(401).json({ error: 'No token provided, authorization denied.' });
  }
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    console.log('[authMiddleware] Decoded JWT:', decoded);
    req.user = decoded;
    next();
  } catch (err) {
    console.error('[authMiddleware] JWT verification error:', err.message);
    return res.status(401).json({ error: 'Invalid or expired token.' });
  }
}

module.exports = authMiddleware;
