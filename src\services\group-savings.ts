
import type {
  GroupSavingsPlan,
  GroupMember,
  RotationalGroup,
  GroupTransaction,
  CreateGroupSavingsInput,
  CreateRotationalGroupInput
} from '@/types/group-savings';

const BACKEND_URL = import.meta.env.VITE_BACKEND_URL;

export const GroupSavingsService = {

  getGroupSavingsPlans: async (): Promise<{ data: GroupSavingsPlan[] | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/group-savings`);
      if (!res.ok) throw new Error('Failed to fetch group savings plans');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching group savings plans:', error);
      return { data: null, error };
    }
  },

  getRotationalGroups: async (): Promise<{ data: RotationalGroup[] | null; error: any }> => {
    try {
      // Use the new user-specific route
      // Try both 'token' and 'access_token' for compatibility
      let token = localStorage.getItem('token');
      if (!token) token = localStorage.getItem('access_token');
      console.log('[getRotationalGroups] Fetching /api/rotational-group-savings/my with token:', token);
      const res = await fetch(`${BACKEND_URL}/api/rotational-group-savings/my`, {
        headers: token ? { 'Authorization': `Bearer ${token}` } : {}
      });
      if (!res.ok) {
        const errorText = await res.text();
        console.error('[getRotationalGroups] Response not ok:', res.status, errorText);
        throw new Error('Failed to fetch rotational groups');
      }
      const data = await res.json();
      console.log('[getRotationalGroups] Data:', data);
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching rotational groups:', error);
      return { data: null, error };
    }
  },

  getGroupMembers: async (groupId: string): Promise<{ data: GroupMember[] | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/group-savings/${groupId}/members`);
      if (!res.ok) throw new Error('Failed to fetch group members');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching group members:', error);
      return { data: null, error };
    }
  },

  getGroupTransactions: async (groupId: string): Promise<{ data: GroupTransaction[] | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/group-savings/${groupId}/transactions`);
      if (!res.ok) throw new Error('Failed to fetch group transactions');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching group transactions:', error);
      return { data: null, error };
    }
  },

  createGroupSavingsPlan: async (planData: CreateGroupSavingsInput): Promise<{ data: GroupSavingsPlan | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/group-savings`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(planData)
      });
      if (!res.ok) throw new Error('Failed to create group savings plan');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error creating group savings plan:', error);
      return { data: null, error };
    }
  },

  createRotationalGroup: async (groupData: CreateRotationalGroupInput & { createdBy?: string; nextPayoutDate?: string }): Promise<{ data: RotationalGroup | null; error: any }> => {
    try {
      // Map frontend fields to backend fields
      const mappedData = {
        name: groupData.name,
        description: groupData.description,
        amountPerInterval: groupData.contribution_amount,
        intervalType: groupData.frequency,
        createdBy: groupData.createdBy, // must be provided by UI/auth context
        nextPayoutDate: groupData.nextPayoutDate // must be provided by UI
      };
      if (!mappedData.createdBy || !mappedData.nextPayoutDate) {
        throw new Error('createdBy and nextPayoutDate are required');
      }
      const res = await fetch(`${BACKEND_URL}/api/rotational-group-savings/rgs`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(mappedData)
      });
      if (!res.ok) throw new Error('Failed to create rotational group');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error creating rotational group:', error);
      return { data: null, error };
    }
  },
  joinRotationalGroup: async (groupId: string, userId: string): Promise<{ data: any | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/rotational-group-savings/${groupId}/join`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      });
      let data = null;
      let errorMsg = null;
      try {
        data = await res.json();
      } catch (e) {
        // ignore JSON parse error
      }
      if (!res.ok) {
        errorMsg = (data && data.error) || (data && data.message) || 'Failed to join group';
        throw new Error(errorMsg);
      }
      return { data, error: null };
    } catch (error) {
      console.error('Error joining rotational group:', error);
      return { data: null, error };
    }
  },

  joinGroupSavingsPlan: async (groupId: string): Promise<{ data: any | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/group-savings/${groupId}/join`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      if (!res.ok) throw new Error('Failed to join group savings plan');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error joining group savings plan:', error);
      return { data: null, error };
    }
  },

  addMemberToGroup: async (groupId: string, email: string, contributionAmount: number, role: 'admin' | 'member' = 'member'): Promise<{ data: any | null; error: any }> => {
    try {
      const res = await fetch(`${BACKEND_URL}/api/group-savings/${groupId}/add-member`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, contributionAmount, role })
      });
      if (!res.ok) throw new Error('Failed to add member to group');
      const data = await res.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error adding member to group:', error);
      return { data: null, error };
    }
  }
};
