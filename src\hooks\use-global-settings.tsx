// Global Settings Hook for ASUSU by Koja
// Custom React hook for managing global settings state and UI updates

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import globalSettingsService from '@/services/global-settings.service';
import {
  GlobalSettings,
  PublicGlobalSettings,
  UseGlobalSettingsReturn,
  UseAdminSettingsReturn,
  AppConfigUpdate,
  ColorThemeUpdate,
  BrandingUpdate,
  APIKeysUpdate,
  ContentUpdate,
  SystemConfigUpdate,
  ContactUpdate
} from '@/types/global-settings';

// Hook for public settings (no authentication required)
export function useGlobalSettings(): UseGlobalSettingsReturn {
  const [settings, setSettings] = useState<PublicGlobalSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const refreshSettings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await globalSettingsService.getPublicSettings();
      setSettings(data);
      
      // Apply theme colors to UI
      if (data.colors) {
        globalSettingsService.applyThemeColors(data.colors);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch settings';
      setError(errorMessage);
      console.error('Error fetching public settings:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    refreshSettings();
  }, [refreshSettings]);

  return {
    settings,
    loading,
    error,
    refreshSettings
  };
}

// Hook for admin settings (authentication required)
export function useAdminSettings(): UseAdminSettingsReturn {
  const [adminSettings, setAdminSettings] = useState<GlobalSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const refreshAdminSettings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await globalSettingsService.getAdminSettings();
      setAdminSettings(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch admin settings';
      setError(errorMessage);
      console.error('Error fetching admin settings:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const updateAppConfig = useCallback(async (data: AppConfigUpdate): Promise<boolean> => {
    try {
      const updatedSettings = await globalSettingsService.updateAppConfig(data);
      setAdminSettings(updatedSettings);
      
      toast({
        title: "Success",
        description: "App configuration updated successfully",
        variant: "default"
      });
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update app configuration';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  const updateColors = useCallback(async (data: ColorThemeUpdate): Promise<boolean> => {
    try {
      const updatedSettings = await globalSettingsService.updateColors(data);
      setAdminSettings(updatedSettings);
      
      // Apply new colors to UI immediately
      globalSettingsService.applyThemeColors(data);
      
      toast({
        title: "Success",
        description: "Color theme updated successfully",
        variant: "default"
      });
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update color theme';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  const updateBranding = useCallback(async (data: BrandingUpdate): Promise<boolean> => {
    try {
      const updatedSettings = await globalSettingsService.updateBranding(data);
      setAdminSettings(updatedSettings);
      
      toast({
        title: "Success",
        description: "Branding updated successfully",
        variant: "default"
      });
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update branding';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  const updateAPIKeys = useCallback(async (data: APIKeysUpdate): Promise<boolean> => {
    try {
      const updatedSettings = await globalSettingsService.updateAPIKeys(data);
      setAdminSettings(updatedSettings);
      
      toast({
        title: "Success",
        description: "API keys updated successfully",
        variant: "default"
      });
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update API keys';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  const updateContent = useCallback(async (data: ContentUpdate): Promise<boolean> => {
    try {
      const updatedSettings = await globalSettingsService.updateContent(data);
      setAdminSettings(updatedSettings);
      
      toast({
        title: "Success",
        description: "Content updated successfully",
        variant: "default"
      });
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update content';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  const updateSystem = useCallback(async (data: SystemConfigUpdate): Promise<boolean> => {
    try {
      const updatedSettings = await globalSettingsService.updateSystem(data);
      setAdminSettings(updatedSettings);
      
      toast({
        title: "Success",
        description: "System configuration updated successfully",
        variant: "default"
      });
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update system configuration';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  const updateContact = useCallback(async (data: ContactUpdate): Promise<boolean> => {
    try {
      const updatedSettings = await globalSettingsService.updateContact(data);
      setAdminSettings(updatedSettings);
      
      toast({
        title: "Success",
        description: "Contact information updated successfully",
        variant: "default"
      });
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update contact information';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  useEffect(() => {
    refreshAdminSettings();
  }, [refreshAdminSettings]);

  return {
    adminSettings,
    loading,
    error,
    refreshAdminSettings,
    updateAppConfig,
    updateColors,
    updateBranding,
    updateAPIKeys,
    updateContent,
    updateSystem,
    updateContact
  };
}
