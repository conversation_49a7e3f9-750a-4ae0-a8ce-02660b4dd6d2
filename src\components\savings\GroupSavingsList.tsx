
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  Users, 
  Calendar, 
  DollarSign, 
  Target,
  Eye,
  UserPlus,
  TrendingUp,
  Clock
} from 'lucide-react';
import { useGroupSavingsPlans, useJoinGroupSavingsPlan } from '@/hooks/use-group-savings';
import { GroupSavingsPlan } from '@/types/group-savings';
import { toast } from 'sonner';

interface GroupSavingsListProps {
  onCreateGroup?: () => void;
  onViewDetails?: (plan: GroupSavingsPlan) => void;
}

export default function GroupSavingsList({ 
  onCreateGroup, 
  onViewDetails 
}: GroupSavingsListProps) {
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<GroupSavingsPlan | null>(null);
  
  const { data: plans = [], isLoading } = useGroupSavingsPlans();
  const joinGroupMutation = useJoinGroupSavingsPlan();

  const handleJoinGroup = async (planId: string) => {
    try {
      await joinGroupMutation.mutateAsync(planId);
    } catch (error) {
      console.error('Error joining group:', error);
    }
  };

  const viewPlanDetails = (plan: GroupSavingsPlan) => {
    setSelectedPlan(plan);
    setShowDetailsModal(true);
    onViewDetails?.(plan);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'recruiting': return 'bg-yellow-500';
      case 'active': return 'bg-green-500';
      case 'completed': return 'bg-blue-500';
      case 'pending': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const calculateProgress = (plan: GroupSavingsPlan) => {
    if (!plan.target_amount) return 0;
    const totalContributed = plan.current_members * plan.contribution_amount;
    return (totalContributed / plan.target_amount) * 100;
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'phone': return '📱';
      case 'car': return '🚗';
      case 'electronics': return '💻';
      case 'grocery': return '🛒';
      default: return '💰';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Plan Details Modal */}
      <Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              {selectedPlan?.name}
            </DialogTitle>
          </DialogHeader>
          {selectedPlan && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <span className="text-2xl">{getCategoryIcon(selectedPlan.category)}</span>
                <Badge className={getStatusColor(selectedPlan.status)}>
                  {selectedPlan.status.charAt(0).toUpperCase() + selectedPlan.status.slice(1)}
                </Badge>
              </div>
              
              <p className="text-muted-foreground">{selectedPlan.description}</p>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-muted-foreground" />
                  <span>₦{selectedPlan.target_amount.toLocaleString()}</span>
                </div>
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span>₦{selectedPlan.contribution_amount.toLocaleString()}/person</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span>{selectedPlan.current_members}/{selectedPlan.max_members} members</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>{new Date(selectedPlan.end_date).toLocaleDateString()}</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{Math.round(calculateProgress(selectedPlan))}%</span>
                </div>
                <Progress value={calculateProgress(selectedPlan)} className="h-2" />
              </div>

              {selectedPlan.interest_rate && selectedPlan.interest_rate > 0 && (
                <div className="bg-green-50 p-3 rounded-lg">
                  <div className="flex items-center gap-2 text-sm">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span className="text-green-800">
                      Earn {selectedPlan.interest_rate}% interest on your contributions
                    </span>
                  </div>
                </div>
              )}

              {selectedPlan.status === 'recruiting' && (
                <Button
                  onClick={() => handleJoinGroup(selectedPlan.id)}
                  disabled={joinGroupMutation.isPending}
                  className="w-full"
                >
                  {joinGroupMutation.isPending ? 'Joining...' : 'Join This Group'}
                </Button>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Group Savings Plans</h2>
          <p className="text-muted-foreground">Join or create group savings for specific goals</p>
        </div>
        <Button onClick={onCreateGroup} className="flex items-center gap-2">
          <UserPlus className="h-4 w-4" />
          Create Group
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {plans.map((plan) => (
          <Card key={plan.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">{getCategoryIcon(plan.category)}</span>
                  <CardTitle className="text-lg">{plan.name}</CardTitle>
                </div>
                <Badge className={getStatusColor(plan.status)}>
                  {plan.status.charAt(0).toUpperCase() + plan.status.slice(1)}
                </Badge>
              </div>
              <CardDescription className="line-clamp-2">{plan.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-muted-foreground" />
                  <span>₦{plan.target_amount.toLocaleString()}</span>
                </div>
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span>₦{plan.contribution_amount.toLocaleString()}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span>{plan.current_members}/{plan.max_members}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>{new Date(plan.end_date).toLocaleDateString()}</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{Math.round(calculateProgress(plan))}%</span>
                </div>
                <Progress value={calculateProgress(plan)} className="h-2" />
              </div>

              {plan.interest_rate && plan.interest_rate > 0 && (
                <div className="bg-green-50 p-2 rounded text-sm">
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-3 w-3 text-green-600" />
                    <span className="text-green-800">{plan.interest_rate}% interest</span>
                  </div>
                </div>
              )}

              <div className="flex gap-2">
                {plan.status === 'recruiting' && (
                  <Button
                    size="sm"
                    onClick={() => handleJoinGroup(plan.id)}
                    disabled={joinGroupMutation.isPending}
                    className="flex-1"
                  >
                    <UserPlus className="h-4 w-4 mr-1" />
                    Join
                  </Button>
                )}
                
                {/* View Details button removed as per request */}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {plans.length === 0 && (
        <div className="text-center py-12">
          <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No group savings plans yet</h3>
          <p className="text-muted-foreground mb-4">
            Create your first group savings plan or join an existing one
          </p>
          <Button onClick={onCreateGroup}>
            <UserPlus className="h-4 w-4 mr-2" />
            Create Group Savings Plan
          </Button>
        </div>
      )}
    </div>
  );
}
