
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    font-family: 'Roboto', sans-serif;
  }

  html {
    font-family: 'Roboto', sans-serif;
  }

  body {
    font-family: 'Roboto', sans-serif;
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 233 82% 40%; /* #1231B8 - Koja blue as primary */
    --primary-foreground: 0 0% 100%;

    --secondary: 52 99% 53%; /* #FDE314 - Ko<PERSON> yellow as secondary */
    --secondary-foreground: 240 10% 3.9%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 233 100% 95%; /* Light Koja blue as accent */
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 233 82% 40%; /* Koja blue as ring */

    --radius: 0.75rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 233 82% 40%;
    --primary-foreground: 0 0% 98%;

    --secondary: 52 99% 53%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 233 100% 20%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 233 82% 40%;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Roboto', sans-serif;
    font-weight: 700;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  }

  html {
    @apply scroll-smooth;
  }
}

@layer utilities {
  .glass-card {
    @apply backdrop-blur-md bg-white/70 border border-white/20 shadow-glass dark:bg-black/40 dark:border-white/10 dark:shadow-dark-glass;
  }

  .text-balance {
    text-wrap: balance;
  }

  .dark-transition {
    @apply transition-colors duration-300;
  }

  .clickable-link {
    @apply cursor-pointer relative z-10 inline-block;
    pointer-events: auto !important;
  }
}

@layer components {
  .nav-link {
    @apply relative px-4 py-2 flex items-center gap-2 rounded-md transition-all duration-300 hover:bg-primary/10;
  }
  
  .nav-link.active {
    @apply bg-primary/15 text-primary;
  }

  .dashboard-card {
    @apply rounded-xl p-6 border border-border bg-card shadow-sm transition-all duration-300 hover:shadow-kola dark:hover:shadow-dark-glass;
  }
  
  .stat-card {
    @apply rounded-xl p-5 border border-border bg-card text-card-foreground flex flex-col hover:shadow-kola transition-all duration-300 dark:hover:shadow-dark-glass;
  }
  
  .btn-brand {
    @apply bg-brand-blue text-white hover:bg-brand-blue/90 transition-all;
  }
  
  .btn-accent {
    @apply bg-brand-yellow text-foreground hover:bg-brand-yellow/90 transition-all;
  }

  .kola-card {
    @apply rounded-xl border border-border bg-gradient-to-br from-white to-brand-lightBlue shadow-kola transition-all duration-300 hover:shadow-glass-strong hover:-translate-y-1 dark:from-gray-900 dark:to-gray-800;
  }
  
  .asusu-gradient {
    @apply bg-gradient-to-br from-brand-blue to-brand-deepBlue text-white;
  }
  
  .unica-title {
    @apply font-unica-one tracking-wider uppercase;
  }
  
  .currency-symbol {
    @apply font-medium;
  }
}
