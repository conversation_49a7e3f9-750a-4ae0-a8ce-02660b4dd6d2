
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    font-family: 'Roboto', sans-serif;
  }

  html {
    font-family: 'Roboto', sans-serif;
  }

  body {
    font-family: 'Roboto', sans-serif;
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 142 76% 36%; /* #16A34A - Green primary */
    --primary-foreground: 0 0% 100%;

    --secondary: 120 100% 25%; /* #008000 - Dark green secondary */
    --secondary-foreground: 0 0% 100%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 142 100% 95%; /* Light green accent */
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 142 76% 36%; /* Green ring */

    --radius: 0.75rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 142 76% 36%;
    --primary-foreground: 0 0% 98%;

    --secondary: 120 100% 25%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 142 100% 20%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 142 76% 36%;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Roboto', sans-serif;
    font-weight: 700;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  }

  html {
    @apply scroll-smooth;
  }
}

@layer utilities {
  .glass-card {
    @apply backdrop-blur-md bg-white/70 border border-white/20 shadow-lg dark:bg-black/40 dark:border-white/10 dark:shadow-xl;
  }

  .text-balance {
    text-wrap: balance;
  }

  .dark-transition {
    @apply transition-colors duration-300;
  }

  .clickable-link {
    @apply cursor-pointer relative z-10 inline-block;
    pointer-events: auto !important;
  }
}

@layer components {
  .nav-link {
    @apply relative px-4 py-2 flex items-center gap-2 rounded-[20px] transition-all duration-300 hover:bg-primary/10;
  }

  .nav-link.active {
    @apply bg-primary/15 text-primary;
  }

  .dashboard-card {
    @apply rounded-[20px] p-6 border border-border bg-card shadow-lg transition-all duration-500 hover:shadow-2xl hover:-translate-y-2 hover:scale-[1.02] dark:hover:shadow-xl;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .dashboard-card:hover {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(22, 163, 74, 0.1);
  }

  .stat-card {
    @apply rounded-[20px] p-5 border border-border bg-card text-card-foreground flex flex-col transition-all duration-500 hover:-translate-y-3 hover:scale-[1.05];
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    background: linear-gradient(145deg, hsl(var(--card)), hsl(var(--card)));
    border: 1px solid rgba(22, 163, 74, 0.1);
  }

  .stat-card:hover {
    box-shadow: 0 25px 50px -12px rgba(22, 163, 74, 0.25), 0 0 0 1px rgba(22, 163, 74, 0.2);
    background: linear-gradient(145deg, hsl(var(--card)), rgba(22, 163, 74, 0.02));
  }

  .btn-brand {
    @apply bg-brand-green text-white hover:bg-brand-green/90 transition-all duration-300 rounded-[20px] shadow-lg hover:shadow-xl hover:-translate-y-1;
  }

  .btn-accent {
    @apply bg-brand-lightGreen text-foreground hover:bg-brand-lightGreen/90 transition-all duration-300 rounded-[20px] shadow-lg hover:shadow-xl hover:-translate-y-1;
  }

  .kola-card {
    @apply rounded-[20px] border border-border bg-gradient-to-br from-white to-brand-lightGreen transition-all duration-500 hover:-translate-y-2 hover:scale-[1.02] dark:from-gray-900 dark:to-gray-800;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .kola-card:hover {
    box-shadow: 0 25px 50px -12px rgba(22, 163, 74, 0.25), 0 0 0 1px rgba(22, 163, 74, 0.1);
  }

  .asusu-gradient {
    @apply bg-gradient-to-br from-brand-green to-brand-darkGreen text-white rounded-[20px];
  }

  .unica-title {
    @apply font-unica-one tracking-wider uppercase;
  }

  .currency-symbol {
    @apply font-medium;
  }

  /* 3D Button Effects */
  .btn-3d {
    @apply rounded-[20px] transition-all duration-300 transform-gpu;
    box-shadow: 0 4px 15px 0 rgba(22, 163, 74, 0.3), 0 0 0 1px rgba(22, 163, 74, 0.1);
    background: linear-gradient(145deg, hsl(var(--primary)), hsl(var(--primary)));
  }

  .btn-3d:hover {
    @apply -translate-y-2 scale-[1.05];
    box-shadow: 0 15px 35px 0 rgba(22, 163, 74, 0.4), 0 5px 15px 0 rgba(22, 163, 74, 0.2);
  }

  .btn-3d:active {
    @apply translate-y-0 scale-100;
    box-shadow: 0 5px 15px 0 rgba(22, 163, 74, 0.3);
  }

  /* 3D Card Effects */
  .card-3d {
    @apply rounded-[20px] transition-all duration-500 transform-gpu;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(22, 163, 74, 0.05);
    background: linear-gradient(145deg, hsl(var(--card)), rgba(255, 255, 255, 0.8));
  }

  .card-3d:hover {
    @apply -translate-y-3 scale-[1.02];
    box-shadow: 0 25px 50px -12px rgba(22, 163, 74, 0.25), 0 0 0 1px rgba(22, 163, 74, 0.1);
    background: linear-gradient(145deg, hsl(var(--card)), rgba(22, 163, 74, 0.02));
  }

  /* Modal 3D Effects */
  .modal-3d {
    @apply rounded-[20px] transition-all duration-500;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(22, 163, 74, 0.1);
    background: linear-gradient(145deg, hsl(var(--background)), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
  }
}

/* Modern animations for Revolut-inspired design */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-gradient {
  animation: gradient 8s ease infinite;
  background-size: 400% 400%;
}

.animate-slide-up {
  animation: slide-up 0.6s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modern glassmorphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Hover effects */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}
