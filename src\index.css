
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    font-family: 'Roboto', sans-serif;
  }

  html {
    font-family: 'Roboto', sans-serif;
  }

  body {
    font-family: 'Roboto', sans-serif;
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 142 76% 36%; /* #16A34A - Green primary */
    --primary-foreground: 0 0% 100%;

    --secondary: 120 100% 25%; /* #008000 - Dark green secondary */
    --secondary-foreground: 0 0% 100%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 142 100% 95%; /* Light green accent */
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 142 76% 36%; /* Green ring */

    --radius: 0.75rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 142 76% 36%;
    --primary-foreground: 0 0% 98%;

    --secondary: 120 100% 25%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 142 100% 20%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 142 76% 36%;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Roboto', sans-serif;
    font-weight: 700;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  }

  html {
    @apply scroll-smooth;
  }
}

@layer utilities {
  .glass-card {
    @apply backdrop-blur-md bg-white/70 border border-white/20 shadow-glass dark:bg-black/40 dark:border-white/10 dark:shadow-dark-glass;
  }

  .text-balance {
    text-wrap: balance;
  }

  .dark-transition {
    @apply transition-colors duration-300;
  }

  .clickable-link {
    @apply cursor-pointer relative z-10 inline-block;
    pointer-events: auto !important;
  }
}

@layer components {
  .nav-link {
    @apply relative px-4 py-2 flex items-center gap-2 rounded-md transition-all duration-300 hover:bg-primary/10;
  }
  
  .nav-link.active {
    @apply bg-primary/15 text-primary;
  }

  .dashboard-card {
    @apply rounded-xl p-6 border border-border bg-card shadow-sm transition-all duration-300 hover:shadow-kola dark:hover:shadow-dark-glass;
  }
  
  .stat-card {
    @apply rounded-xl p-5 border border-border bg-card text-card-foreground flex flex-col hover:shadow-kola transition-all duration-300 dark:hover:shadow-dark-glass;
  }
  
  .btn-brand {
    @apply bg-brand-green text-white hover:bg-brand-green/90 transition-all;
  }

  .btn-accent {
    @apply bg-brand-lightGreen text-foreground hover:bg-brand-lightGreen/90 transition-all;
  }

  .kola-card {
    @apply rounded-xl border border-border bg-gradient-to-br from-white to-brand-lightGreen shadow-green transition-all duration-300 hover:shadow-glass-strong hover:-translate-y-1 dark:from-gray-900 dark:to-gray-800;
  }

  .asusu-gradient {
    @apply bg-gradient-to-br from-brand-green to-brand-darkGreen text-white;
  }
  
  .unica-title {
    @apply font-unica-one tracking-wider uppercase;
  }
  
  .currency-symbol {
    @apply font-medium;
  }
}

/* Modern animations for Revolut-inspired design */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-gradient {
  animation: gradient 8s ease infinite;
  background-size: 400% 400%;
}

.animate-slide-up {
  animation: slide-up 0.6s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modern glassmorphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Hover effects */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}
