
import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { NotificationBadge } from "@/components/ui/notification-badge";
import { PhoneIcon, AtSign, MapPin, CreditCard, Shield, Bell } from "lucide-react";

export default function UserProfile() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  
  // Get user from backend API
  const [userData, setUserData] = useState<any>(null);

  useEffect(() => {
    const fetchProfile = async () => {
      const token = localStorage.getItem('access_token');
      console.log('[UserProfile] access_token from localStorage:', token);
      if (!token) {
        console.warn('[UserProfile] No access_token found in localStorage');
        return;
      }
      try {
        const res = await fetch('http://localhost:8080/api/user/profile', {
          headers: { 'Authorization': `Bearer ${token}` },
        });
        console.log('[UserProfile] GET /api/user/profile status:', res.status);
        if (!res.ok) {
          const errorText = await res.text();
          console.error('[UserProfile] Failed to fetch profile:', res.status, errorText);
          return;
        }
        const data = await res.json();
        console.log('[UserProfile] Profile data received:', data);
        setUserData(data);
      } catch (e) {
        console.error('[UserProfile] Error fetching profile:', e);
      }
    };
    fetchProfile();
  }, []);

  // Initialize form from userData
  const [profileForm, setProfileForm] = useState({
    fullName: "",
    email: "",
    phoneNumber: "",
    address: "",
  });

  useEffect(() => {
    if (userData) {
      setProfileForm({
        fullName: `${userData.profile?.first_name || userData.firstName || userData.first_name || ""} ${userData.profile?.last_name || userData.lastName || userData.last_name || ""}`.trim(),
        email: userData.email || "",
        phoneNumber: userData.profile?.phone || userData.phoneNumber || userData.phone || "",
        address: userData.profile?.address || userData.address || "",
      });
    }
  }, [userData]);
  
  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileForm(prev => ({ ...prev, [name]: value }));
  };
  
  const handleUpdateProfile = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Mock API call
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "Profile Updated",
        description: "Your profile information has been updated successfully.",
      });
    }, 1500);
  };

  return (
    <div className="container mx-auto max-w-4xl py-6">
      <h1 className="text-2xl font-bold mb-6">User Profile</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* User Info Card */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
              <CardDescription>
                Your personal account details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-center mb-6">
                  <div className="h-24 w-24 rounded-full bg-primary/10 flex items-center justify-center text-2xl font-bold text-primary">
                    {profileForm.fullName.split(' ').map(name => name[0]).join('')}
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    <PhoneIcon className="h-4 w-4 mt-1 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Phone Number</p>
                      <p>{profileForm.phoneNumber}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-2">
                    <AtSign className="h-4 w-4 mt-1 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Email</p>
                      <p>{profileForm.email}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 mt-1 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Address</p>
                      <p>{profileForm.address}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-2">
                    <CreditCard className="h-4 w-4 mt-1 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Account Number</p>
                      <p>{userData?.accountNumber || userData?.account_number || '-'}</p>
                    </div>
                  </div>
                </div>
                
                <div className="pt-4 border-t">
                  <div className="flex items-start gap-2">
                    <Shield className="h-4 w-4 mt-1 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">KYC Status</p>
                      <div className="flex items-center gap-2">
                        <span className={`text-${userData?.kycStatus === 'verified' ? 'green' : userData?.kycStatus === 'pending' ? 'yellow' : 'red'}-500 font-medium`}>
                          {userData?.kycStatus === 'verified' 
                            ? 'Verified' 
                            : userData?.kycStatus === 'pending' 
                              ? 'Pending Verification' 
                              : 'Not Verified'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" asChild>
                <a href="/kyc-verification">
                  {userData?.kycStatus === 'verified' 
                    ? 'View KYC Status' 
                    : 'Complete KYC Verification'}
                </a>
              </Button>
            </CardFooter>
          </Card>
          
          <Card className="mt-6">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Notifications</CardTitle>
                <NotificationBadge count={3} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-start gap-2">
                  <Bell className="h-4 w-4 mt-1 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Payment Confirmed</p>
                    <p className="text-sm text-muted-foreground">Your deposit of ₦10,000 has been confirmed</p>
                    <p className="text-xs text-muted-foreground mt-1">2 hours ago</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-2">
                  <Bell className="h-4 w-4 mt-1 text-muted-foreground" />
                  <div>
                    <p className="font-medium">KYC Update</p>
                    <p className="text-sm text-muted-foreground">Your KYC documents are being processed</p>
                    <p className="text-xs text-muted-foreground mt-1">1 day ago</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-2">
                  <Bell className="h-4 w-4 mt-1 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Welcome!</p>
                    <p className="text-sm text-muted-foreground">Welcome to Nigerian Daily Savings App</p>
                    <p className="text-xs text-muted-foreground mt-1">5 days ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="ghost" className="w-full">View All Notifications</Button>
            </CardFooter>
          </Card>
        </div>
        
        {/* Update Profile Form */}
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Update Profile</CardTitle>
              <CardDescription>
                Update your personal information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleUpdateProfile} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    name="fullName"
                    value={profileForm.fullName}
                    onChange={handleProfileChange}
                    placeholder="Enter your full name"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={profileForm.email}
                    onChange={handleProfileChange}
                    placeholder="Enter your email address"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    name="phoneNumber"
                    value={profileForm.phoneNumber}
                    onChange={handleProfileChange}
                    placeholder="Enter your phone number"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="address">Residential Address</Label>
                  <Input
                    id="address"
                    name="address"
                    value={profileForm.address}
                    onChange={handleProfileChange}
                    placeholder="Enter your address"
                  />
                </div>
                
                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={isLoading}
                >
                  {isLoading ? "Updating..." : "Update Profile"}
                </Button>
              </form>
            </CardContent>
          </Card>
          
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Manage your account security and password
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <Input
                    id="currentPassword"
                    type="password"
                    placeholder="Enter your current password"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input
                    id="newPassword"
                    type="password"
                    placeholder="Enter new password"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    placeholder="Confirm new password"
                  />
                </div>
                
                <Button className="w-full">
                  Change Password
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
