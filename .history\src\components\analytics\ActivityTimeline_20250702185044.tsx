
import React from "react";
import { Badge } from "@/components/ui/badge";
import { Calendar<PERSON>lock, PiggyBank, CreditCard, ArrowDownLeft, ArrowUpRight } from "lucide-react";

// Accepts data as prop, falls back to sample if not provided
type TimelineItem = {
  id?: string | number;
  type: string;
  amount: string | number;
  date: string | Date;
  description: string;
};
interface ActivityTimelineProps {
  data?: TimelineItem[];
}

// Sample activities removed. All data now comes from backend.
  {
    id: 5,
    type: "plan_matured",
    amount: "₦100,000",
    date: "May 18, 10:00 AM",
    description: "Education Savings Plan Matured",
  },
];

export function ActivityTimeline({ data }: ActivityTimelineProps) {
  // Helper function to get icon and badge color based on activity type
  const getActivityDetails = (type: string) => {
    switch (type) {
      case "deposit":
        return {
          icon: <ArrowDownLeft className="h-4 w-4" />,
          color: "bg-green-100 text-green-600",
          badge: "Deposit",
        };
      case "withdrawal":
        return {
          icon: <ArrowUpRight className="h-4 w-4" />,
          color: "bg-orange-100 text-orange-600",
          badge: "Withdrawal",
        };
      case "interest":
        return {
          icon: <CreditCard className="h-4 w-4" />,
          color: "bg-blue-100 text-blue-600",
          badge: "Interest",
        };
      case "plan_started":
        return {
          icon: <PiggyBank className="h-4 w-4" />,
          color: "bg-purple-100 text-purple-600",
          badge: "New Plan",
        };
      case "plan_matured":
        return {
          icon: <CalendarClock className="h-4 w-4" />,
          color: "bg-yellow-100 text-yellow-700",
          badge: "Plan Matured",
        };
      default:
        return {
          icon: <PiggyBank className="h-4 w-4" />,
          color: "bg-gray-100 text-gray-600",
          badge: "Activity",
        };
    }
  };

  const activities = data && data.length > 0 ? data : [];
  return (
    <div className="space-y-4">
      {activities.map((activity, idx) => {
        const { icon, color, badge } = getActivityDetails(activity.type);
        return (
          <div key={activity.id ?? idx} className="flex gap-3 pb-4 border-b last:border-0">
            <div className={`${color} p-2 rounded-full h-8 w-8 flex items-center justify-center shrink-0`}>
              {icon}
            </div>
            <div className="flex-1 space-y-1">
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="text-sm font-medium">{activity.description}</h4>
                  <p className="text-xs text-muted-foreground">{typeof activity.date === 'string' ? activity.date : new Date(activity.date).toLocaleString()}</p>
                </div>
                <Badge variant="outline" className={color}>
                  {badge}
                </Badge>
              </div>
              <p className="text-sm font-semibold">
                {activity.type === "withdrawal" ? "-" : ""}{typeof activity.amount === 'number' ? `₦${activity.amount.toLocaleString()}` : activity.amount}
              </p>
            </div>
          </div>
        );
      })}
    </div>
  );
}
