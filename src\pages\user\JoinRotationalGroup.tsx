
import { useParams, useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { useRotationalGroups, useJoinRotationalGroup } from '@/hooks/use-group-savings';
import { useAuth } from '@/hooks/use-auth';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  Users,
  DollarSign,
  Calendar,
  Clock,
  UserPlus,
  ArrowLeft,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function JoinRotationalGroupPage() {
  const { groupId } = useParams<{ groupId: string }>();
  const { user } = useAuth();
  const { data: groups = [] } = useRotationalGroups();
  const joinMutation = useJoinRotationalGroup();
  const navigate = useNavigate();
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Debug logs for group lookup
  // eslint-disable-next-line no-console
  console.log('JoinRotationalGroupPage groupId:', groupId);
  // eslint-disable-next-line no-console
  console.log('JoinRotationalGroupPage groups:', groups);
  const group = Array.isArray(groups) ? groups.find((g: any) => g._id === groupId || g.id === groupId) : null;
  if (group) {
    // eslint-disable-next-line no-console
    console.log('JoinRotationalGroupPage group:', group);
  } else {
    // eslint-disable-next-line no-console
    console.log('JoinRotationalGroupPage group NOT FOUND for groupId:', groupId);
  }

  useEffect(() => {
    if (!user && groupId) {
      // Store pending group join for after login
      localStorage.setItem('pendingJoinGroupId', groupId);
    }
    if (user && groupId && group) {
      // Optionally auto-join if not already a member
      const isMember = group.current_members?.some((m: any) => m.userId === user.id || m.id === user.id);
      if (!isMember) {
        joinMutation.mutate({ groupId, userId: user.id }, {
          onSuccess: () => {
            toast.success('You have joined the group!');
            localStorage.removeItem('pendingJoinGroupId');
            navigate(`/user/rotational-savings`);
          },
          onError: (error: any) => {
            toast.error(error?.message || 'Failed to join group');
            localStorage.removeItem('pendingJoinGroupId');
          }
        });
      }
    }
  }, [user, groupId, group]);

  if (!groupId) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-yellow-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Invalid Group Link</h2>
            <p className="text-muted-foreground mb-4">
              The group link you followed is not valid or has expired.
            </p>
            <Button onClick={() => navigate('/user/rotational-savings')} className="w-full">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go to Rotational Savings
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!group) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-yellow-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-blue mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold mb-2">Loading Group Info...</h2>
            <p className="text-muted-foreground">
              Please wait while we fetch the group details.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const isMember = group.current_members?.some((m: any) => m.userId === user?.id || m.id === user?.id);
  const isGroupFull = (group.current_members?.length || 0) >= (group.max_members || 0);
  const canJoin = user && !isMember && !isGroupFull && group.status === 'recruiting';

  const handleJoinClick = () => {
    if (!showConfirmation) {
      setShowConfirmation(true);
    } else {
      joinMutation.mutate({ groupId, userId: user.id }, {
        onSuccess: () => {
          toast.success('Welcome to the group! 🎉');
          localStorage.removeItem('pendingJoinGroupId');
          navigate('/user/rotational-savings');
        },
        onError: (error: any) => {
          toast.error(error?.message || 'Failed to join group');
          setShowConfirmation(false);
        }
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-yellow-50 p-2 sm:p-4">
      <div className="max-w-2xl mx-auto pt-4 sm:pt-8">
        {/* Header */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => navigate('/user/rotational-savings')}
            className="mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Rotational Savings
          </Button>
        </div>

        {/* Main Card */}
        <Card className="shadow-lg">
          <CardHeader className="text-center bg-gradient-to-r from-brand-blue to-blue-600 text-white rounded-t-lg">
            <div className="flex items-center justify-center mb-2">
              <Users className="h-6 w-6 sm:h-8 sm:w-8 mr-2" />
              <CardTitle className="text-xl sm:text-2xl">Join Rotational Group</CardTitle>
            </div>
            <CardDescription className="text-blue-100">
              You've been invited to join a rotational savings group
            </CardDescription>
          </CardHeader>

          <CardContent className="p-6">
            {/* Group Info */}
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">{group.name}</h2>
                {group.description && (
                  <p className="text-muted-foreground">{group.description}</p>
                )}
              </div>

              {/* Group Details Grid */}
              <div className="grid grid-cols-1 xs:grid-cols-2 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <DollarSign className="h-6 w-6 text-brand-blue mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">Contribution Amount</p>
                  <p className="font-semibold text-lg">
                    ₦{
                      (group as any).amountPerInterval === undefined || (group as any).amountPerInterval === null
                        ? 'N/A'
                        : (typeof (group as any).amountPerInterval === 'number' && !isNaN((group as any).amountPerInterval))
                          ? (group as any).amountPerInterval.toLocaleString()
                          : String((group as any).amountPerInterval)
                    }
                  </p>
                </div>

                <div className="bg-yellow-50 p-4 rounded-lg text-center">
                  <Calendar className="h-6 w-6 text-brand-yellow mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">Frequency</p>
                  <p className="font-semibold text-lg capitalize">
                    {(group as any).intervalType || 'N/A'}
                  </p>
                </div>

                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <Users className="h-6 w-6 text-green-600 mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">Members</p>
                  <p className="font-semibold text-lg">
                    {(group as any).members?.length || 0}/{group.max_members || 0}
                  </p>
                </div>

                <div className="bg-purple-50 p-4 rounded-lg text-center">
                  <Clock className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">Status</p>
                  <Badge variant={group.status === 'recruiting' ? 'default' : 'secondary'}>
                    {group.status === 'recruiting' ? 'Open' : 'Closed'}
                  </Badge>
                </div>
              </div>

              {/* Current Members */}
              {(group as any).members && (group as any).members.length > 0 && (
                <div>
                  <h3 className="font-semibold mb-3 flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    Current Members
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {(group as any).members.slice(0, 6).map((member: any, index: number) => (
                      <div key={index} className="flex items-center gap-2 bg-gray-100 rounded-full px-3 py-1">
                        <Avatar className="h-6 w-6">
                          <AvatarFallback className="text-xs">
                            {member.name ? member.name.charAt(0).toUpperCase() : 'M'}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm">{member.name || 'Member'}</span>
                      </div>
                    ))}
                    {(group as any).members.length > 6 && (
                      <div className="flex items-center gap-2 bg-gray-100 rounded-full px-3 py-1">
                        <span className="text-sm">+{(group as any).members.length - 6} more</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* How it Works */}
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <strong>How it works:</strong> Each member contributes the same amount {(group as any).intervalType || 'regularly'}.
                  Every cycle, one member receives the total pot. Everyone gets their turn to receive the full amount.
                </AlertDescription>
              </Alert>

              {/* Action Buttons */}
              <div className="space-y-3">
                {!user ? (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-700">
                      You must be logged in to join this group. Please log in or create an account.
                    </AlertDescription>
                  </Alert>
                ) : isMember ? (
                  <Alert className="border-green-200 bg-green-50">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-700">
                      You're already a member of this group!
                    </AlertDescription>
                  </Alert>
                ) : isGroupFull ? (
                  <Alert className="border-yellow-200 bg-yellow-50">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <AlertDescription className="text-yellow-700">
                      This group is full and no longer accepting new members.
                    </AlertDescription>
                  </Alert>
                ) : group.status !== 'recruiting' ? (
                  <Alert className="border-gray-200 bg-gray-50">
                    <AlertCircle className="h-4 w-4 text-gray-600" />
                    <AlertDescription className="text-gray-700">
                      This group is no longer recruiting new members.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <div className="space-y-3">
                    {!showConfirmation ? (
                      <Button
                        onClick={handleJoinClick}
                        className="w-full bg-brand-blue hover:bg-blue-700 text-white py-3"
                        size="lg"
                      >
                        <UserPlus className="mr-2 h-5 w-5" />
                        Join This Group
                      </Button>
                    ) : (
                      <div className="space-y-3">
                        <Alert className="border-blue-200 bg-blue-50">
                          <Info className="h-4 w-4 text-blue-600" />
                          <AlertDescription className="text-blue-700">
                            <strong>Confirm your participation:</strong> By joining, you commit to contributing
                            ₦{(group as any).amountPerInterval?.toLocaleString()} {(group as any).intervalType || 'regularly'}
                            until everyone has received their payout.
                          </AlertDescription>
                        </Alert>
                        <div className="flex gap-3">
                          <Button
                            variant="outline"
                            onClick={() => setShowConfirmation(false)}
                            className="flex-1"
                          >
                            Cancel
                          </Button>
                          <Button
                            onClick={handleJoinClick}
                            disabled={joinMutation.isPending}
                            className="flex-1 bg-brand-blue hover:bg-blue-700"
                          >
                            {joinMutation.isPending ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                Joining...
                              </>
                            ) : (
                              <>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Confirm & Join
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {(user && !isMember) && (
                  <Button
                    variant="outline"
                    onClick={() => navigate('/user/rotational-savings')}
                    className="w-full"
                  >
                    Browse Other Groups
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
