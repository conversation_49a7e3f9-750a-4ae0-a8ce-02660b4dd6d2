
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'
import { v4 as uuidv4 } from 'https://esm.sh/uuid@9.0.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PaymentRequest {
  userId: string;
  amount: number;
  savingsPlanId?: string;
  paymentMethod: string;
  transactionType: 'deposit' | 'withdrawal' | 'transfer';
  description?: string;
  metadata?: Record<string, any>;
}

interface KojaPayRequest {
  amount: number;
  currency: string;
  reference: string;
  customerEmail: string;
  customerName: string;
  customerPhone?: string;
  paymentMethod: string;
  redirectUrl?: string;
  metadata?: Record<string, any>;
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL') as string
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') as string
    const kojaPayApiKey = Deno.env.get('KOJA_PAY_API_KEY') as string
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Get request data
    const requestData: PaymentRequest = await req.json()
    const { 
      userId, 
      amount, 
      savingsPlanId, 
      paymentMethod, 
      transactionType,
      description,
      metadata
    } = requestData

    console.log('Received payment request:', requestData)

    if (!userId || !amount || amount <= 0 || !paymentMethod || !transactionType) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields or invalid amount' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Get user information for Koja Pay integration
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .select('first_name, last_name, email, phone')
      .eq('id', userId)
      .single()

    if (userError) {
      console.error('Error fetching user data:', userError)
      throw new Error('Could not retrieve user information')
    }

    // Generate unique reference for the transaction
    const reference = `TXN-${uuidv4().substring(0, 8)}-${Date.now()}`

    // Create transaction record
    const { data: transactionData, error: transactionError } = await supabase
      .from('transactions')
      .insert({
        user_id: userId,
        amount,
        savings_plan_id: savingsPlanId || null,
        type: transactionType,
        payment_method: paymentMethod,
        reference,
        description: description || `${transactionType} transaction`,
        status: 'pending',
        metadata
      })
      .select()
      .single()

    if (transactionError) {
      console.error('Error creating transaction:', transactionError)
      throw transactionError
    }

    // Process payment based on payment method
    let paymentResult = null
    let isSuccessful = false

    // Integrate with Koja Pay API if available and payment method requires it
    if (kojaPayApiKey && ['card', 'bank_transfer', 'ussd', 'qr'].includes(paymentMethod)) {
      try {
        console.log('Integrating with Koja Pay API')
        const customerName = `${userData.first_name} ${userData.last_name}`
        
        const kojaPayRequest: KojaPayRequest = {
          amount,
          currency: 'NGN',
          reference,
          customerEmail: userData.email,
          customerName,
          customerPhone: userData.phone,
          paymentMethod,
          metadata: {
            userId,
            transactionId: transactionData.id,
            savingsPlanId: savingsPlanId || null,
            transactionType
          }
        }

        // Call Koja Pay API - Replace with actual API endpoint
        const kojaPayResponse = await fetch('https://api.kojapay.com/v1/transactions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${kojaPayApiKey}`
          },
          body: JSON.stringify(kojaPayRequest)
        })

        if (!kojaPayResponse.ok) {
          const errorData = await kojaPayResponse.json()
          throw new Error(`Koja Pay API error: ${errorData.message || 'Unknown error'}`)
        }

        paymentResult = await kojaPayResponse.json()
        console.log('Koja Pay API response:', paymentResult)
        
        // For this implementation, we'll consider the payment as initiated successfully
        isSuccessful = true
      } catch (kojaPayError) {
        console.error('Koja Pay API error:', kojaPayError)
        throw new Error(`Payment processor error: ${kojaPayError.message}`)
      }
    } else {
      // For non-integrated payment methods or when API key is not available
      // Simulate a successful payment (in a real app, this would be handled differently)
      console.log('Using fallback payment processing')
      isSuccessful = true
    }

    // Update transaction status based on payment result
    const status = isSuccessful ? (transactionType === 'withdrawal' ? 'pending_approval' : 'completed') : 'failed'
    const { data: updatedTransaction, error: updateError } = await supabase
      .from('transactions')
      .update({
        status,
        processed_at: new Date().toISOString(),
        metadata: {
          ...metadata,
          payment_result: paymentResult
        }
      })
      .eq('id', transactionData.id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating transaction:', updateError)
      throw updateError
    }

    // Create notification for the user
    await supabase
      .from('notifications')
      .insert({
        user_id: userId,
        title: `${transactionType.charAt(0).toUpperCase() + transactionType.slice(1)} ${status === 'completed' ? 'Successful' : (status === 'pending_approval' ? 'Pending Approval' : 'Failed')}`,
        message: `Your ${transactionType} of ₦${amount.toLocaleString()} has been ${status === 'completed' ? 'processed successfully' : (status === 'pending_approval' ? 'submitted for approval' : 'failed')}. Reference: ${reference}`,
        type: status === 'completed' ? 'success' : (status === 'pending_approval' ? 'info' : 'error'),
        channel: 'all',
        priority: 'high',
        metadata: {
          transactionId: transactionData.id,
          amount,
          reference
        }
      })

    return new Response(
      JSON.stringify({ 
        success: true, 
        data: updatedTransaction,
        payment_result: paymentResult
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Error processing payment:', error.message)
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
