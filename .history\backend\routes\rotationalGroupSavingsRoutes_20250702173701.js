const express = require('express');
const router = express.Router();
const RotationalGroupSavings = require('../models/rotationalGroupSavings');
const mongoose = require('mongoose');

// Create a new rotational group
router.post('/rgs', async (req, res) => {
  try {
    console.log('[POST /rgs] Payload:', req.body);
    const { name, description, amountPerInterval, intervalType, createdBy, nextPayoutDate } = req.body;
    if (!name || !amountPerInterval || !intervalType || !createdBy || !nextPayoutDate) {
      console.error('[POST /rgs] Missing required fields:', { name, amountPerInterval, intervalType, createdBy, nextPayoutDate });
      return res.status(400).json({ error: 'Missing required fields' });
    }
    const group = new RotationalGroupSavings({
      name,
      description,
      amountPerInterval,
      intervalType,
      createdBy,
      nextPayoutDate,
      members: [{ userId: createdBy }]
    });
    await group.save();
    console.log('[POST /rgs] Group created:', group);
    res.status(201).json(group);
  } catch (err) {
    console.error('[POST /rgs] Error:', err);
    res.status(500).json({ error: err.message });
  }
});

// Join a group
router.post('/:groupId/join', async (req, res) => {
  try {
    console.log('[POST /:groupId/join] Params:', req.params, 'Body:', req.body);
    const { userId } = req.body;
    const { groupId } = req.params;
    if (!groupId || groupId === 'undefined') {
      console.error('[POST /:groupId/join] Invalid groupId:', groupId);
      return res.status(400).json({ error: 'Invalid groupId' });
    }
    if (!userId) {
      console.error('[POST /:groupId/join] Missing userId');
      return res.status(400).json({ error: 'Missing userId' });
    }
    const group = await RotationalGroupSavings.findById(groupId);
    if (!group) {
      console.error('[POST /:groupId/join] Group not found:', groupId);
      return res.status(404).json({ error: 'Group not found' });
    }
    if (group.members.some(m => m.userId.toString() === userId)) {
      console.error('[POST /:groupId/join] Already a member:', userId);
      return res.status(400).json({ error: 'Already a member' });
    }
    group.members.push({ userId });
    await group.save();
    console.log('[POST /:groupId/join] User joined:', userId, 'Group:', groupId);
    res.json(group);
  } catch (err) {
    console.error('[POST /:groupId/join] Error:', err);
    res.status(500).json({ error: err.message });
  }
});

// Make a payment for the current interval
router.post('/:groupId/pay', async (req, res) => {
  try {
    const { userId } = req.body;
    const group = await RotationalGroupSavings.findById(req.params.groupId);
    if (!group) return res.status(404).json({ error: 'Group not found' });
    const member = group.members.find(m => m.userId.toString() === userId);
    if (!member) return res.status(400).json({ error: 'Not a group member' });
    member.hasPaid = true;
    await group.save();
    res.json({ message: 'Payment recorded', group });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// Get group status
router.get('/:groupId/status', async (req, res) => {
  try {
    const group = await RotationalGroupSavings.findById(req.params.groupId).populate('members.userId payouts.userId', 'name email');
    if (!group) return res.status(404).json({ error: 'Group not found' });
    res.json(group);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// List all rotational groups
router.get('/', async (req, res) => {
  try {
    const groups = await RotationalGroupSavings.find().populate('createdBy', 'name email');
    res.json(groups);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// Leave a group
router.post('/:groupId/leave', async (req, res) => {
  try {
    const { userId } = req.body;
    const group = await RotationalGroupSavings.findById(req.params.groupId);
    if (!group) return res.status(404).json({ error: 'Group not found' });
    group.members = group.members.filter(m => m.userId.toString() !== userId);
    await group.save();
    res.json({ message: 'Left group', group });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// Remove group (admin/creator only)
router.delete('/:groupId', async (req, res) => {
  try {
    const { userId } = req.body;
    const group = await RotationalGroupSavings.findById(req.params.groupId);
    if (!group) return res.status(404).json({ error: 'Group not found' });
    if (group.createdBy.toString() !== userId) {
      return res.status(403).json({ error: 'Only creator can delete group' });
    }
    await group.remove();
    res.json({ message: 'Group deleted' });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

module.exports = router;

// --- NEW: Get all rotational group savings for the logged-in user ---
const authMiddleware = require('../middleware/authMiddleware');
router.get('/my', authMiddleware, async (req, res) => {
  try {
    const userId = req.user && req.user.id;
    const groups = await RotationalGroupSavings.find({
      $or: [
        { createdBy: userId },
        { 'members.userId': userId }
      ]
    }).sort({ createdAt: -1 });
    res.status(200).json(groups);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch user rotational group savings', details: error.message });
  }
});
