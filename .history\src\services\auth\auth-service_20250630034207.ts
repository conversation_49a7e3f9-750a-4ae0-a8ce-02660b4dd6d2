import { toast } from 'sonner';

// Auth provider enum
export enum AuthProvider {
  MOCK = 'mock'
}

// Current auth provider - using mock for development
const API_BASE_URL = import.meta.env.VITE_BACKEND_URL;



// Unified auth service with mock implementation
export const AuthService = {
  // Sign in with email and password (real backend)
  signIn: async (email: string, password: string) => {
    try {
      const res = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password })
      });
      const result = await res.json();
      if (!res.ok) {
        return { data: null, error: result.error || 'Failed to sign in' };
      }
      // The backend returns user and token
      return { data: { user: result.user, token: result.token }, error: null };
    } catch (error) {
      console.error('Sign in error:', error);
      return { data: null, error: 'An unexpected error occurred' };
    }
  },

  // Sign up with email and password (real backend)
  signUp: async (email: string, password: string, userData: any) => {
    try {
      const res = await fetch(`${API_BASE_URL}/api/auth/signup`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          password,
          firstName: userData.first_name,
          lastName: userData.last_name,
          phoneNumber: userData.phone
        })
      });
      const result = await res.json();
      if (!res.ok) {
        return { data: null, error: result.error || 'Failed to sign up' };
      }
      // Optionally, fetch user info after signup
      return { data: { user: { email, first_name: userData.first_name, last_name: userData.last_name, phone: userData.phone, role: 'user', isAdmin: false } }, error: null };
    } catch (error) {
      console.error('Sign up error:', error);
      return { data: null, error: 'An unexpected error occurred' };
    }
  },

  // Sign out
  signOut: async () => {
    try {
      return { data: true, error: null };
    } catch (error) {
      console.error('Sign out error:', error);
      return { data: null, error };
    }
  },

  // Reset password
  resetPassword: async (email: string) => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      toast.success('Password reset email sent (mock)');
      return { data: true, error: null };
    } catch (error) {
      console.error('Password reset error:', error);
      return { data: null, error };
    }
  },

  // Update user profile (not implemented)
  updateProfile: async (userId: string, profileData: any) => {
    return { data: null, error: 'Not implemented' };
  },

  // Sign in with phone number (not implemented)
  signInWithPhone: async (phoneNumber: string, recaptchaId: string) => {
    return { data: null, error: 'Not implemented' };
  },

  // Confirm phone sign in with OTP (not implemented)
  confirmPhoneSignIn: async (code: string, confirmationResult: any) => {
    return { data: null, error: 'Not implemented' };
  }
};
