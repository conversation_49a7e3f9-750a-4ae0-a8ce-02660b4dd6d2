
import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Gift, Users, ArrowRight } from 'lucide-react';
import { toast } from 'sonner';

export default function ReferralSignup() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [referralCode, setReferralCode] = useState<string | null>(null);

  useEffect(() => {
    const ref = searchParams.get('ref');
    if (ref) {
      setReferralCode(ref);
      toast.success('🎉 You have a referral bonus waiting!');
    }
  }, [searchParams]);

  const handleSignup = () => {
    // Store referral code in localStorage to be used during signup
    if (referralCode) {
      localStorage.setItem('referral_code', referralCode);
    }
    navigate('/signup');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-brand-blue to-brand-deepBlue flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-brand-yellow rounded-full">
              <Gift className="h-8 w-8 text-brand-blue" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold">Welcome to ASUSU!</CardTitle>
          <CardDescription>
            You've been invited to join the best savings platform
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {referralCode && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Users className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-800">Referral Bonus</span>
                <Badge className="bg-green-500 text-white">₦1,000</Badge>
              </div>
              <p className="text-sm text-green-700">
                You'll receive ₦1,000 bonus when you complete your first deposit!
              </p>
            </div>
          )}
          
          <div className="space-y-4">
            <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
              <div className="p-2 bg-blue-100 rounded-full">
                <span className="text-blue-600 font-bold">1</span>
              </div>
              <div>
                <p className="font-medium">Create Account</p>
                <p className="text-sm text-muted-foreground">Quick and secure signup</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
              <div className="p-2 bg-yellow-100 rounded-full">
                <span className="text-yellow-600 font-bold">2</span>
              </div>
              <div>
                <p className="font-medium">Start Saving</p>
                <p className="text-sm text-muted-foreground">Choose from multiple savings plans</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
              <div className="p-2 bg-green-100 rounded-full">
                <span className="text-green-600 font-bold">3</span>
              </div>
              <div>
                <p className="font-medium">Earn Rewards</p>
                <p className="text-sm text-muted-foreground">Get bonuses and grow your savings</p>
              </div>
            </div>
          </div>
          
          <Button 
            onClick={handleSignup}
            className="w-full bg-brand-blue hover:bg-brand-blue/90 gap-2"
            size="lg"
          >
            Get Started
            <ArrowRight className="h-4 w-4" />
          </Button>
          
          <p className="text-center text-sm text-muted-foreground">
            Already have an account?{' '}
            <Button 
              variant="link" 
              className="p-0 h-auto text-brand-blue"
              onClick={() => navigate('/login')}
            >
              Sign In
            </Button>
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
