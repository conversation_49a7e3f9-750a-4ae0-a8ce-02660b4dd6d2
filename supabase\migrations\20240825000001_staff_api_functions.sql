
-- Staff API functions for accessing staff roles and permissions

-- Function to get all staff members with their roles
CREATE OR REPLACE FUNCTION public.get_staff_members()
RETURNS TABLE (
  id UUID,
  email TEXT,
  first_name TEXT,
  last_name TEXT,
  role_id UUID,
  role_name TEXT,
  permissions TEXT[],
  last_login TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    au.id,
    au.email,
    p.first_name,
    p.last_name,
    sr.id as role_id,
    sr.name as role_name,
    array_agg(DISTINCT rp.permission::TEXT) as permissions,
    au.last_sign_in_at as last_login
  FROM auth.users au
  JOIN public.profiles p ON au.id = p.id
  LEFT JOIN public.staff_assignments sa ON au.id = sa.user_id
  LEFT JOIN public.staff_roles sr ON sa.role_id = sr.id
  LEFT JOIN public.role_permissions rp ON sr.id = rp.role_id
  GROUP BY au.id, p.first_name, p.last_name, sr.id, sr.name, au.last_sign_in_at;
END;
$$;

-- Function to get all roles with their permissions
CREATE OR REPLACE FUNCTION public.get_roles_with_permissions()
RETURNS TABLE (
  id UUID,
  name TEXT,
  description TEXT,
  permissions TEXT[]
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    sr.id,
    sr.name,
    sr.description,
    array_agg(DISTINCT rp.permission::TEXT) as permissions
  FROM public.staff_roles sr
  LEFT JOIN public.role_permissions rp ON sr.id = rp.role_id
  GROUP BY sr.id, sr.name, sr.description;
END;
$$;

-- Function to assign a role to a user
CREATE OR REPLACE FUNCTION public.assign_role_to_user(
  p_user_id UUID,
  p_role_id UUID,
  p_assigned_by UUID
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_assignment_id UUID;
BEGIN
  -- Check if the user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
    RAISE EXCEPTION 'User does not exist';
  END IF;
  
  -- Check if the role exists
  IF NOT EXISTS (SELECT 1 FROM public.staff_roles WHERE id = p_role_id) THEN
    RAISE EXCEPTION 'Role does not exist';
  END IF;
  
  -- Insert or update the assignment
  INSERT INTO public.staff_assignments (user_id, role_id, assigned_by)
  VALUES (p_user_id, p_role_id, p_assigned_by)
  ON CONFLICT (user_id, role_id) 
  DO UPDATE SET assigned_by = p_assigned_by, assigned_at = now()
  RETURNING id INTO v_assignment_id;
  
  RETURN v_assignment_id;
END;
$$;

-- Function to update staff permissions
CREATE OR REPLACE FUNCTION public.update_role_permissions(
  p_role_id UUID,
  p_permissions permission[]
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the role exists
  IF NOT EXISTS (SELECT 1 FROM public.staff_roles WHERE id = p_role_id) THEN
    RAISE EXCEPTION 'Role does not exist';
  END IF;
  
  -- Delete existing permissions
  DELETE FROM public.role_permissions WHERE role_id = p_role_id;
  
  -- Insert new permissions
  IF p_permissions IS NOT NULL AND array_length(p_permissions, 1) > 0 THEN
    INSERT INTO public.role_permissions (role_id, permission)
    SELECT p_role_id, unnest(p_permissions);
  END IF;
  
  RETURN TRUE;
END;
$$;

-- Grant execution permissions
GRANT EXECUTE ON FUNCTION public.get_staff_members TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION public.get_roles_with_permissions TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION public.assign_role_to_user TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION public.update_role_permissions TO authenticated, anon, service_role;
