
import { supabase } from './client';

// Auth related functions
export async function signUp(email: string, password: string, userData: {
  first_name: string;
  last_name: string;
  phone: string;
}) {
  try {
    const response = await supabase.functions.invoke('auth-user', {
      body: {
        action: 'signup',
        email,
        password,
        first_name: userData.first_name,
        last_name: userData.last_name,
        phone: userData.phone
      }
    });

    if (response.error) throw response.error;
    return { data: response.data, error: null };
  } catch (error) {
    console.error('Error signing up:', error);
    return { data: null, error };
  }
}

export async function signIn(email: string, password: string) {
  try {
    const response = await supabase.functions.invoke('auth-user', {
      body: {
        action: 'signin',
        email,
        password
      }
    });

    if (response.error) throw response.error;
    
    // Set the session in the local Supabase client
    if (response.data && response.data.session) {
      await supabase.auth.setSession({
        access_token: response.data.session.access_token,
        refresh_token: response.data.session.refresh_token
      });
    }
    
    return { data: response.data, error: null };
  } catch (error) {
    console.error('Error signing in:', error);
    return { data: null, error };
  }
}

export async function signOut() {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error('Error signing out:', error);
    return { error };
  }
}

export async function resetPassword(email: string) {
  try {
    const response = await supabase.functions.invoke('auth-user', {
      body: {
        action: 'reset-password',
        email
      }
    });
    
    if (response.error) throw response.error;
    return { data: response.data, error: null };
  } catch (error) {
    console.error('Error resetting password:', error);
    return { data: null, error };
  }
}

export async function updatePassword(accessToken: string, newPassword: string) {
  try {
    const response = await supabase.functions.invoke('auth-user', {
      body: {
        action: 'update-password',
        access_token: accessToken,
        new_password: newPassword
      }
    });
    
    if (response.error) throw response.error;
    return { data: response.data, error: null };
  } catch (error) {
    console.error('Error updating password:', error);
    return { data: null, error };
  }
}

export async function getCurrentUser() {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) throw error;
    if (!session) return { user: null, error: null };
    
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', session.user.id)
      .single();
      
    if (profileError && profileError.code !== 'PGRST116') {
      throw profileError;
    }
    
    // Get user role
    const { data: roleData, error: roleError } = await supabase
      .rpc('get_user_role', { user_id: session.user.id });
      
    if (roleError) {
      throw roleError;
    }
    
    // Check if user is admin
    const { data: isAdmin, error: adminError } = await supabase
      .rpc('is_admin', { user_id: session.user.id });
      
    if (adminError) {
      throw adminError;
    }
    
    return { 
      user: { 
        ...session.user, 
        profile, 
        role: roleData,
        isAdmin 
      }, 
      error: null 
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    return { user: null, error };
  }
}

export async function updateProfile(profile: Partial<{
  first_name: string;
  last_name: string;
  phone: string;
  avatar_url: string;
}>) {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user found');

    const { data, error } = await supabase
      .from('profiles')
      .update(profile)
      .eq('id', user.id)
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating profile:', error);
    return { data: null, error };
  }
}

export async function makeAdmin(userId: string) {
  try {
    // Check if current user is admin
    const { data: currentUserData } = await supabase.auth.getUser();
    const { data: isAdmin, error: adminCheckError } = await supabase
      .rpc('is_admin', { user_id: currentUserData.user?.id });
      
    if (adminCheckError) throw adminCheckError;
    if (!isAdmin) throw new Error('Only admins can assign admin roles');

    // Add admin role
    const { data, error } = await supabase
      .from('user_roles')
      .upsert({
        user_id: userId,
        role: 'admin'
      }, { onConflict: 'user_id,role' })
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error making user admin:', error);
    return { data: null, error };
  }
}

export async function removeAdmin(userId: string) {
  try {
    // Check if current user is admin
    const { data: currentUserData } = await supabase.auth.getUser();
    const { data: isAdmin, error: adminCheckError } = await supabase
      .rpc('is_admin', { user_id: currentUserData.user?.id });
      
    if (adminCheckError) throw adminCheckError;
    if (!isAdmin) throw new Error('Only admins can remove admin roles');

    // Remove admin role
    const { data, error } = await supabase
      .from('user_roles')
      .delete()
      .eq('user_id', userId)
      .eq('role', 'admin');

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error removing admin role:', error);
    return { data: null, error };
  }
}

export async function updateUserStatus(userId: string, status: 'active' | 'suspended' | 'blocked') {
  try {
    // Check if current user is admin
    const { data: currentUserData } = await supabase.auth.getUser();
    const { data: isAdmin, error: adminCheckError } = await supabase
      .rpc('is_admin', { user_id: currentUserData.user?.id });
      
    if (adminCheckError) throw adminCheckError;
    if (!isAdmin) throw new Error('Only admins can update user status');

    // Update user status
    const { data, error } = await supabase
      .from('profiles')
      .update({ status })
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating user status:', error);
    return { data: null, error };
  }
}

export async function uploadKycDocument(file: File, documentType: string) {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user found');

    // Create form data
    const formData = new FormData();
    formData.append('file', file);
    formData.append('documentType', documentType);
    formData.append('userId', user.id);

    // Call the Edge Function
    const { data, error } = await supabase.functions.invoke('upload-kyc-document', {
      body: formData,
    });

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error uploading KYC document:', error);
    return { data: null, error };
  }
}

export async function verifyKycDocument(documentId: string, verified: boolean, rejectionReason?: string) {
  try {
    // Check if current user is admin
    const { data: currentUserData } = await supabase.auth.getUser();
    const { data: isAdmin, error: adminCheckError } = await supabase
      .rpc('is_admin', { user_id: currentUserData.user?.id });
      
    if (adminCheckError) throw adminCheckError;
    if (!isAdmin) throw new Error('Only admins can verify KYC documents');

    // Get the document to get user_id
    const { data: document, error: docError } = await supabase
      .from('kyc_documents')
      .select('user_id')
      .eq('id', documentId)
      .single();

    if (docError) throw docError;

    // Update document verification status
    const updateData = verified ? 
      {
        verification_status: 'verified' as const,
        verified_at: new Date().toISOString()
      } : 
      {
        verification_status: 'rejected' as const,
        rejected_reason: rejectionReason || 'Document did not meet verification criteria'
      };

    const { data, error } = await supabase
      .from('kyc_documents')
      .update(updateData)
      .eq('id', documentId)
      .select()
      .single();

    if (error) throw error;

    // Check if user has any other pending documents
    const { data: pendingDocs, error: pendingError } = await supabase
      .from('kyc_documents')
      .select('id')
      .eq('user_id', document.user_id)
      .eq('verification_status', 'pending');
    
    if (pendingError) throw pendingError;

    // If all docs verified and current doc is verified, update user's KYC status
    if (pendingDocs.length === 0 && verified) {
      await supabase
        .from('profiles')
        .update({ kyc_status: 'verified' })
        .eq('id', document.user_id);
    } else if (!verified) {
      // If doc is rejected, update user's KYC status
      await supabase
        .from('profiles')
        .update({ kyc_status: 'rejected' })
        .eq('id', document.user_id);
    }

    // Notify user of verification result
    await supabase.functions.invoke('handle-notification', {
      body: {
        userId: document.user_id,
        title: verified ? 'KYC Verification Approved' : 'KYC Verification Rejected',
        message: verified 
          ? 'Your KYC document has been verified successfully.' 
          : `Your KYC document was rejected: ${rejectionReason || 'Document did not meet verification criteria'}`,
        type: verified ? 'success' : 'error',
        channel: 'all',
        priority: 'high'
      }
    });

    return { data, error: null };
  } catch (error) {
    console.error('Error verifying KYC document:', error);
    return { data: null, error };
  }
}

export async function getPendingKycDocuments() {
  try {
    // Check if current user is admin
    const { data: currentUserData } = await supabase.auth.getUser();
    const { data: isAdmin, error: adminCheckError } = await supabase
      .rpc('is_admin', { user_id: currentUserData.user?.id });
      
    if (adminCheckError) throw adminCheckError;
    if (!isAdmin) throw new Error('Only admins can view pending KYC documents');

    const { data, error } = await supabase
      .from('kyc_documents')
      .select(`
        *,
        profiles:user_id (
          first_name,
          last_name,
          email,
          phone
        )
      `)
      .eq('verification_status', 'pending')
      .order('uploaded_at', { ascending: false });

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error getting pending KYC documents:', error);
    return { data: null, error };
  }
}
