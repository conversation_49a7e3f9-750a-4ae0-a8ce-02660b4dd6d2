
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Switch } from '@/components/ui/switch';
import { CalendarIcon, Target, Users, ArrowLeft } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { useCreateGroupSavingsPlan } from '@/hooks/use-group-savings';
import { CreateGroupSavingsInput } from '@/types/group-savings';

interface CreateGroupSavingsFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export default function CreateGroupSavingsForm({ 
  onSuccess, 
  onCancel 
}: CreateGroupSavingsFormProps) {
  const [formData, setFormData] = useState<CreateGroupSavingsInput>({
    name: '',
    description: '',
    category: '',
    target_amount: 0,
    contribution_amount: 0,
    start_date: new Date().toISOString().split('T')[0],
    end_date: '',
    max_members: 5,
    auto_deduct: false,
    interest_rate: 0
  });
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>();

  const createGroupMutation = useCreateGroupSavingsPlan();

  const categories = [
    { value: 'electronics', label: 'Electronics', icon: '💻' },
    { value: 'phone', label: 'Phone', icon: '📱' },
    { value: 'car', label: 'Car', icon: '🚗' },
    { value: 'grocery', label: 'Grocery', icon: '🛒' },
    { value: 'other', label: 'Other', icon: '💰' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.category || !formData.target_amount || !formData.contribution_amount || !endDate) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (formData.target_amount <= 0 || formData.contribution_amount <= 0) {
      toast.error('Please enter valid amounts');
      return;
    }

    if (formData.max_members < 2) {
      toast.error('Group must have at least 2 members');
      return;
    }

    try {
      const submitData = {
        ...formData,
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate.toISOString().split('T')[0]
      };

      await createGroupMutation.mutateAsync(submitData);
      onSuccess?.();
    } catch (error) {
      console.error('Error creating group savings plan:', error);
    }
  };

  const calculateEstimatedCompletion = () => {
    if (formData.target_amount && formData.contribution_amount && formData.max_members) {
      const totalPerRound = formData.contribution_amount * formData.max_members;
      const roundsNeeded = Math.ceil(formData.target_amount / totalPerRound);
      return roundsNeeded;
    }
    return 0;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={onCancel}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h2 className="text-2xl font-bold">Create Group Savings Plan</h2>
          <p className="text-muted-foreground">Set up a collaborative savings goal with friends</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Group Savings Details
          </CardTitle>
          <CardDescription>
            Create a savings plan that multiple people can contribute to
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Plan Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., iPhone 15 Group Purchase"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        <div className="flex items-center gap-2">
                          <span>{category.icon}</span>
                          <span>{category.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what you're saving for and any important details"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="targetAmount">Target Amount (₦) *</Label>
                <Input
                  id="targetAmount"
                  type="number"
                  value={formData.target_amount || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, target_amount: parseInt(e.target.value) || 0 }))}
                  placeholder="500000"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="contributionAmount">Contribution per Member (₦) *</Label>
                <Input
                  id="contributionAmount"
                  type="number"
                  value={formData.contribution_amount || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, contribution_amount: parseInt(e.target.value) || 0 }))}
                  placeholder="50000"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="maxMembers">Maximum Members *</Label>
                <Input
                  id="maxMembers"
                  type="number"
                  value={formData.max_members}
                  onChange={(e) => setFormData(prev => ({ ...prev, max_members: parseInt(e.target.value) || 5 }))}
                  placeholder="10"
                  min="2"
                  max="50"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="interestRate">Interest Rate (%) - Optional</Label>
                <Input
                  id="interestRate"
                  type="number"
                  step="0.1"
                  value={formData.interest_rate || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, interest_rate: parseFloat(e.target.value) || 0 }))}
                  placeholder="5.0"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label>Start Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDate ? format(startDate, "PPP") : "Pick start date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={startDate}
                      onSelect={setStartDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label>End Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !endDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {endDate ? format(endDate, "PPP") : "Pick end date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={endDate}
                      onSelect={setEndDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="autoDeduct"
                  checked={formData.auto_deduct}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, auto_deduct: checked }))}
                />
                <Label htmlFor="autoDeduct">Enable automatic deductions</Label>
              </div>

              {formData.target_amount && formData.contribution_amount && formData.max_members && (
                <div className="bg-blue-50 p-4 rounded-lg space-y-2">
                  <h4 className="font-medium text-blue-900">Plan Summary:</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Total target: ₦{formData.target_amount.toLocaleString()}</li>
                    <li>• Per member: ₦{formData.contribution_amount.toLocaleString()}</li>
                    <li>• Max members: {formData.max_members}</li>
                    <li>• Total per round: ₦{(formData.contribution_amount * formData.max_members).toLocaleString()}</li>
                    <li>• Estimated rounds: {calculateEstimatedCompletion()}</li>
                  </ul>
                </div>
              )}
            </div>

            <div className="flex gap-3 pt-4">
              <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
                Cancel
              </Button>
              <Button 
                type="submit" 
                className="flex-1"
                disabled={createGroupMutation.isPending}
              >
                {createGroupMutation.isPending ? 'Creating...' : 'Create Group Plan'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
